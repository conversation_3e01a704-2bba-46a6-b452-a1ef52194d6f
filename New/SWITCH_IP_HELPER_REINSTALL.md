# Switch IP Helper-Address Configuration for Server Reinstall

## Overview

I've implemented automatic switch IP helper-address configuration during server PXE reinstall operations. This ensures that when a server is reinstalled, the switch interface is properly configured with the IP helper-address needed for PXE boot and network operations.

## What Was Added

### 1. **PXE Reinstall API Enhancement** (`api_admin_inventory.php`)

Added switch configuration to the `pxe_reinstall` endpoint:

```php
// Configure switch interface with IP helper-address for PXE reinstall
try {
  error_log("Configuring switch interface for PXE reinstall on server $server_id", 3, "auto.logs");
  
  // Get server's main IP for switch configuration
  $server_main_ip = $server_data['main_ip'] ?? null;
  if (!empty($server_main_ip)) {
    // Include the subnet configuration functions
    require_once 'api_admin_subnets.php';
    
    // Configure the switch interface - this will set up IP helper-address
    $switch_config_result = configureServerSwitchInterface($pdo, $server_id, $server_type, $server_main_ip);
    
    if ($switch_config_result['success']) {
      error_log("Switch interface configured successfully for PXE reinstall: " . $switch_config_result['message'], 3, "auto.logs");
    } else {
      error_log("Switch interface configuration failed for PXE reinstall: " . $switch_config_result['error'], 3, "auto.logs");
      // Don't fail the reinstall if switch configuration fails
    }
  } else {
    error_log("No main IP found for server $server_id, skipping switch configuration", 3, "auto.logs");
  }
} catch (Exception $switch_error) {
  error_log("Switch configuration error during PXE reinstall: " . $switch_error->getMessage(), 3, "auto.logs");
  // Don't fail the operation if switch configuration fails
}
```

### 2. **PXE Network Manager Enhancement** (`pxe_api_integration.php`)

Added switch configuration to the `setupCompleteReinstall` method:

```php
// Configure switch interface with IP helper-address for PXE reinstall
$switch_configured = false;
try {
    $this->logOperation("Configuring switch interface for PXE reinstall", [
        'server_id' => $server_id,
        'ip_address' => $network_config['ip_address']
    ]);
    
    // Determine server type from server_data or assume dedicated
    $server_type = $server_data['server_type'] ?? 'dedicated';
    
    // Include the subnet configuration functions
    require_once 'api_admin_subnets.php';
    
    // Configure the switch interface - this will set up IP helper-address
    $switch_config_result = configureServerSwitchInterface($this->pdo, $server_id, $server_type, $network_config['ip_address']);
    
    if ($switch_config_result['success']) {
        $this->logOperation("Switch interface configured successfully for PXE reinstall", [
            'server_id' => $server_id,
            'message' => $switch_config_result['message']
        ]);
        $switch_configured = true;
    } else {
        $this->logOperation("Switch interface configuration failed for PXE reinstall", [
            'server_id' => $server_id,
            'error' => $switch_config_result['error']
        ]);
    }
} catch (Exception $switch_error) {
    $this->logOperation("Switch configuration error during PXE reinstall", [
        'server_id' => $server_id,
        'error' => $switch_error->getMessage()
    ]);
}
```

## How It Works

### 1. **Existing IP Allocation Flow**
When IPs are allocated to servers, the system already:
- Calls `configureServerSwitchInterface()` 
- Fetches IP helper-address from `general_settings` table (key: `ip_helper_address`, value: `*************`)
- Configures switch interface with: `ip helper-address *************`

### 2. **New PXE Reinstall Flow**
Now when servers are reinstalled via PXE, the system:
- Calls the same `configureServerSwitchInterface()` function
- Uses the server's existing main IP address
- Configures the switch interface with the same IP helper-address
- Ensures consistent network configuration

### 3. **Switch Configuration Commands**
The system generates these switch commands during reinstall:
```
enable
configure terminal
interface <port_number>
no shutdown
description <server_label> <timestamp>
no switchport
no ip address
ip address <gateway_ip>/<prefix>
ip helper-address *************
no shutdown
exit
exit
write memory
```

## Configuration Source

The IP helper-address is fetched from the database:
```sql
SELECT setting_value FROM general_settings WHERE setting_key = 'ip_helper_address' LIMIT 1
```

Current value: `*************`

## Error Handling

- Switch configuration failures do NOT stop the PXE reinstall process
- All errors are logged to `auto.logs` for debugging
- If switch configuration fails, the server reinstall continues normally
- Manual switch configuration can be performed later if needed

## Logging

All switch configuration activities during reinstall are logged:
- Configuration attempts
- Success/failure status
- Error messages
- Server and IP details

## Benefits

1. **Consistency**: Same IP helper-address configuration for both IP allocation and reinstall
2. **Automation**: No manual switch configuration needed after reinstall
3. **Reliability**: PXE boot will work immediately after reinstall
4. **Maintainability**: Uses existing, tested switch configuration functions

## Testing

To test the implementation:
1. Initiate a PXE reinstall on a server
2. Check `auto.logs` for switch configuration messages
3. Verify switch interface has correct IP helper-address configured
4. Confirm PXE boot works properly

## Files Modified

- `New/api_admin_inventory.php` - Added switch config to `pxe_reinstall` endpoint
- `New/pxe_api_integration.php` - Added switch config to `setupCompleteReinstall` method
- `New/SWITCH_IP_HELPER_REINSTALL.md` - This documentation file
