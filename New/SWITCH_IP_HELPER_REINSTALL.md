# Switch IP Helper-Address Configuration for Server Reinstall

## Overview

I've implemented automatic switch IP helper-address configuration during server PXE reinstall operations. This ensures that when a server is reinstalled, the switch interface is properly configured with the IP helper-address needed for PXE boot and network operations.

## What Was Added

### 1. **PXE Reinstall API Enhancement** (`api_admin_inventory.php`)

Added switch configuration to the `pxe_reinstall` endpoint using the same approach as subnet assignment:

```php
// Configure switch interface with IP helper-address for PXE reinstall
try {
  error_log("Configuring switch interface for PXE reinstall on server $server_id", 3, "auto.logs");

  // Include the subnet configuration functions
  require_once 'api_admin_subnets.php';

  // Get server information including switch details
  $server_table = $server_type === 'dedicated' ? 'inventory_dedicated_servers' : 'blade_server_inventory';
  $server_info_stmt = $pdo->prepare("SELECT * FROM $server_table WHERE id = :server_id");
  $server_info_stmt->bindValue(':server_id', $server_id);
  $server_info_stmt->execute();
  $server_info = $server_info_stmt->fetch(PDO::FETCH_ASSOC);

  if ($server_info && !empty($server_info['switch_ip']) && !empty($server_info['port1'])) {
    // Get all subnets assigned to this server (like in subnet assignment)
    $all_subnets = [];

    // Add main IP if exists
    if (!empty($server_info['main_ip'])) {
      $all_subnets[] = $server_info['main_ip'];
    }

    // Add additional IPs if exist
    if (!empty($server_info['additional_ips'])) {
      $additional_ips = array_filter(array_map('trim', explode(',', $server_info['additional_ips'])));
      $all_subnets = array_merge($all_subnets, $additional_ips);
    }

    if (!empty($all_subnets)) {
      // Calculate gateway IP from first subnet (like in subnet assignment)
      $primary_subnet = $all_subnets[0];
      $gateway_ip = $primary_subnet;
      if (strpos($primary_subnet, '/') !== false) {
        list($net_ip, $prefix) = explode('/', $primary_subnet);
        $gateway_ip = long2ip(ip2long($net_ip) + 1);
      }

      // Queue switch configuration operation (same as subnet assignment)
      $operation_id = queueSwitchOperation('configure', $server_info['switch_ip'], $server_info['port1'], [
        'password' => $server_info['root_password'],
        'ip_address' => $gateway_ip,
        'subnet_info' => $all_subnets,
        'server_label' => $server_info['label'] ?? null
      ], $pdo);

      if ($operation_id) {
        // Process the queue immediately (like in subnet assignment)
        processSwitchQueue($pdo);
        error_log("Switch interface configured successfully for PXE reinstall", 3, "auto.logs");
      }
    }
  }
} catch (Exception $switch_error) {
  error_log("Switch configuration error during PXE reinstall: " . $switch_error->getMessage(), 3, "auto.logs");
  // Don't fail the operation if switch configuration fails
}
```

### 2. **PXE Network Manager Enhancement** (`pxe_api_integration.php`)

Added switch configuration to the `setupCompleteReinstall` method using the same approach as subnet assignment:

```php
// Configure switch interface with IP helper-address for PXE reinstall
$switch_configured = false;
try {
    $this->logOperation("Configuring switch interface for PXE reinstall", [
        'server_id' => $server_id,
        'ip_address' => $network_config['ip_address']
    ]);

    // Include the subnet configuration functions
    require_once 'api_admin_subnets.php';

    // Determine server type from server_data or assume dedicated
    $server_type = $server_data['server_type'] ?? 'dedicated';

    // Get server information including switch details
    $server_table = $server_type === 'dedicated' ? 'inventory_dedicated_servers' : 'blade_server_inventory';
    $server_info_stmt = $this->pdo->prepare("SELECT * FROM $server_table WHERE id = :server_id");
    $server_info_stmt->bindValue(':server_id', $server_id);
    $server_info_stmt->execute();
    $server_info = $server_info_stmt->fetch(PDO::FETCH_ASSOC);

    if ($server_info && !empty($server_info['switch_ip']) && !empty($server_info['port1'])) {
        // Get all subnets assigned to this server (like in subnet assignment)
        $all_subnets = [];

        // Add main IP if exists
        if (!empty($server_info['main_ip'])) {
            $all_subnets[] = $server_info['main_ip'];
        }

        // Add additional IPs if exist
        if (!empty($server_info['additional_ips'])) {
            $additional_ips = array_filter(array_map('trim', explode(',', $server_info['additional_ips'])));
            $all_subnets = array_merge($all_subnets, $additional_ips);
        }

        if (!empty($all_subnets)) {
            // Calculate gateway IP from first subnet (like in subnet assignment)
            $primary_subnet = $all_subnets[0];
            $gateway_ip = $primary_subnet;
            if (strpos($primary_subnet, '/') !== false) {
                list($net_ip, $prefix) = explode('/', $primary_subnet);
                $gateway_ip = long2ip(ip2long($net_ip) + 1);
            }

            // Queue switch configuration operation (same as subnet assignment)
            $operation_id = queueSwitchOperation('configure', $server_info['switch_ip'], $server_info['port1'], [
                'password' => $server_info['root_password'],
                'ip_address' => $gateway_ip,
                'subnet_info' => $all_subnets,
                'server_label' => $server_info['label'] ?? null
            ], $this->pdo);

            if ($operation_id) {
                // Process the queue immediately (like in subnet assignment)
                processSwitchQueue($this->pdo);
                $switch_configured = true;
            }
        }
    }
} catch (Exception $switch_error) {
    $this->logOperation("Switch configuration error during PXE reinstall", [
        'server_id' => $server_id,
        'error' => $switch_error->getMessage()
    ]);
}
```

## How It Works

### 1. **Existing IP Allocation Flow**
When IPs are allocated to servers, the system already:
- Calls `configureServerSwitchInterface()` 
- Fetches IP helper-address from `general_settings` table (key: `ip_helper_address`, value: `*************`)
- Configures switch interface with: `ip helper-address *************`

### 2. **New PXE Reinstall Flow**
Now when servers are reinstalled via PXE, the system:
- Calls the same `configureServerSwitchInterface()` function
- Uses the server's existing main IP address
- Configures the switch interface with the same IP helper-address
- Ensures consistent network configuration

### 3. **Switch Configuration Commands**
The system generates these switch commands during reinstall:
```
enable
configure terminal
interface <port_number>
no shutdown
description <server_label> <timestamp>
no switchport
no ip address
ip address <gateway_ip>/<prefix>
ip helper-address *************
no shutdown
exit
exit
write memory
```

## Configuration Source

The IP helper-address is fetched from the database:
```sql
SELECT setting_value FROM general_settings WHERE setting_key = 'ip_helper_address' LIMIT 1
```

Current value: `*************`

## Error Handling

- Switch configuration failures do NOT stop the PXE reinstall process
- All errors are logged to `auto.logs` for debugging
- If switch configuration fails, the server reinstall continues normally
- Manual switch configuration can be performed later if needed

## Logging

All switch configuration activities during reinstall are logged:
- Configuration attempts
- Success/failure status
- Error messages
- Server and IP details

## Benefits

1. **Consistency**: Same IP helper-address configuration for both IP allocation and reinstall
2. **Automation**: No manual switch configuration needed after reinstall
3. **Reliability**: PXE boot will work immediately after reinstall
4. **Maintainability**: Uses existing, tested switch configuration functions

## Testing

To test the implementation:
1. Initiate a PXE reinstall on a server
2. Check `auto.logs` for switch configuration messages
3. Verify switch interface has correct IP helper-address configured
4. Confirm PXE boot works properly

## Files Modified

- `New/api_admin_inventory.php` - Added switch config to `pxe_reinstall` endpoint
- `New/pxe_api_integration.php` - Added switch config to `setupCompleteReinstall` method
- `New/SWITCH_IP_HELPER_REINSTALL.md` - This documentation file
