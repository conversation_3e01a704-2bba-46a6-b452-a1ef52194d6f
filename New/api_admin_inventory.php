<?php
require_once("auth_functions.php");






function clearChassisReferences($pdo, $server_id) {
  // Ensure server_id is an integer
  $server_id = intval($server_id);

  // Log the attempt
  error_log("Clearing chassis references for blade server ID: $server_id");

  // Within the delete_blade_server_inventory function, replace the chassis updating section with this:

  // Clear any chassis references using direct SQL that's proven to work
  try {
  // Get the server_id in both string and integer format to be safe
  $server_id_str = (string)$server_id;
  $server_id_int = (int)$server_id;

  // Log the values for debugging
  error_log("Attempting to clear chassis references for blade ID: $server_id");
  error_log("Using string value: '$server_id_str' and integer value: $server_id_int");

  // Use the exact SQL query that worked manually
  $clearChassisSql = "UPDATE inventory_chassis
                      SET bay1 = CASE WHEN bay1 = ? OR bay1 = ? THEN NULL ELSE bay1 END,
                          bay2 = CASE WHEN bay2 = ? OR bay2 = ? THEN NULL ELSE bay2 END,
                          bay3 = CASE WHEN bay3 = ? OR bay3 = ? THEN NULL ELSE bay3 END,
                          bay4 = CASE WHEN bay4 = ? OR bay4 = ? THEN NULL ELSE bay4 END
                      WHERE bay1 = ? OR bay1 = ?
                         OR bay2 = ? OR bay2 = ?
                         OR bay3 = ? OR bay3 = ?
                         OR bay4 = ? OR bay4 = ?";

  $clearChassisSth = $pdo->prepare($clearChassisSql);

  // Bind all parameters - string and int versions for maximum compatibility
  // For the SET clause
  $clearChassisSth->bindValue(1, $server_id_str, PDO::PARAM_STR);
  $clearChassisSth->bindValue(2, $server_id_int, PDO::PARAM_INT);
  $clearChassisSth->bindValue(3, $server_id_str, PDO::PARAM_STR);
  $clearChassisSth->bindValue(4, $server_id_int, PDO::PARAM_INT);
  $clearChassisSth->bindValue(5, $server_id_str, PDO::PARAM_STR);
  $clearChassisSth->bindValue(6, $server_id_int, PDO::PARAM_INT);
  $clearChassisSth->bindValue(7, $server_id_str, PDO::PARAM_STR);
  $clearChassisSth->bindValue(8, $server_id_int, PDO::PARAM_INT);

  // For the WHERE clause
  $clearChassisSth->bindValue(9, $server_id_str, PDO::PARAM_STR);
  $clearChassisSth->bindValue(10, $server_id_int, PDO::PARAM_INT);
  $clearChassisSth->bindValue(11, $server_id_str, PDO::PARAM_STR);
  $clearChassisSth->bindValue(12, $server_id_int, PDO::PARAM_INT);
  $clearChassisSth->bindValue(13, $server_id_str, PDO::PARAM_STR);
  $clearChassisSth->bindValue(14, $server_id_int, PDO::PARAM_INT);
  $clearChassisSth->bindValue(15, $server_id_str, PDO::PARAM_STR);
  $clearChassisSth->bindValue(16, $server_id_int, PDO::PARAM_INT);

  $clearChassisSth->execute();

  $rowsAffected = $clearChassisSth->rowCount();
  error_log("Chassis reference clearing affected $rowsAffected rows");

  // Set chassisUpdated flag for the response
  $chassisUpdated = ($rowsAffected > 0);

  // Verify no chassis still has this blade reference
  $verifySql = "SELECT id, label FROM inventory_chassis
               WHERE bay1 = ? OR bay1 = ?
               OR bay2 = ? OR bay2 = ?
               OR bay3 = ? OR bay3 = ?
               OR bay4 = ? OR bay4 = ?";
  $verifySth = $pdo->prepare($verifySql);
  $verifySth->bindValue(1, $server_id_str, PDO::PARAM_STR);
  $verifySth->bindValue(2, $server_id_int, PDO::PARAM_INT);
  $verifySth->bindValue(3, $server_id_str, PDO::PARAM_STR);
  $verifySth->bindValue(4, $server_id_int, PDO::PARAM_INT);
  $verifySth->bindValue(5, $server_id_str, PDO::PARAM_STR);
  $verifySth->bindValue(6, $server_id_int, PDO::PARAM_INT);
  $verifySth->bindValue(7, $server_id_str, PDO::PARAM_STR);
  $verifySth->bindValue(8, $server_id_int, PDO::PARAM_INT);
  $verifySth->execute();

  $remainingChassisCount = $verifySth->rowCount();
  if ($remainingChassisCount > 0) {
      $remainingChassis = $verifySth->fetchAll(PDO::FETCH_ASSOC);
      error_log("WARNING: " . $remainingChassisCount . " chassis still reference blade $server_id after deletion attempt");
      error_log("Remaining chassis: " . json_encode($remainingChassis));
  } else {
      error_log("Verification successful: No chassis still reference blade $server_id");
  }

  } catch (Exception $e) {
    error_log("Error clearing chassis references: " . $e->getMessage());
    // Don't throw - we still want to delete the blade server even if this fails
    $chassisUpdated = false;
  }
  }




  function collectSwitchPortTraffic() {
  global $pdo;

  // Get all switches with SNMP credentials
  $switches = $pdo->query("SELECT id, label, switch_ip, snmp_community, snmp_version FROM inventory_switches
                          WHERE switch_ip IS NOT NULL AND snmp_community IS NOT NULL")->fetchAll(PDO::FETCH_ASSOC);

  $stats = [
      'switches_processed' => 0,
      'switches_failed' => 0,
      'ports_processed' => 0,
      'ports_with_data' => 0
  ];

  // Calculate a timestamp that rounds to the current minute with 00 seconds
  $currentTime = time();
  $roundedTimestamp = date('Y-m-d H:i:00', $currentTime);

  foreach ($switches as $switch) {
      // Get all ports for this switch
      $ports = $pdo->prepare("SELECT id, port_number, port_name FROM inventory_switch_ports WHERE switch_id = :switch_id");
      $ports->bindValue(':switch_id', $switch['id']);
      $ports->execute();
      $ports = $ports->fetchAll(PDO::FETCH_ASSOC);

      // Skip if no ports found
      if (empty($ports)) {
          continue;
      }

      // Set up SNMP session
      $version = ($switch['snmp_version'] == 1) ? SNMP::VERSION_1 :
                (($switch['snmp_version'] == 3) ? SNMP::VERSION_3 : SNMP::VERSION_2C);

      try {
          // Set reasonable timeout options
          $timeout = 1000000; // 1 second
          $retries = 3;

          $session = new SNMP($version, $switch['switch_ip'], $switch['snmp_community'], $timeout, $retries);
          $session->valueretrieval = SNMP_VALUE_PLAIN;
          $session->quick_print = true;

          // Test SNMP connectivity first
          try {
              $sysName = $session->get('.*******.*******.0'); // sysName
              if (!$sysName) {
                  throw new Exception("Could not get system name from switch");
              }
          } catch (Exception $e) {
              throw new Exception("SNMP connectivity test failed: " . $e->getMessage());
          }

          // Get traffic data for all ports using different methods

          // 1. Get all interface indices
          $ifIndices = [];
          try {
              $indices = $session->walk('.*******.*******.1.1'); // ifIndex
              foreach ($indices as $oid => $value) {
                  $parts = explode('.', $oid);
                  $index = end($parts);
                  $ifIndices[$index] = trim($value, '"');
              }
          } catch (Exception $e) {
              throw new Exception("Failed to get interface indices: " . $e->getMessage());
          }

          // 2. Get interface names/descriptions for mapping
          $ifDescriptions = [];
          $ifNames = [];

          try {
              // Standard MIB-II descriptions
              $descriptions = $session->walk('.*******.*******.1.2'); // ifDescr
              foreach ($descriptions as $oid => $value) {
                  $parts = explode('.', $oid);
                  $index = end($parts);
                  $ifDescriptions[$index] = trim($value, '"');
              }
          } catch (Exception $e) {
              // Non-critical, continue
          }

          try {
              // IF-MIB names (better for newer devices)
              $names = $session->walk('.*******.********.1.1.1'); // ifName
              foreach ($names as $oid => $value) {
                  $parts = explode('.', $oid);
                  $index = end($parts);
                  $ifNames[$index] = trim($value, '"');
              }
          } catch (Exception $e) {
              // Non-critical, continue
          }

          // 3. Get counters - try different OIDs

          // First try 64-bit (high capacity) counters
          $inOctets64 = [];
          $outOctets64 = [];
          $inPkts64 = [];
          $outPkts64 = [];

          try {
              $in64 = $session->walk('.*******.********.1.1.6'); // ifHCInOctets
              foreach ($in64 as $oid => $value) {
                  $parts = explode('.', $oid);
                  $index = end($parts);
                  $inOctets64[$index] = trim($value, '"');
              }
          } catch (Exception $e) {
              // Non-critical, will fall back to 32-bit counters
          }

          try {
              $out64 = $session->walk('.*******.********.1.1.10'); // ifHCOutOctets
              foreach ($out64 as $oid => $value) {
                  $parts = explode('.', $oid);
                  $index = end($parts);
                  $outOctets64[$index] = trim($value, '"');
              }
          } catch (Exception $e) {
              // Non-critical, will fall back to 32-bit counters
          }

          try {
              $inPkts = $session->walk('.*******.********.1.1.7'); // ifHCInUcastPkts
              foreach ($inPkts as $oid => $value) {
                  $parts = explode('.', $oid);
                  $index = end($parts);
                  $inPkts64[$index] = trim($value, '"');
              }
          } catch (Exception $e) {
              // Non-critical
          }

          try {
              $outPkts = $session->walk('.*******.********.1.1.11'); // ifHCOutUcastPkts
              foreach ($outPkts as $oid => $value) {
                  $parts = explode('.', $oid);
                  $index = end($parts);
                  $outPkts64[$index] = trim($value, '"');
              }
          } catch (Exception $e) {
              // Non-critical
          }

          // Then fallback to 32-bit counters if needed
          $inOctets32 = [];
          $outOctets32 = [];

          if (empty($inOctets64) || empty($outOctets64)) {
              try {
                  $in32 = $session->walk('.*******.*******.1.10'); // ifInOctets
                  foreach ($in32 as $oid => $value) {
                      $parts = explode('.', $oid);
                      $index = end($parts);
                      $inOctets32[$index] = trim($value, '"');
                  }
              } catch (Exception $e) {
                  // This is more critical since we need at least one counter type
                  if (empty($inOctets64)) {
                      throw new Exception("Failed to get input octets from either 32-bit or 64-bit counters");
                  }
              }

              try {
                  $out32 = $session->walk('.*******.*******.1.16'); // ifOutOctets
                  foreach ($out32 as $oid => $value) {
                      $parts = explode('.', $oid);
                      $index = end($parts);
                      $outOctets32[$index] = trim($value, '"');
                  }
              } catch (Exception $e) {
                  // This is more critical since we need at least one counter type
                  if (empty($outOctets64)) {
                      throw new Exception("Failed to get output octets from either 32-bit or 64-bit counters");
                  }
              }
          }

          // Get error counters
          $inErrors = [];
          $outErrors = [];

          try {
              $inErrs = $session->walk('.*******.*******.1.14'); // ifInErrors
              foreach ($inErrs as $oid => $value) {
                  $parts = explode('.', $oid);
                  $index = end($parts);
                  $inErrors[$index] = trim($value, '"');
              }
          } catch (Exception $e) {
              // Non-critical
          }

          try {
              $outErrs = $session->walk('.*******.*******.1.20'); // ifOutErrors
              foreach ($outErrs as $oid => $value) {
                  $parts = explode('.', $oid);
                  $index = end($parts);
                  $outErrors[$index] = trim($value, '"');
              }
          } catch (Exception $e) {
              // Non-critical
          }

          // Now process each port
          foreach ($ports as $port) {
              $stats['ports_processed']++;

              // Map port to SNMP index using multiple methods
              $portIndex = null;
              $portNumber = $port['port_number'];
              $portName = $port['port_name'];

              // Method 1: Try exact match on ifName
              foreach ($ifNames as $index => $name) {
                  if ($name === $portNumber || $name === $portName) {
                      $portIndex = $index;
                      break;
                  }
              }

              // Method 2: Try substring match
              if (!$portIndex) {
                  foreach ($ifNames as $index => $name) {
                      if (strpos($name, $portNumber) !== false) {
                          $portIndex = $index;
                          break;
                      }
                  }
              }

              // Method 3: Try with ifDescr
              if (!$portIndex) {
                  foreach ($ifDescriptions as $index => $descr) {
                      if ($descr === $portNumber || $descr === $portName ||
                          strpos($descr, $portNumber) !== false) {
                          $portIndex = $index;
                          break;
                      }
                  }
              }

              // Method 4: Try number extraction
              if (!$portIndex && preg_match('/(\d+)/', $portNumber, $matches)) {
                  $numericPart = $matches[1];

                  // Check if this number exists directly as an index
                  if (isset($ifIndices[$numericPart])) {
                      $portIndex = $numericPart;
                  } else {
                      // Otherwise look for it in port names/descriptions
                      foreach ($ifNames as $index => $name) {
                          if (preg_match('/\b' . $numericPart . '\b/', $name)) {
                              $portIndex = $index;
                              break;
                          }
                      }

                      if (!$portIndex) {
                          foreach ($ifDescriptions as $index => $descr) {
                              if (preg_match('/\b' . $numericPart . '\b/', $descr)) {
                                  $portIndex = $index;
                                  break;
                              }
                          }
                      }
                  }
              }

              // If we found a mapping, get the data
              if ($portIndex) {
                  // Get the byte counters, preferring 64-bit where available
                  $inBytes = isset($inOctets64[$portIndex]) ? $inOctets64[$portIndex] :
                           (isset($inOctets32[$portIndex]) ? $inOctets32[$portIndex] : 0);

                  $outBytes = isset($outOctets64[$portIndex]) ? $outOctets64[$portIndex] :
                            (isset($outOctets32[$portIndex]) ? $outOctets32[$portIndex] : 0);

                  $inPkts = isset($inPkts64[$portIndex]) ? $inPkts64[$portIndex] : 0;
                  $outPkts = isset($outPkts64[$portIndex]) ? $outPkts64[$portIndex] : 0;

                  $inErrs = isset($inErrors[$portIndex]) ? $inErrors[$portIndex] : 0;
                  $outErrs = isset($outErrors[$portIndex]) ? $outErrors[$portIndex] : 0;

                  // Make sure we have actual numeric values
                  $inBytes = is_numeric($inBytes) ? $inBytes : 0;
                  $outBytes = is_numeric($outBytes) ? $outBytes : 0;
                  $inPkts = is_numeric($inPkts) ? $inPkts : 0;
                  $outPkts = is_numeric($outPkts) ? $outPkts : 0;
                  $inErrs = is_numeric($inErrs) ? $inErrs : 0;
                  $outErrs = is_numeric($outErrs) ? $outErrs : 0;

                  // Only store if we have some non-zero values - we don't want to store useless data
                  if ($inBytes > 0 || $outBytes > 0) {
                      // Store data in database with the rounded timestamp (00 seconds)
                      $stmt = $pdo->prepare("INSERT INTO switch_port_traffic
                                            (port_id, timestamp, in_bytes, out_bytes, in_packets, out_packets, in_errors, out_errors)
                                            VALUES (:port_id, :timestamp, :in_bytes, :out_bytes, :in_packets, :out_packets, :in_errors, :out_errors)");
                      $stmt->bindValue(':port_id', $port['id']);
                      $stmt->bindValue(':timestamp', $roundedTimestamp);
                      $stmt->bindValue(':in_bytes', $inBytes);
                      $stmt->bindValue(':out_bytes', $outBytes);
                      $stmt->bindValue(':in_packets', $inPkts);
                      $stmt->bindValue(':out_packets', $outPkts);
                      $stmt->bindValue(':in_errors', $inErrs);
                      $stmt->bindValue(':out_errors', $outErrs);
                      $stmt->execute();

                      $stats['ports_with_data']++;
                  }
              }
          }

          $session->close();
          $stats['switches_processed']++;
      } catch (Exception $e) {
          $stats['switches_failed']++;
          error_log("SNMP error for switch {$switch['label']} (ID: {$switch['id']}): " . $e->getMessage());
          continue;
      }
  }

  // Clean up old data (keep 30 days)
  $cleanupDate = date('Y-m-d H:i:s', strtotime('-30 days'));
  $cleanup = $pdo->prepare("DELETE FROM switch_port_traffic WHERE timestamp < :cleanup_date");
  $cleanup->bindValue(':cleanup_date', $cleanupDate);
  $cleanup->execute();

  // Log a message about the execution
  error_log("Switch port traffic collected at {$roundedTimestamp}. Stats: " .
            json_encode($stats));

  return $stats;
}


function refreshPortStatus($switchId, $portId = null) {
  global $pdo;

  // Validate parameters
  if (empty($switchId)) {
    throw new Exception('Switch ID is required');
  }

  // Get switch information from database
  $switchQuery = $pdo->prepare("SELECT label, switch_ip, snmp_community, snmp_version FROM inventory_switches WHERE id = :id");
  $switchQuery->bindValue(':id', $switchId, PDO::PARAM_INT);
  $switchQuery->execute();

  if ($switchQuery->rowCount() === 0) {
    throw new Exception("Switch with ID $switchId not found");
  }

  $switchInfo = $switchQuery->fetch(PDO::FETCH_ASSOC);
  $switchIP = $switchInfo['switch_ip'];

  // Check if we have SNMP credentials
  if (empty($switchIP)) {
    throw new Exception("Switch IP address is not set for switch ID $switchId");
  }

  $snmpCommunity = $switchInfo['snmp_community'] ?? 'public';
  $snmpVersion = $switchInfo['snmp_version'] ?? 2;

  // Set SNMP version
  switch($snmpVersion) {
    case 1:
      $version = SNMP::VERSION_1;
      break;
    case 3:
      $version = SNMP::VERSION_3;
      break;
    default:
      $version = SNMP::VERSION_2C;
  }

  // Get the port(s) to refresh
  if ($portId) {
    // Get a specific port
    $portQuery = $pdo->prepare("SELECT id, port_number, port_name, status FROM inventory_switch_ports WHERE id = :id AND switch_id = :switch_id");
    $portQuery->bindValue(':id', $portId, PDO::PARAM_INT);
    $portQuery->bindValue(':switch_id', $switchId, PDO::PARAM_INT);
    $portQuery->execute();

    if ($portQuery->rowCount() === 0) {
      throw new Exception("Port with ID $portId not found on switch $switchId");
    }

    $ports = [$portQuery->fetch(PDO::FETCH_ASSOC)];
  } else {
    // Get all ports for the switch
    $portsQuery = $pdo->prepare("SELECT id, port_number, port_name, status FROM inventory_switch_ports WHERE switch_id = :switch_id");
    $portsQuery->bindValue(':switch_id', $switchId, PDO::PARAM_INT);
    $portsQuery->execute();
    $ports = $portsQuery->fetchAll(PDO::FETCH_ASSOC);
  }

  if (empty($ports)) {
    throw new Exception("No ports found for switch ID $switchId");
  }

  // Create a mapping of port number to port details for faster lookups
  $portMap = [];
  foreach ($ports as $port) {
    // Extract numeric part if port number contains letters
    $portNumStr = $port['port_number'];
    if (preg_match('/(\d+)/', $portNumStr, $matches)) {
      $portNum = $matches[1];
      $portMap[$portNum] = $port;
    }
  }

  // Create SNMP session
  try {
    // Set timeouts to avoid hanging
    $timeout = 500000; // 0.5 seconds
    $retries = 1;

    $session = new SNMP($version, $switchIP, $snmpCommunity, $timeout, $retries);
    $session->valueretrieval = SNMP_VALUE_PLAIN;
    $session->quick_print = true;
    $session->enum_print = true;

    // OIDs for port status
    $ifOperStatusOID = ".*******.*******.1.8"; // ifOperStatus (1=up, 2=down)
    $ifAdminStatusOID = ".*******.*******.1.7"; // ifAdminStatus (1=up, 2=down)

    // Try to get operational and admin status for all interfaces
    $operStatus = [];
    $adminStatus = [];

    try {
      // Walk ifOperStatus to get operational status for all interfaces
      $operStatusResponse = $session->walk($ifOperStatusOID);
      foreach ($operStatusResponse as $oid => $value) {
        // Extract index from OID
        $parts = explode(".", $oid);
        $index = end($parts);
        $operStatus[$index] = trim($value, '"');
      }

      // Walk ifAdminStatus to get admin status for all interfaces
      $adminStatusResponse = $session->walk($ifAdminStatusOID);
      foreach ($adminStatusResponse as $oid => $value) {
        // Extract index from OID
        $parts = explode(".", $oid);
        $index = end($parts);
        $adminStatus[$index] = trim($value, '"');
      }
    } catch (Exception $e) {
      throw new Exception("SNMP Error: " . $e->getMessage());
    }

    // Map the SNMP status values to our status values
    $statusMapping = [
      'operStatus' => [
        '1' => 'Up',
        '2' => 'Down',
      ],
      'adminStatus' => [
        '1' => 'Up',
        '2' => 'Down',
      ]
    ];

    // Update each port with its current status
    $updatedPorts = [];
    foreach ($portMap as $portNum => $port) {
      $opStatus = isset($operStatus[$portNum]) ?
                ($statusMapping['operStatus'][$operStatus[$portNum]] ?? 'Unknown') :
                'Unknown';

      $admStatus = isset($adminStatus[$portNum]) ?
                 ($statusMapping['adminStatus'][$adminStatus[$portNum]] ?? 'Unknown') :
                 'Unknown';

      // Determine the overall status based on admin and operational status
      $newStatus = 'Unknown';

      if ($admStatus === 'Down') {
        $newStatus = 'Disabled'; // Administratively down
      } else if ($opStatus === 'Down') {
        $newStatus = 'Down'; // Link down
      } else if ($port['status'] === 'Used') {
        $newStatus = 'Used'; // Keep the 'Used' status if that was its previous status
      } else if ($opStatus === 'Up' && $admStatus === 'Up') {
        $newStatus = 'Available'; // Link up and admin up
      }

      // Only update if status changed
      if ($newStatus !== $port['status']) {
        // Update in database
        $updateQuery = $pdo->prepare("UPDATE inventory_switch_ports SET status = :status WHERE id = :id");
        $updateQuery->bindValue(':status', $newStatus);
        $updateQuery->bindValue(':id', $port['id']);
        $updateQuery->execute();

        // Update local copy
        $port['status'] = $newStatus;
      }

      // Add operational data for the response
      $port['operational_status'] = $opStatus;
      $port['admin_status'] = $admStatus;

      $updatedPorts[] = $port;
    }

    // Close the SNMP session
    $session->close();

    return [
      'success' => true,
      'switch_id' => $switchId,
      'switch_label' => $switchInfo['label'],
      'ports' => $updatedPorts,
      'timestamp' => date('Y-m-d H:i:s')
    ];

  } catch (SNMPException $e) {
    throw new Exception("SNMP Error: " . $e->getMessage());
  } catch (Exception $e) {
    throw new Exception("Error refreshing port status: " . $e->getMessage());
  }
}

// New function to manually update port speed
function updatePortSpeed($port_id, $speed) {
  global $pdo;

  // Validate parameters
  if (empty($port_id)) {
    throw new Exception('Port ID is required');
  }

  // Convert speed to integer or NULL
  $speed_mbps = is_numeric($speed) ? intval($speed) : null;

  // Check if port exists
  $checkPort = $pdo->prepare("SELECT id FROM inventory_switch_ports WHERE id = :id");
  $checkPort->bindValue(':id', $port_id, PDO::PARAM_INT);
  $checkPort->execute();

  if ($checkPort->rowCount() === 0) {
    throw new Exception("Port with ID $port_id not found");
  }

  // Update the port speed
  $updatePort = $pdo->prepare("UPDATE inventory_switch_ports SET max_speed = :max_speed WHERE id = :id");
  $updatePort->bindValue(':id', $port_id, PDO::PARAM_INT);

  if ($speed_mbps === null) {
    $updatePort->bindValue(':max_speed', null, PDO::PARAM_NULL);
  } else {
    $updatePort->bindValue(':max_speed', $speed_mbps, PDO::PARAM_INT);
  }

  $updatePort->execute();

  return [
    'success' => true,
    'port_id' => $port_id,
    'max_speed' => $speed_mbps
  ];
}



// Helper function to process hardware inventory output
function process_hwinventory_output($output) {
  $processed_output = [];
  $currentSection = null;
  $currentInstance = null;
  $instanceData = [];

  // Precompile regular expressions for better performance
  $sectionPattern = '/^-+\s*(.+?)\s*-+$/';
  $instancePattern = '/^([^:]+)$/';
  $keyValuePattern = '/^([^:=]+)[=:](.*)$/';

  foreach ($output as $line) {
    $line = trim($line);

    // Skip empty lines
    if (empty($line)) continue;

    // Detect section headers
    if (preg_match($sectionPattern, $line, $matches)) {
      if ($currentSection && $currentInstance && !empty($instanceData)) {
        $processed_output[$currentSection][] = $instanceData;
        $instanceData = [];
      }

      $currentSection = trim($matches[1]);
      $currentInstance = null;
      continue;
    }

    // Detect instance headers
    if (preg_match($instancePattern, $line) && !strpos($line, '=')) {
      if ($currentInstance && !empty($instanceData)) {
        if (!isset($processed_output[$currentSection])) {
          $processed_output[$currentSection] = [];
        }
        $processed_output[$currentSection][] = $instanceData;
        $instanceData = [];
      }

      $currentInstance = trim($line);
      continue;
    }

    // Process key-value pairs
    if (preg_match($keyValuePattern, $line, $matches)) {
      $key = trim($matches[1]);
      $value = trim($matches[2]);
      $instanceData[$key] = $value;
    }
  }

  // Add the last instance if we have one
  if ($currentSection && $currentInstance && !empty($instanceData)) {
    if (!isset($processed_output[$currentSection])) {
      $processed_output[$currentSection] = [];
    }
    $processed_output[$currentSection][] = $instanceData;
  }

  return $processed_output;
}


// Helper function to process sensor info output
function process_sensorinfo_output($output) {
  $processed_output = [
    'fans' => [],
    'temperatures' => [],
    'power' => [],
    'voltages' => [],  // Add this to match frontend expectations
    'others' => []     // Add this to match frontend expectations
  ];
  try {
    // Precompile patterns
    $fanPattern = '/Fan\s*(\d+)/i';
    $tempPattern = '/(CPU|Inlet|Exhaust|System|Ambient).*Temp/i';
    $powerPattern = '/(System|Board).*Power/i';
    $voltagePattern = '/(Volt|VRM|PSU)/i';

    foreach ($output as $line) {
      $line = trim($line);

      // Skip empty lines
      if (empty($line)) continue;

      // Split by | if present (typical sensor output format)
      $parts = preg_split('/\s*\|\s*/', $line);

      // If we don't have at least 3 parts, try splitting by whitespace
      if (count($parts) < 3) {
        $parts = preg_split('/\s{2,}/', $line);
        $parts = array_values(array_filter(array_map('trim', $parts)));
      }

      if (count($parts) >= 3) {
        $sensorName = trim($parts[0]);
        $reading = trim($parts[1]);
        $status = trim($parts[2]);

        // Create a default sensor entry
        $sensor = [
          'name' => $sensorName,
          'reading' => $reading,
          'status' => $status
        ];

        // Categorize based on name
        if (preg_match($fanPattern, $sensorName)) {
          $processed_output['fans'][] = $sensor;
        }
        else if (preg_match($tempPattern, $sensorName)) {
          $processed_output['temperatures'][] = $sensor;
        }
        else if (preg_match($powerPattern, $sensorName)) {
          $processed_output['power'][] = $sensor;
        }
        else if (preg_match($voltagePattern, $sensorName)) {
          $processed_output['voltages'][] = $sensor;
        }
        else {
          $processed_output['others'][] = $sensor;
        }
      }
    }
  } catch (Exception $e) {
    error_log("Error processing sensor data: " . $e->getMessage());
    // Return the raw output as a fallback
    $processed_output['raw'] = $output;
  }

  return $processed_output;
}





// Unassign port from server
if($_GET['f'] == 'unassign_port_from_server'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log the incoming request for debugging
    error_log("Unassign port request: " . json_encode($data));

    // Validate required fields
    if (!isset($data['device_id']) || empty($data['device_id'])) {
      throw new Exception('Device ID is required');
    }
    if (!isset($data['device_type']) || empty($data['device_type'])) {
      throw new Exception('Device type is required');
    }
    if (!isset($data['port_number']) || empty($data['port_number'])) {
      throw new Exception('Port number is required');
    }

    $device_id = intval($data['device_id']);
    $device_type = $data['device_type'];
    $port_number = $data['port_number'];

    // Determine server table based on device type
    if ($device_type === 'blade') {
      $serverTable = 'blade_server_inventory';
    } else if ($device_type === 'dedicated') {
      $serverTable = 'inventory_dedicated_servers';
    } else {
      throw new Exception('Unsupported device type: ' . $device_type);
    }

    // Begin transaction
    $pdo->beginTransaction();

    // Step 1: First find all switch ports that are assigned to this device
    $getAssignedPortsSql = "SELECT * FROM inventory_switch_ports 
                           WHERE connected_device_id = :device_id 
                           AND connected_device_type = :device_type";
    $getAssignedPortsSth = $pdo->prepare($getAssignedPortsSql);
    $getAssignedPortsSth->bindValue(':device_id', $device_id);
    $getAssignedPortsSth->bindValue(':device_type', $device_type);
    $getAssignedPortsSth->execute();
    $assignedPorts = $getAssignedPortsSth->fetchAll(PDO::FETCH_ASSOC);

    error_log("Found assigned ports: " . json_encode($assignedPorts));

    // Step 2: Find the specific port we want to unassign
    $targetPort = null;
    foreach ($assignedPorts as $assignedPort) {
      if ($assignedPort['port_number'] == $port_number) {
        $targetPort = $assignedPort;
        break;
      }
    }

    if (!$targetPort) {
      error_log("Could not find target port with number: " . $port_number);
      // Try to find it without the connected_device_id filter - might be data inconsistency
      $getPortSql = "SELECT * FROM inventory_switch_ports 
                     WHERE port_number = :port_number";
      $getPortSth = $pdo->prepare($getPortSql);
      $getPortSth->bindValue(':port_number', $port_number);
      $getPortSth->execute();
      $allMatchingPorts = $getPortSth->fetchAll(PDO::FETCH_ASSOC);
      error_log("All ports with this number: " . json_encode($allMatchingPorts));
      
      $pdo->rollBack();
      throw new Exception('Port not found in switch ports table. Port number: ' . $port_number);
    }

    // Step 3: Get current server info to identify which port fields need to be updated
    $getServerSql = "SELECT * FROM $serverTable WHERE id = :device_id";
    $getServerSth = $pdo->prepare($getServerSql);
    $getServerSth->bindValue(':device_id', $device_id);
    $getServerSth->execute();

    $server = $getServerSth->fetch(PDO::FETCH_ASSOC);

    if (!$server) {
      error_log("Server not found in table $serverTable with ID: " . $device_id);
      $pdo->rollBack();
      throw new Exception('Server not found');
    }

    error_log("Found server: " . json_encode($server));

    // Step 4: Force clear all port assignments for this device - no validation checks
    error_log("Force clearing all port assignments for device $device_id");

    // Step 5: Update the server record to clear ALL port fields that might match
    // The server table now stores PORT IDs for proper foreign key relationships
    $targetPortId = $targetPort['id']; // Use the port ID from the target port
    
    error_log("Looking for port ID: $targetPortId in server port fields");

    $updateServerSql = "UPDATE $serverTable SET ";
    $updateFields = [];
    
    // Check each port field and clear it if it matches our target port ID
    foreach(['port1', 'port2', 'port3', 'port4'] as $portField) {
      $speedField = $portField . '_speed';
      $serverPortValue = $server[$portField] ?? null;
      
      // Match against port ID or empty cleanup
      $shouldClear = false;
      
      if (empty($serverPortValue)) {
        // Clear empty/null fields for cleanup
        $shouldClear = true;
        error_log("Clearing empty field $portField");
      } elseif ($serverPortValue == $targetPortId) {
        // Port ID match
        $shouldClear = true;
        error_log("Port ID match: $portField = $serverPortValue matches target port ID $targetPortId");
      }
      
      if ($shouldClear) {
        $updateFields[] = "$portField = NULL";
        if (isset($server[$speedField])) {
          $updateFields[] = "$speedField = NULL";
        }
        error_log("Clearing $portField (was: " . ($serverPortValue ?? 'null') . ")");
      }
    }

    // If no specific fields were identified, force clear all to ensure cleanup
    if (empty($updateFields)) {
      error_log("No matching fields found, force clearing ALL port fields for cleanup");
      $updateFields = [
        "port1 = NULL", "port1_speed = NULL",
        "port2 = NULL", "port2_speed = NULL", 
        "port3 = NULL", "port3_speed = NULL",
        "port4 = NULL", "port4_speed = NULL",
        "switch_id = NULL"
      ];
    } else {
      // Check if we should also clear switch_id (if no other ports remain)
      $remainingPorts = false;
      foreach(['port1', 'port2', 'port3', 'port4'] as $checkPort) {
        $checkValue = $server[$checkPort] ?? null;
        
        // Skip if this field will be cleared
        $willBeCleared = in_array("$checkPort = NULL", $updateFields);
        
        if (!$willBeCleared && !empty($checkValue)) {
          $remainingPorts = true;
          break;
        }
      }
      
      if (!$remainingPorts) {
        $updateFields[] = "switch_id = NULL";
        error_log("Also clearing switch_id as no other ports remain");
      }
    }

    $updateServerSql .= implode(', ', $updateFields);
    $updateServerSql .= " WHERE id = :device_id";
    
    error_log("Server update SQL: $updateServerSql");
    
    $updateServerSth = $pdo->prepare($updateServerSql);
    $updateServerSth->bindValue(':device_id', $device_id);
    $updateServerResult = $updateServerSth->execute();

    if (!$updateServerResult) {
      error_log("Failed to update server record");
      $pdo->rollBack();
      throw new Exception('Failed to update server record');
    }

    error_log("Server record updated successfully");

    // Step 6: Update the port record to clear device assignment, leave status unchanged
    $updatePortSql = "UPDATE inventory_switch_ports SET
                      connected_device_id = NULL,
                      connected_device_type = NULL
                      WHERE id = :port_id";
    $updatePortSth = $pdo->prepare($updatePortSql);
    $updatePortSth->bindValue(':port_id', $targetPort['id']);
    $updatePortResult = $updatePortSth->execute();

    if (!$updatePortResult) {
      error_log("Failed to update port record");
      $pdo->rollBack();
      throw new Exception('Failed to update port record');
    }

    error_log("Port record updated successfully");

    // Commit transaction
    $pdo->commit();

    error_log("Transaction committed successfully");

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Port disconnected successfully',
      'port_field' => $portField,
      'debug_info' => [
        'device_id' => $device_id,
        'device_type' => $device_type,
        'port_number' => $port_number,
        'port_field_cleared' => $portField,
        'switch_id_cleared' => !$otherPorts
      ]
    ]);

  } catch (Exception $e) {
    // Rollback transaction if it was started
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Unassign port error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}


// API endpoint to update port speed
elseif ($_GET['f'] == 'update_port_speed') {
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['port_id'])) {
      throw new Exception('Port ID is required');
    }

    if (!isset($data['max_speed'])) {
      throw new Exception('Speed value is required');
    }

    // Update the port speed
    $result = updatePortSpeed($data['port_id'], $data['max_speed']);

    // Return success
    header('Content-Type: application/json');
    echo json_encode($result);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'detect_idrac_storage'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Detailed logging
    error_log("Storage detection requested by admin_id: $admin_id");

    // Get data from request
    $data = json_decode(file_get_contents('php://input'), true);
    error_log("Request data: " . json_encode($data));

    // Validate required fields
    if (!isset($data['ipmi_address'])) {
      throw new Exception('iDRAC IP address is required');
    }

    $server_id = isset($data['server_id']) ? $data['server_id'] : null;
    $server_type = isset($data['server_type']) ? $data['server_type'] : 'blade'; // Default to blade if not specified
    $ipmi_address = $data['ipmi_address'];
    $ipmi_username = isset($data['ipmi_username']) ? $data['ipmi_username'] : 'root';
    $ipmi_password = isset($data['ipmi_password']) ? $data['ipmi_password'] : '';
    $idrac_version = isset($data['idrac_version']) ? $data['idrac_version'] : 8;

    // Create storage_detection_history record
    $history_id = null;
    try {
      $sth = $pdo->prepare("INSERT INTO storage_detection_history
                           (server_id, server_type, admin_id, status)
                           VALUES (:server_id, :server_type, :admin_id, 'pending')");
      $sth->bindValue(':server_id', $server_id);
      $sth->bindValue(':server_type', $server_type); // Store server type in history
      $sth->bindValue(':admin_id', $admin_id);
      $sth->execute();
      $history_id = $pdo->lastInsertId();
      error_log("Created detection history record: $history_id for $server_type server ID: $server_id");
    } catch (Exception $e) {
      error_log("Non-critical error: Could not create history record: " . $e->getMessage());
      // Continue with detection even if history record fails
    }

    // Function to sanitize command input for security
    function sanitize_cmd_input($input) {
      return escapeshellarg($input);
    }

    // Test if sshpass is available
    exec('which sshpass', $which_output, $which_return);
    if ($which_return !== 0) {
      throw new Exception('sshpass is not installed on the server');
    }

    // Test if ssh is available
    exec('which ssh', $which_ssh_output, $which_ssh_return);
    if ($which_ssh_return !== 0) {
      throw new Exception('ssh is not installed on the server');
    }

    // Prepare SSH connection
    $ipmi_address_clean = sanitize_cmd_input($ipmi_address);
    $ipmi_username_clean = sanitize_cmd_input($ipmi_username);

    // Set timeout for SSH commands (30 seconds)
    $timeout = 30;

    // Prepare the command based on iDRAC version
    // First, create a temporary file for the password
    $temp_password_file = tempnam(sys_get_temp_dir(), 'sshpass');
    if (!$temp_password_file) {
      throw new Exception('Failed to create temporary password file');
    }

    $write_result = file_put_contents($temp_password_file, $ipmi_password);
    if ($write_result === false) {
      throw new Exception('Failed to write to temporary password file');
    }

    $chmod_result = chmod($temp_password_file, 0600); // Secure the file
    if (!$chmod_result) {
      throw new Exception('Failed to set permissions on temporary password file');
    }

    // Test basic connection first
    $test_cmd = "sshpass -f " . escapeshellarg($temp_password_file) .
                " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 " .
                "$ipmi_username_clean@$ipmi_address_clean 'echo Connection test'";

    error_log("Testing basic SSH connection with command: " . preg_replace('/sshpass -f .*? ssh/', 'sshpass -f [PASSWORD_FILE] ssh', $test_cmd));

    exec($test_cmd, $test_output, $test_return_var);
    if ($test_return_var !== 0) {
      throw new Exception("SSH connection test failed (code $test_return_var): " . implode("\n", $test_output));
    }

    error_log("SSH connection test successful");

    // Based on test success, now run the actual storage detection command
    if ($idrac_version == 9) {
      // iDRAC 9 uses REDFISH API via racadm
      $cmd = "sshpass -f " . escapeshellarg($temp_password_file) .
             " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=$timeout " .
             "$ipmi_username_clean@$ipmi_address_clean 'racadm storage get pdisks -o'";

      // Fall back to alternate command if first one fails
      $fallback_cmd = "sshpass -f " . escapeshellarg($temp_password_file) .
                      " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=$timeout " .
                      "$ipmi_username_clean@$ipmi_address_clean 'racadm storage get pdisks'";
    } else {
      // iDRAC 8 uses older commands
      $cmd = "sshpass -f " . escapeshellarg($temp_password_file) .
             " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=$timeout " .
             "$ipmi_username_clean@$ipmi_address_clean 'racadm raid get pdisks -o'";

      // Fall back to alternate command if first one fails
      $fallback_cmd = "sshpass -f " . escapeshellarg($temp_password_file) .
                      " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=$timeout " .
                      "$ipmi_username_clean@$ipmi_address_clean 'racadm raid get pdisks'";
    }

    error_log("Executing disk detection command for $server_type server: " . preg_replace('/sshpass -f .*? ssh/', 'sshpass -f [PASSWORD_FILE] ssh', $cmd));

    // Execute the command
    $output = [];
    $return_var = 0;
    exec($cmd, $output, $return_var);

    // If primary command fails, try fallback
    if ($return_var !== 0) {
      error_log("Primary command failed with code $return_var, trying fallback command");
      exec($fallback_cmd, $output, $return_var);

      if ($return_var !== 0) {
        // Both commands failed
        throw new Exception("Failed to execute storage detection commands (code $return_var): " . implode("\n", $output));
      }
    }

    // Delete the temporary password file
    unlink($temp_password_file);

    // Log the raw output for debugging
    $raw_output = implode("\n", $output);
    error_log("iDRAC raw output for $server_type server (first 200 chars): " . substr($raw_output, 0, 200) . "...");

    // Parse the output with more flexible bay number extraction
    $disks = [];
    $current_disk = null;
    $disk_size_gb = null;
    $bay_number = null;

    foreach ($output as $line) {
        // Try multiple patterns for bay extraction
        if (preg_match('/Disk\.(Bay\.(\d+)|Direct\.(\d+)-(\d+)|(\d+)-(\d+)):/', $line, $matches)) {
            // Extract bay number using the most appropriate match
            $new_bay_number = null;

            // Priority order for bay number extraction
            if (!empty($matches[2])) {
                $new_bay_number = $matches[2]; // Disk.Bay.X pattern
            } elseif (!empty($matches[3])) {
                $new_bay_number = $matches[3]; // Disk.Direct.X-Y pattern
            } elseif (!empty($matches[5])) {
                $new_bay_number = $matches[5]; // X-Y pattern
            }

            // If we have a current disk, save it before starting a new one
            if ($current_disk && $disk_size_gb !== null) {
                $disks[] = [
                    'id' => $current_disk,
                    'size_gb' => $disk_size_gb,
                    'bay_number' => $bay_number
                ];
            }

            // Start a new disk
            $current_disk = $matches[0];
            $disk_size_gb = null;
            $bay_number = $new_bay_number;
        }

        // Look for size info
        if (preg_match('/Size\s*=\s*([\d\.]+)\s*(GB|TB)/i', $line, $size_matches)) {
            $size = floatval($size_matches[1]);
            $unit = strtoupper($size_matches[2]);

            // Convert to GB if needed
            if ($unit == 'TB') {
                $size = $size * 1000;
            }

            $disk_size_gb = $size;
        }
    }

    // Add the last disk
    if ($current_disk && $disk_size_gb !== null) {
        $disks[] = [
            'id' => $current_disk,
            'size_gb' => $disk_size_gb,
            'bay_number' => $bay_number
        ];
    }

    // Normalize bay numbers to be sequential
    $normalized_disks = [];
    $bay_mapping = [];
    $next_available_bay = 0;

    // Sort disks by size to maintain a consistent mapping
    usort($disks, function($a, $b) {
        return $a['size_gb'] <=> $b['size_gb'];
    });

    foreach ($disks as $disk) {
        if (!isset($bay_mapping[$disk['bay_number']])) {
            $bay_mapping[$disk['bay_number']] = $next_available_bay++;
        }

        $normalized_disks[] = [
            'id' => $disk['id'],
            'size_gb' => $disk['size_gb'],
            'bay_number' => $bay_mapping[$disk['bay_number']]
        ];
    }

    $disks = $normalized_disks;

    // Log the bay mapping for debugging
    error_log("Bay mapping for $server_type server: " . json_encode($bay_mapping));
    error_log("Normalized disks for $server_type server: " . json_encode($disks));

    // If no disks found, create mock data or throw an exception
    if (count($disks) == 0) {
        throw new Exception("No disk information could be extracted from iDRAC output");
    }

    // Update history record if it exists
    if ($history_id) {
      try {
        $sth = $pdo->prepare("UPDATE storage_detection_history
                             SET status = 'success',
                                 disks_detected = :disks_count,
                                 raw_output = :raw_output,
                                 server_type = :server_type
                             WHERE id = :id");
        $sth->bindValue(':disks_count', count($disks));
        $sth->bindValue(':raw_output', $raw_output);
        $sth->bindValue(':server_type', $server_type);
        $sth->bindValue(':id', $history_id);
        $sth->execute();
      } catch (Exception $e) {
        error_log("Non-critical error: Could not update history record: " . $e->getMessage());
      }
    }

    // Return the results with server type included
    error_log("Successfully detected " . count($disks) . " disks for $server_type server");
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'disks' => $disks,
      'raw_output' => $raw_output,
      'idrac_version' => $idrac_version,
      'server_type' => $server_type
    ]);

  } catch (Exception $e) {
    error_log("Error in detect_idrac_storage: " . $e->getMessage());

    // Update history record with error if it exists
    if (isset($history_id)) {
      try {
        $sth = $pdo->prepare("UPDATE storage_detection_history
                           SET status = 'error',
                               error_message = :error_message,
                               server_type = :server_type
                           WHERE id = :id");
        $sth->bindValue(':error_message', $e->getMessage());
        $sth->bindValue(':server_type', $server_type ?? 'unknown');
        $sth->bindValue(':id', $history_id);
        $sth->execute();
      } catch (Exception $update_error) {
        error_log("Could not update history record with error: " . $update_error->getMessage());
      }
    }

    // Clean up temporary file if it exists
    if (isset($temp_password_file) && file_exists($temp_password_file)) {
      unlink($temp_password_file);
    }

    // Return detailed error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Storage detection failed: ' . $e->getMessage(),
      'details' => [
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
      ],
      'server_type' => $server_type ?? 'unknown'
    ]);
  }
}

elseif ($_GET['f'] == 'get_port_traffic') {
  try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get data
      $data = json_decode(file_get_contents('php://input'), true);

      // Validate required fields
      if (!isset($data['port_id']) || empty($data['port_id'])) {
          throw new Exception('Port ID is required');
      }

      $port_id = intval($data['port_id']);
      $days = isset($data['days']) ? floatval($data['days']) : 30;
      $interval = isset($data['interval']) ? intval($data['interval']) : 1; // minutes

      // Calculate start date
      $startDate = date('Y-m-d H:i:s', strtotime("-$days days"));

      // APPROACH CHANGE: Instead of aggregating and then calculating rates,
      // we'll get the raw data points directly and calculate rates in one pass

      // First, get the raw data points ordered by timestamp
      $rawDataQuery = $pdo->prepare("
          SELECT
              timestamp,
              in_bytes,
              out_bytes,
              in_packets,
              out_packets,
              in_errors,
              out_errors
          FROM switch_port_traffic
          WHERE port_id = :port_id AND timestamp >= :start_date
          ORDER BY timestamp ASC
      ");

      $rawDataQuery->bindValue(':port_id', $port_id, PDO::PARAM_INT);
      $rawDataQuery->bindValue(':start_date', $startDate);
      $rawDataQuery->execute();

      $rawData = $rawDataQuery->fetchAll(PDO::FETCH_ASSOC);

      // Exit early if no data
      if (empty($rawData)) {
          header('Content-Type: application/json');
          echo json_encode([
              'success' => true,
              'port_id' => $port_id,
              'days' => $days,
              'interval' => $interval,
              'data' => [],
              'message' => 'No data found for the specified time period'
          ]);
          return;
      }

      // Now, downsample the data according to the requested interval
      $intervalSeconds = $interval * 60;
      $sampledData = [];
      $currentInterval = null;
      $currentPoint = null;

      foreach ($rawData as $point) {
          // Calculate the interval this point belongs to
          $pointTimestamp = strtotime($point['timestamp']);
          $pointInterval = floor($pointTimestamp / $intervalSeconds) * $intervalSeconds;
          $intervalKey = date('Y-m-d H:i:s', $pointInterval);

          if ($currentInterval !== $intervalKey) {
              // We've moved to a new interval, store the previous one
              if ($currentPoint !== null) {
                  $sampledData[] = $currentPoint;
              }

              // Start a new interval
              $currentInterval = $intervalKey;
              $currentPoint = [
                  'interval_start' => $intervalKey,
                  'first_timestamp' => $point['timestamp'],
                  'last_timestamp' => $point['timestamp'],
                  'first_in_bytes' => $point['in_bytes'],
                  'last_in_bytes' => $point['in_bytes'],
                  'first_out_bytes' => $point['out_bytes'],
                  'last_out_bytes' => $point['out_bytes'],
                  'first_in_packets' => $point['in_packets'],
                  'last_in_packets' => $point['in_packets'],
                  'first_out_packets' => $point['out_packets'],
                  'last_out_packets' => $point['out_packets'],
                  'in_errors' => $point['in_errors'],
                  'out_errors' => $point['out_errors']
              ];
          } else {
              // Update the existing interval with the latest values
              $currentPoint['last_timestamp'] = $point['timestamp'];
              $currentPoint['last_in_bytes'] = $point['in_bytes'];
              $currentPoint['last_out_bytes'] = $point['out_bytes'];
              $currentPoint['last_in_packets'] = $point['in_packets'];
              $currentPoint['last_out_packets'] = $point['out_packets'];
              $currentPoint['in_errors'] += $point['in_errors'];
              $currentPoint['out_errors'] += $point['out_errors'];
          }
      }

      // Add the last interval if necessary
      if ($currentPoint !== null) {
          $sampledData[] = $currentPoint;
      }

      // Calculate rates for each interval
      $processedData = [];

      foreach ($sampledData as $i => $sample) {
          // Calculate time difference within this interval
          $startTime = strtotime($sample['first_timestamp']);
          $endTime = strtotime($sample['last_timestamp']);
          $timeElapsed = $endTime - $startTime;

          // If there's only one data point in this interval, we can't calculate a rate
          // So we'll use the previous and next intervals to calculate an average rate
          if ($timeElapsed < 30) { // Less than 30 seconds = probably single point
              // Try to use previous interval
              if ($i > 0) {
                  $prevSample = $sampledData[$i - 1];
                  $prevTime = strtotime($prevSample['last_timestamp']);
                  $sampleTime = strtotime($sample['first_timestamp']);

                  $timeElapsed = $sampleTime - $prevTime;

                  if ($timeElapsed > 0) {
                      $inBytesDiff = $sample['first_in_bytes'] - $prevSample['last_in_bytes'];
                      $outBytesDiff = $sample['first_out_bytes'] - $prevSample['last_out_bytes'];
                      $inPktsDiff = $sample['first_in_packets'] - $prevSample['last_in_packets'];
                      $outPktsDiff = $sample['first_out_packets'] - $prevSample['last_out_packets'];

                      // Handle counter resets
                      if ($inBytesDiff < 0) $inBytesDiff = $sample['first_in_bytes'];
                      if ($outBytesDiff < 0) $outBytesDiff = $sample['first_out_bytes'];
                      if ($inPktsDiff < 0) $inPktsDiff = $sample['first_in_packets'];
                      if ($outPktsDiff < 0) $outPktsDiff = $sample['first_out_packets'];

                      // Calculate rates
                      $inBps = $inBytesDiff / $timeElapsed;
                      $outBps = $outBytesDiff / $timeElapsed;
                      $inPps = $inPktsDiff / $timeElapsed;
                      $outPps = $outPktsDiff / $timeElapsed;
                  } else {
                      // Default to zero if time difference is invalid
                      $inBps = $outBps = $inPps = $outPps = 0;
                  }
              } else {
                  // No previous interval, check for next interval
                  if ($i < count($sampledData) - 1) {
                      $nextSample = $sampledData[$i + 1];
                      $sampleTime = strtotime($sample['first_timestamp']);
                      $nextTime = strtotime($nextSample['first_timestamp']);

                      $timeElapsed = $nextTime - $sampleTime;

                      if ($timeElapsed > 0) {
                          $inBytesDiff = $nextSample['first_in_bytes'] - $sample['first_in_bytes'];
                          $outBytesDiff = $nextSample['first_out_bytes'] - $sample['first_out_bytes'];
                          $inPktsDiff = $nextSample['first_in_packets'] - $sample['first_in_packets'];
                          $outPktsDiff = $nextSample['first_out_packets'] - $sample['first_out_packets'];

                          // Handle counter resets
                          if ($inBytesDiff < 0) $inBytesDiff = $nextSample['first_in_bytes'];
                          if ($outBytesDiff < 0) $outBytesDiff = $nextSample['first_out_bytes'];
                          if ($inPktsDiff < 0) $inPktsDiff = $nextSample['first_in_packets'];
                          if ($outPktsDiff < 0) $outPktsDiff = $nextSample['first_out_packets'];

                          // Calculate rates
                          $inBps = $inBytesDiff / $timeElapsed;
                          $outBps = $outBytesDiff / $timeElapsed;
                          $inPps = $inPktsDiff / $timeElapsed;
                          $outPps = $outPktsDiff / $timeElapsed;
                      } else {
                          // Default to zero if time difference is invalid
                          $inBps = $outBps = $inPps = $outPps = 0;
                      }
                  } else {
                      // No previous or next interval, can't calculate rate
                      $inBps = $outBps = $inPps = $outPps = 0;
                  }
              }
          } else {
              // Normal case: calculate rate using the first and last points in this interval
              $inBytesDiff = $sample['last_in_bytes'] - $sample['first_in_bytes'];
              $outBytesDiff = $sample['last_out_bytes'] - $sample['first_out_bytes'];
              $inPktsDiff = $sample['last_in_packets'] - $sample['first_in_packets'];
              $outPktsDiff = $sample['last_out_packets'] - $sample['first_out_packets'];

              // Handle counter resets
              if ($inBytesDiff < 0) $inBytesDiff = $sample['last_in_bytes'];
              if ($outBytesDiff < 0) $outBytesDiff = $sample['last_out_bytes'];
              if ($inPktsDiff < 0) $inPktsDiff = $sample['last_in_packets'];
              if ($outPktsDiff < 0) $outPktsDiff = $sample['last_out_packets'];

              // Calculate rates
              $inBps = ($timeElapsed > 0) ? $inBytesDiff / $timeElapsed : 0;
              $outBps = ($timeElapsed > 0) ? $outBytesDiff / $timeElapsed : 0;
              $inPps = ($timeElapsed > 0) ? $inPktsDiff / $timeElapsed : 0;
              $outPps = ($timeElapsed > 0) ? $outPktsDiff / $timeElapsed : 0;
          }

          // Add to processed data
          $processedData[] = [
              'timestamp' => $sample['interval_start'],
              'in_bytes' => $inBytesDiff,
              'out_bytes' => $outBytesDiff,
              'in_bps' => $inBps * 8, // Convert to bits per second
              'out_bps' => $outBps * 8,
              'in_pps' => $inPps,
              'out_pps' => $outPps,
              'in_errors' => $sample['in_errors'],
              'out_errors' => $sample['out_errors'],
              // Including raw data for debugging
              'first_timestamp' => $sample['first_timestamp'],
              'last_timestamp' => $sample['last_timestamp']
          ];
      }

      // Return the data
      header('Content-Type: application/json');
      echo json_encode([
          'success' => true,
          'port_id' => $port_id,
          'days' => $days,
          'interval' => $interval,
          'data' => $processedData,
          'data_points' => count($rawData),
          'intervals' => count($sampledData)
      ]);

  } catch (Exception $e) {
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
          'success' => false,
          'error' => $e->getMessage()
      ]);
  }
}

elseif($_GET['f'] == 'check_port_traffic_data'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['port_id']) || empty($data['port_id'])) {
      throw new Exception('Port ID is required');
    }

    $port_id = intval($data['port_id']);

    // 1. First check if the port exists
    $portQuery = $pdo->prepare("SELECT * FROM inventory_switch_ports WHERE id = :port_id");
    $portQuery->bindValue(':port_id', $port_id, PDO::PARAM_INT);
    $portQuery->execute();

    $portExists = $portQuery->rowCount() > 0;
    $portData = $portExists ? $portQuery->fetch(PDO::FETCH_ASSOC) : null;

    // 2. Count total data points for this port
    $countQuery = $pdo->prepare("SELECT COUNT(*) FROM switch_port_traffic WHERE port_id = :port_id");
    $countQuery->bindValue(':port_id', $port_id, PDO::PARAM_INT);
    $countQuery->execute();
    $totalDataPoints = $countQuery->fetchColumn();

    // 3. Get the first and last data points for this port to check date range
    $rangeQuery = $pdo->prepare("
      SELECT
        MIN(timestamp) as first_timestamp,
        MAX(timestamp) as last_timestamp
      FROM switch_port_traffic
      WHERE port_id = :port_id
    ");
    $rangeQuery->bindValue(':port_id', $port_id, PDO::PARAM_INT);
    $rangeQuery->execute();
    $dateRange = $rangeQuery->fetch(PDO::FETCH_ASSOC);

    // 4. Get some sample data points
    $sampleQuery = $pdo->prepare("
      SELECT *
      FROM switch_port_traffic
      WHERE port_id = :port_id
      ORDER BY timestamp DESC
      LIMIT 5
    ");
    $sampleQuery->bindValue(':port_id', $port_id, PDO::PARAM_INT);
    $sampleQuery->execute();
    $sampleData = $sampleQuery->fetchAll(PDO::FETCH_ASSOC);

    // Return diagnostic information
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'port_exists' => $portExists,
      'port_data' => $portData,
      'total_data_points' => $totalDataPoints,
      'date_range' => $dateRange,
      'sample_data' => $sampleData,
      'diagnostic_time' => date('Y-m-d H:i:s')
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// API endpoint to manually trigger port traffic collection
elseif($_GET['f'] == 'collect_port_traffic'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Execute the collection function
    $stats = collectSwitchPortTraffic();

    // Return collection statistics
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'collection_stats' => $stats,
      'collection_time' => date('Y-m-d H:i:s'),
      'message' => 'Traffic collection executed successfully'
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}


elseif($_GET['f'] == 'detect_idrac_version'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data from request
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['ipmi_address'])) {
      throw new Exception('iDRAC IP address is required');
    }

    $ipmi_address = $data['ipmi_address'];
    $ipmi_username = isset($data['ipmi_username']) ? $data['ipmi_username'] : 'root';
    $ipmi_password = isset($data['ipmi_password']) ? $data['ipmi_password'] : '';

    // Log the request for debugging
    error_log("iDRAC version detection request for: $ipmi_address");

    // Function to sanitize command input for security
    function sanitize_cmd_input($input) {
      return escapeshellarg($input);
    }

    // Prepare SSH connection
    $ipmi_address_clean = sanitize_cmd_input($ipmi_address);
    $ipmi_username_clean = sanitize_cmd_input($ipmi_username);

    // Create a temporary password file for sshpass
    $temp_password_file = tempnam(sys_get_temp_dir(), 'sshpass');
    file_put_contents($temp_password_file, $ipmi_password);
    chmod($temp_password_file, 0600); // Secure the file

    // Set timeout for SSH commands (10 seconds)
    $timeout = 10;

    // First try to detect using racadm version command
    $cmd = "sshpass -f " . escapeshellarg($temp_password_file) .
           " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=$timeout " .
           "$ipmi_username_clean@$ipmi_address_clean 'racadm version'";

    // Execute the command
    $output = [];
    $return_var = 0;
    exec($cmd, $output, $return_var);

    // Check results - if we got a successful connection
    if ($return_var == 0) {
      // Join output into a single string for easier pattern matching
      $output_text = implode("\n", $output);

      // Look for iDRAC version information
      if (preg_match('/idrac\s*version\s*:?\s*(\d+)\./', $output_text, $matches)) {
        $idrac_version = (int)$matches[1];

        // Clean up temporary password file
        unlink($temp_password_file);

        // Valid versions are 7, 8, 9
        if ($idrac_version >= 7 && $idrac_version <= 9) {
          // Return the detected version
          header('Content-Type: application/json');
          echo json_encode([
            'success' => true,
            'idrac_version' => $idrac_version,
            'method' => 'racadm version'
          ]);
          return;
        }
      }

      // If we didn't find a clear version number, try to infer from command support

      // Try iDRAC 9 specific command
      $cmd = "sshpass -f " . escapeshellarg($temp_password_file) .
             " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=$timeout " .
             "$ipmi_username_clean@$ipmi_address_clean 'racadm storage get controllers'";

      $output9 = [];
      $return_var9 = 0;
      exec($cmd, $output9, $return_var9);

      if ($return_var9 == 0 && !empty($output9) && !preg_match('/not found|invalid|unsupported/i', implode("\n", $output9))) {
        // Command succeeded, likely iDRAC 9
        unlink($temp_password_file);
        header('Content-Type: application/json');
        echo json_encode([
          'success' => true,
          'idrac_version' => 9,
          'method' => 'storage command support'
        ]);
        return;
      }

      // Try iDRAC 8 specific command structure
      $cmd = "sshpass -f " . escapeshellarg($temp_password_file) .
             " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=$timeout " .
             "$ipmi_username_clean@$ipmi_address_clean 'racadm raid get controllers'";

      $output8 = [];
      $return_var8 = 0;
      exec($cmd, $output8, $return_var8);

      if ($return_var8 == 0 && !empty($output8) && !preg_match('/not found|invalid|unsupported/i', implode("\n", $output8))) {
        // Command succeeded, likely iDRAC 8
        unlink($temp_password_file);
        header('Content-Type: application/json');
        echo json_encode([
          'success' => true,
          'idrac_version' => 8,
          'method' => 'raid command support'
        ]);
        return;
      }
    }

    // If we get here, default to iDRAC 8
    unlink($temp_password_file);
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'idrac_version' => 8,
      'method' => 'default value'
    ]);

  } catch (Exception $e) {
    // Clean up temporary file if it exists
    if (isset($temp_password_file) && file_exists($temp_password_file)) {
      unlink($temp_password_file);
    }

    error_log("Error in detect_idrac_version: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Get all CPUs from the dedicated_cpu table
elseif($_GET['f'] == 'get_dedicated_cpus'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Build a simple query to get all CPU records
    $sql = "SELECT * FROM dedicated_cpu ORDER BY cpu ASC";

    // Execute query
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $cpus = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Return CPUs as JSON
    header('Content-Type: application/json');
    echo json_encode($cpus);

  } catch (Exception $e) {
    error_log("Error in get_dedicated_cpus: " . $e->getMessage());

    // Check if the table exists, if not return empty array
    try {
      $tableExists = $pdo->query("SHOW TABLES LIKE 'dedicated_cpu'")->rowCount() > 0;
      if (!$tableExists) {
        header('Content-Type: application/json');
        echo json_encode([]);
        return;
      }
    } catch (Exception $checkError) {
      // If we can't even check if the table exists, there's a serious DB issue
      error_log("Error checking if dedicated_cpu table exists: " . $checkError->getMessage());
    }

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'error' => 'Failed to fetch CPU options',
      'details' => $e->getMessage()
    ]);
  }
}




elseif($_GET['f'] == 'detect_mac_address'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data from request
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['ipmi_address'])) {
      throw new Exception('iDRAC IP address is required');
    }

    $server_id = isset($data['server_id']) ? $data['server_id'] : null;
    $server_type = isset($data['server_type']) ? $data['server_type'] : 'blade'; // Default to blade if not specified
    $ipmi_address = $data['ipmi_address'];
    $ipmi_username = isset($data['ipmi_username']) ? $data['ipmi_username'] : 'root';
    $ipmi_password = isset($data['ipmi_password']) ? $data['ipmi_password'] : '';
    $idrac_version = isset($data['idrac_version']) ? $data['idrac_version'] : 8;

    // Create detection history record
    $history_id = null;
    try {
      $sth = $pdo->prepare("INSERT INTO detection_history
                           (server_id, server_type, admin_id, detection_type, status)
                           VALUES (:server_id, :server_type, :admin_id, 'mac', 'pending')");
      $sth->bindValue(':server_id', $server_id);
      $sth->bindValue(':server_type', $server_type);
      $sth->bindValue(':admin_id', $admin_id);
      $sth->execute();
      $history_id = $pdo->lastInsertId();
    } catch (Exception $e) {
      error_log("Non-critical error: Could not create history record: " . $e->getMessage());
      // Continue with detection even if history record fails
    }

    // Function to sanitize command input for security
    function sanitize_cmd_input($input) {
      return escapeshellarg($input);
    }

    // Create temporary password file
    $temp_password_file = tempnam(sys_get_temp_dir(), 'sshpass');
    if (!$temp_password_file) {
      throw new Exception('Failed to create temporary password file');
    }

    $write_result = file_put_contents($temp_password_file, $ipmi_password);
    if ($write_result === false) {
      throw new Exception('Failed to write to temporary password file');
    }

    chmod($temp_password_file, 0600); // Secure the file

    // Prepare SSH connection
    $ipmi_address_clean = sanitize_cmd_input($ipmi_address);
    $ipmi_username_clean = sanitize_cmd_input($ipmi_username);

    // Set timeout for SSH commands
    $timeout = 15;

    // Test basic connection first
    $test_cmd = "sshpass -f " . escapeshellarg($temp_password_file) .
                " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 " .
                "$ipmi_username_clean@$ipmi_address_clean 'echo Connection test'";

    exec($test_cmd, $test_output, $test_return_var);
    if ($test_return_var !== 0) {
      throw new Exception("SSH connection test failed (code $test_return_var)");
    }

    // Store all command attempts and their outputs
    $commands_tried = [];
    $all_outputs = [];

    // Try different commands in sequence until one works
    $command_list = [
        'hwinventory' => "racadm hwinventory",
        'getniccfg' => "racadm getniccfg",
        'get_nic' => "racadm get NIC",
        'get_idrac_nic' => "racadm get iDRAC.NIC",
        'ifconfig' => "ifconfig",
        'sbin_ifconfig' => "/sbin/ifconfig",
        'network_info' => "racadm getsysinfo -d SystemBoardNIC"
    ];

    $output = [];
    $success = false;

    foreach ($command_list as $cmd_name => $cmd_string) {
        // Create new temporary password file for each attempt
        $temp_password_file = tempnam(sys_get_temp_dir(), 'sshpass');
        file_put_contents($temp_password_file, $ipmi_password);
        chmod($temp_password_file, 0600);

        $full_cmd = "sshpass -f " . escapeshellarg($temp_password_file) .
               " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=$timeout " .
               "$ipmi_username_clean@$ipmi_address_clean '$cmd_string'";

        $commands_tried[$cmd_name] = $cmd_string;
        $cmd_output = [];
        $return_var = 0;

        // Execute the command
        exec($full_cmd, $cmd_output, $return_var);

        // Delete the temporary password file
        unlink($temp_password_file);

        // Record the output even if command failed
        $all_outputs[$cmd_name] = [
            'output' => $cmd_output,
            'return_code' => $return_var,
            'success' => ($return_var === 0)
        ];

        // If command succeeded, use its output
        if ($return_var === 0 && !empty($cmd_output)) {
            $output = $cmd_output;
            $success = true;
            error_log("Successfully executed command: $cmd_string");
            break;
        }

        error_log("Command failed ($return_var): $cmd_string");
    }

    // If all commands failed, try one more desperate attempt with combined commands
    if (!$success) {
        $temp_password_file = tempnam(sys_get_temp_dir(), 'sshpass');
        file_put_contents($temp_password_file, $ipmi_password);
        chmod($temp_password_file, 0600);

        $desperate_cmd = "racadm getniccfg || racadm get NIC || ifconfig || /sbin/ifconfig";
        $full_cmd = "sshpass -f " . escapeshellarg($temp_password_file) .
               " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=$timeout " .
               "$ipmi_username_clean@$ipmi_address_clean '$desperate_cmd'";

        $commands_tried['desperate'] = $desperate_cmd;
        $cmd_output = [];
        $return_var = 0;

        // Execute the command
        exec($full_cmd, $cmd_output, $return_var);

        // Delete the temporary password file
        unlink($temp_password_file);

        // Record the output
        $all_outputs['desperate'] = [
            'output' => $cmd_output,
            'return_code' => $return_var,
            'success' => ($return_var === 0)
        ];

        // If command succeeded, use its output
        if ($return_var === 0 && !empty($cmd_output)) {
            $output = $cmd_output;
            $success = true;
            error_log("Successfully executed desperate combined command");
        }
    }

    // If we still have no success, throw exception
    if (!$success) {
        throw new Exception("All MAC detection commands failed. Check iDRAC credentials and connectivity.");
    }

    // Log the raw output for detailed debugging
    $raw_output = implode("\n", $output);
    error_log("iDRAC hardware inventory raw output for $server_type server: " . $raw_output);

    // Also log the command used
    error_log("Command executed for MAC detection: " . preg_replace('/sshpass -f .*? ssh/', 'sshpass -f [PASSWORD_FILE] ssh', $cmd));

    // Enhanced parsing with detailed logging
    $mac_addresses = [];
    $current_interface = null;
    $is_management_port = false;
    $current_mac = null;
    $current_link_status = null;
    $debug_lines = [];

    // Enhanced pattern to match MAC addresses (various formats)
    $mac_pattern = '/([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})/';

    // Additional patterns for different iDRAC versions
    $nic_patterns = [
        '/NIC\.(\w+)\.(\d+)-(\d+)/', // Standard pattern
        '/NIC\.Slot\.(\d+)-(\d+)-(\d+)/', // Alternative format
        '/NIC\.Embedded\.(\d+)-(\d+)/', // Embedded NICs
        '/NIC\.Integrated\.(\d+)-(\d+)/', // Integrated NICs
        '/NetworkDevice\.Slot\.(\d+)-(\d+)/', // Network devices
        '/NetworkAdapter\.Embedded\.(\d+)/', // Network adapters
        '/CurrentMACAddress=([0-9A-Fa-f:]{17})/', // Direct MAC format
        '/MACAddr=([0-9A-Fa-f:]{17})/' // Alternative MAC format
    ];

    foreach ($output as $line_num => $line) {
        $line = trim($line);
        $found_match = false;

        // Debug info for this line
        $line_debug = "#$line_num: $line";

        // Check if line contains a NIC identifier
        foreach ($nic_patterns as $pattern) {
            if (preg_match($pattern, $line, $matches)) {
                // Save previous interface if we have MAC and it's not management
                if ($current_interface && $current_mac && !$is_management_port) {
                    if ($current_link_status === 'Up' || $current_link_status === null) {
                        $mac_addresses[] = [
                            'interface' => $current_interface,
                            'mac' => $current_mac,
                            'link_status' => $current_link_status ?: 'Unknown',
                            'debug' => "Found from pattern: $pattern"
                        ];
                        $debug_lines[] = "SAVED MAC: $current_interface -> $current_mac (LinkStatus: " . ($current_link_status ?: 'Unknown') . ")";
                    } else {
                        $debug_lines[] = "SKIPPED MAC (DOWN): $current_interface -> $current_mac (LinkStatus: " . ($current_link_status ?: 'Unknown') . ")";
                    }
                }

                // Start new interface
                $current_interface = isset($matches[0]) ? $matches[0] : "unknown";
                $is_management_port = false;
                $current_mac = null;
                $current_link_status = null;
                $found_match = true;
                $line_debug .= " [NIC IDENTIFIER FOUND: $current_interface]";
                break;
            }
        }

        // Check if it's a management port (very broad detection)
        if (stripos($line, 'Management') !== false || stripos($line, 'iDRAC') !== false ||
            stripos($line, 'BMC') !== false || stripos($line, 'Dedicated') !== false ||
            stripos($line, 'DRAC') !== false || stripos($line, 'Remote Access Controller') !== false) {
            $is_management_port = true;
            $line_debug .= " [MANAGEMENT PORT DETECTED]";
        }

        // Direct MAC address detection in the line
        if (preg_match('/MACAddress=([0-9A-Fa-f:]{17})/', $line, $mac_matches)) {
            $current_mac = $mac_matches[1];
            $line_debug .= " [MAC FOUND: $current_mac]";
            $found_match = true;
        }
        // MAC with equals sign in iDRAC 9
        elseif (preg_match('/CurrentMACAddress=([0-9A-Fa-f:]{17})/', $line, $mac_matches)) {
            $current_mac = $mac_matches[1];
            $line_debug .= " [MAC FOUND: $current_mac]";
            $found_match = true;
        }
        // Look for standard MAC format anywhere in line
        elseif (preg_match($mac_pattern, $line, $mac_matches)) {
            // Check if we're in an interface context or in a short output format
            if ($current_interface || count($output) < 100) {
                $current_mac = $mac_matches[0];
                $line_debug .= " [MAC FOUND: $current_mac]";
                $found_match = true;
            }
        }

        // Look for link status with various formats
        if (preg_match('/LinkStatus=(Up|Down)/i', $line, $status_matches)) {
            $current_link_status = $status_matches[1];
            $line_debug .= " [LINK STATUS: $current_link_status]";
            $found_match = true;
        }
        elseif (preg_match('/Status=(Up|Down|Connected|Disconnected)/i', $line, $status_matches)) {
            // Convert Connected/Disconnected to Up/Down
            $status = $status_matches[1];
            $current_link_status = (strtolower($status) === 'connected') ? 'Up' :
                                  ((strtolower($status) === 'disconnected') ? 'Down' : $status);
            $line_debug .= " [LINK STATUS: $current_link_status]";
            $found_match = true;
        }
        // Simple 'up' word in the line (for getniccfg and simpler outputs)
        elseif (!$current_link_status &&
                (stripos($line, ' up') !== false ||
                 stripos($line, 'link=up') !== false ||
                 stripos($line, 'connected') !== false)) {
            $current_link_status = 'Up';
            $line_debug .= " [LINK STATUS: Up (from text)]";
            $found_match = true;
        }

        // For getniccfg output - simpler parsing
        if (stripos($line, 'MAC Address') !== false && preg_match($mac_pattern, $line, $mac_matches)) {
            // Determine if this is a management MAC
            $is_mgmt = (stripos($line, 'Dedicated') !== false || stripos($line, 'iDRAC') !== false);

            if (!$is_mgmt) {
                $mac_addresses[] = [
                    'interface' => 'NIC.Integrated.1-1',
                    'mac' => $mac_matches[0],
                    'link_status' => 'Up', // Assume up if found via getniccfg
                    'debug' => "Found from getniccfg"
                ];
                $line_debug .= " [DIRECT MAC FOUND: " . $mac_matches[0] . "]";
                $found_match = true;
            } else {
                $line_debug .= " [MANAGEMENT MAC SKIPPED]";
            }
        }

        // Log interesting lines (those that matched a pattern)
        if ($found_match) {
            $debug_lines[] = $line_debug;
        }
    }

    // Try some fallback methods for older iDRAC versions or different output formats
    if (count($mac_addresses) === 0) {
        $debug_lines[] = "No MACs found with primary method. Trying fallback...";

        // Simple approach - just find all MAC addresses in the output
        foreach ($output as $line) {
            if (preg_match($mac_pattern, $line, $mac_matches) &&
                stripos($line, 'MAC') !== false &&
                stripos($line, 'iDRAC') === false &&
                stripos($line, 'BMC') === false &&
                stripos($line, 'Management') === false) {

                $mac_addresses[] = [
                    'interface' => 'Unknown',
                    'mac' => $mac_matches[0],
                    'link_status' => 'Unknown',
                    'debug' => "Found with fallback method"
                ];
                $debug_lines[] = "FALLBACK MAC FOUND: " . $mac_matches[0] . " in line: $line";
            }
        }
    }

    // Add the last interface if we have one from hwinventory
    if ($current_interface && $current_mac && !$is_management_port) {
        if ($current_link_status === 'Up' || $current_link_status === null) {
            $mac_addresses[] = [
              'interface' => $current_interface,
              'mac' => $current_mac,
              'link_status' => $current_link_status ?: 'Unknown',
              'debug' => "Final interface capture"
            ];
            $debug_lines[] = "SAVED FINAL MAC: $current_interface -> $current_mac (LinkStatus: " . ($current_link_status ?: 'Unknown') . ")";
        } else {
            $debug_lines[] = "SKIPPED FINAL MAC (DOWN): $current_interface -> $current_mac (LinkStatus: " . ($current_link_status ?: 'Unknown') . ")";
        }
    }

    // Try an alternative more lenient approach if we didn't find anything
    if (count($mac_addresses) === 0) {
        $debug_lines[] = "No MACs found, trying to run alternative command...";

        // Create new temporary password file
        $temp_password_file = tempnam(sys_get_temp_dir(), 'sshpass');
        file_put_contents($temp_password_file, $ipmi_password);
        chmod($temp_password_file, 0600);

        // Try with ifconfig or a simpler racadm command
        $alt_cmd = "sshpass -f " . escapeshellarg($temp_password_file) .
                " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=$timeout " .
                "$ipmi_username_clean@$ipmi_address_clean 'ifconfig || /sbin/ifconfig || racadm get iDRAC.NIC'";

        $alt_output = [];
        $alt_return = 0;
        exec($alt_cmd, $alt_output, $alt_return);
        unlink($temp_password_file);

        if ($alt_return === 0 && !empty($alt_output)) {
            $debug_lines[] = "Got alternative output (" . count($alt_output) . " lines)";

            // Parse ifconfig-style output
            $current_iface = null;
            foreach ($alt_output as $line) {
                if (preg_match('/^([a-zA-Z0-9]+):.+$/', $line, $iface_match)) {
                    $current_iface = $iface_match[1];
                    if ($current_iface === 'lo' || $current_iface === 'lo0' ||
                        stripos($current_iface, 'loop') !== false) {
                        $current_iface = null; // Skip loopback
                    }
                }

                if ($current_iface && preg_match($mac_pattern, $line, $mac_match)) {
                    // Skip if it looks like a management interface
                    if (stripos($current_iface, 'idrac') === false &&
                        stripos($current_iface, 'ipmi') === false &&
                        stripos($current_iface, 'drac') === false &&
                        stripos($current_iface, 'mgmt') === false) {

                        $mac_addresses[] = [
                            'interface' => $current_iface,
                            'mac' => $mac_match[0],
                            'link_status' => 'Unknown',
                            'debug' => "Found from ifconfig"
                        ];
                        $debug_lines[] = "IFCONFIG MAC FOUND: $current_iface -> " . $mac_match[0];
                    }
                }
            }
        }
    }

    // Log MAC addresses found
    error_log("MAC addresses found (" . count($mac_addresses) . "): " . json_encode($mac_addresses));

    // Get the first up non-management port MAC
    $primary_mac = count($mac_addresses) > 0 ? $mac_addresses[0]['mac'] : null;

    // Last resort: try to extract any MAC-like pattern from the raw output
    if (!$primary_mac) {
        $debug_lines[] = "Still no MAC found, trying last resort pattern matching...";

        // Match anything that looks like a MAC address
        preg_match_all($mac_pattern, $raw_output, $all_matches);
        if (!empty($all_matches[0])) {
            $debug_lines[] = "Found " . count($all_matches[0]) . " potential MACs with raw pattern matching";

            // Filter out obvious management MACs
            $filtered_macs = [];
            foreach ($all_matches[0] as $potential_mac) {
                // Get the context (500 chars) around this MAC
                $pos = strpos($raw_output, $potential_mac);
                $start = max(0, $pos - 250);
                $length = min(500, strlen($raw_output) - $start);
                $context = substr($raw_output, $start, $length);

                // Skip if the context suggests this is a management interface
                if (stripos($context, 'idrac') === false &&
                    stripos($context, 'drac') === false &&
                    stripos($context, 'mgmt') === false &&
                    stripos($context, 'ipmi') === false &&
                    stripos($context, 'bmc') === false) {

                    $filtered_macs[] = $potential_mac;
                    $debug_lines[] = "POTENTIAL MAC: $potential_mac";
                }
            }

            if (!empty($filtered_macs)) {
                $primary_mac = $filtered_macs[0];
                $debug_lines[] = "SELECTED LAST RESORT MAC: $primary_mac";
            }
        }
    }

    // Standardize MAC format (lowercase with colons)
    if ($primary_mac) {
      // Replace dashes with colons if needed
      $primary_mac = str_replace('-', ':', $primary_mac);
      // Make lowercase
      $primary_mac = strtolower($primary_mac);
    }

    // Update history record if it exists
    if ($history_id) {
      try {
        $sth = $pdo->prepare("UPDATE detection_history
                           SET status = :status,
                               result = :result,
                               raw_output = :raw_output
                           WHERE id = :id");
        $sth->bindValue(':status', $primary_mac ? 'success' : 'error');
        $sth->bindValue(':result', $primary_mac);
        $sth->bindValue(':raw_output', $raw_output);
        $sth->bindValue(':id', $history_id);
        $sth->execute();
      } catch (Exception $e) {
        error_log("Non-critical error: Could not update history record: " . $e->getMessage());
      }
    }

    // Return the results with comprehensive debugging information
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'mac_address' => $primary_mac,
      'all_mac_addresses' => $mac_addresses,
      'raw_output' => $raw_output,
      'debug_lines' => $debug_lines,
      'commands_tried' => $commands_tried,
      'all_command_outputs' => array_map(function($item) {
          // Only return first 10 lines of each output to avoid excessive response size
          if (isset($item['output']) && is_array($item['output']) && count($item['output']) > 10) {
              $item['output'] = array_slice($item['output'], 0, 10);
              $item['output'][] = "... (truncated for response size)";
          }
          return $item;
      }, $all_outputs),
      'idrac_version' => $idrac_version,
      'server_type' => $server_type
    ]);

  } catch (Exception $e) {
    error_log("Error in detect_mac_address: " . $e->getMessage());

    // Clean up temporary file if it exists
    if (isset($temp_password_file) && file_exists($temp_password_file)) {
      unlink($temp_password_file);
    }

    // Update history record with error if it exists
    if (isset($history_id)) {
      try {
        $sth = $pdo->prepare("UPDATE detection_history
                           SET status = 'error',
                               error_message = :error_message,
                               raw_output = :raw_output
                           WHERE id = :id");
        $sth->bindValue(':error_message', $e->getMessage());
        $sth->bindValue(':raw_output', isset($raw_output) ? $raw_output : '');
        $sth->bindValue(':id', $history_id);
        $sth->execute();
      } catch (Exception $update_error) {
        error_log("Could not update history record with error: " . $update_error->getMessage());
      }
    }

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'MAC address detection failed: ' . $e->getMessage()
    ]);
  }
}



// Get chassis models
elseif($_GET['f'] == 'get_inventory_chassis_models'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Query to fetch chassis models
    $sql = "SELECT * FROM inventory_chassis_model ORDER BY name ASC";
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $models = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Return models as JSON
    header('Content-Type: application/json');
    echo json_encode($models);

  } catch (Exception $e) {
    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'error' => 'Failed to fetch chassis models',
      'details' => $e->getMessage()
    ]);
  }
}

// Get switch models
elseif($_GET['f'] == 'get_inventory_switch_models'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Query to fetch switch models
    $sql = "SELECT * FROM inventory_switch_model ORDER BY name ASC";
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $models = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Return models as JSON
    header('Content-Type: application/json');
    echo json_encode($models);

  } catch (Exception $e) {
    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'error' => 'Failed to fetch switch models',
      'details' => $e->getMessage()
    ]);
  }
}


elseif($_GET['f'] == 'add_chassis'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['label']) || empty($data['label'])) {
      throw new Exception('Chassis label is required');
    }

    if (!isset($data['status']) || empty($data['status'])) {
      // Default to 'Available' if not provided
      $data['status'] = 'Available';
    }

    // Log the input data to help with debugging
    error_log("Adding chassis with data: " . json_encode($data));

    // Prepare insert fields and values
    $fields = [];
    $placeholders = [];
    $params = [];

    // Define allowed fields
    $allowedFields = [
      'label', 'rack_id', 'position', 'status',
      'city_id', 'country_id', 'notes', 'model_id',
      'bay1', 'bay2', 'bay3', 'bay4'
    ];

    // Process fields with explicit type handling
    foreach ($allowedFields as $field) {
      if (array_key_exists($field, $data) && $data[$field] !== '') {
        $fields[] = "`$field`";
        $placeholders[] = ":$field";

        // Handle foreign key fields
        if (in_array($field, ['rack_id', 'city_id', 'country_id', 'model_id']) && $data[$field] !== null) {
          // Convert to integer or NULL for foreign keys
          $params[":$field"] = ($data[$field] === '0' || $data[$field] === 0) ? null : intval($data[$field]);
        } else {
          $params[":$field"] = $data[$field];
        }
      }
    }

    // Ensure we have fields to insert
    if (empty($fields)) {
      throw new Exception('No valid fields provided');
    }

    // Check if model_id is valid
    if (isset($data['model_id']) && $data['model_id'] !== null && $data['model_id'] !== '0' && $data['model_id'] !== 0) {
      $checkModel = $pdo->prepare("SELECT id FROM inventory_chassis_model WHERE id = :id");
      $checkModel->bindValue(':id', intval($data['model_id']));
      $checkModel->execute();

      if ($checkModel->rowCount() === 0) {
        throw new Exception('Invalid chassis model ID');
      }
    }

    // Build the SQL query
    $sql = "INSERT INTO inventory_chassis (" . implode(", ", $fields) . ") VALUES (" . implode(", ", $placeholders) . ")";
    error_log("SQL for adding chassis: $sql");
    error_log("Params: " . json_encode($params));

    // Execute the query
    $sth = $pdo->prepare($sql);
    foreach ($params as $key => $value) {
      if ($value === null) {
        $sth->bindValue($key, null, PDO::PARAM_NULL);
      } elseif (is_int($value)) {
        $sth->bindValue($key, $value, PDO::PARAM_INT);
      } else {
        $sth->bindValue($key, $value);
      }
    }
    $sth->execute();

    // Get the new chassis ID
    $chassis_id = $pdo->lastInsertId();

    if (!$chassis_id) {
      throw new Exception('Failed to get new chassis ID after insert');
    }

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $chassis_id,
      'message' => 'Chassis added successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_chassis: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to add chassis: ' . $e->getMessage()
    ]);
  }
}


elseif($_GET['f'] == 'add_switch'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // EXTENSIVE DEBUG LOGGING - remove in production
    error_log("====== ADD SWITCH DEBUG ======");
    error_log("Raw data: " . file_get_contents('php://input'));
    error_log("Parsed data: " . json_encode($data));
    error_log("rack_position (isset): " . (isset($data['rack_position']) ? 'YES' : 'NO'));
    error_log("rack_position (value): " . (isset($data['rack_position']) ? "'" . $data['rack_position'] . "'" : 'NOT SET'));
    error_log("rack_position (empty): " . (empty($data['rack_position']) ? 'YES' : 'NO'));
    error_log("==============================");

    // Validate required fields
    if (!isset($data['label']) || empty($data['label'])) {
      throw new Exception('Switch label is required');
    }

    // DIRECT APPROACH - Create SQL manually with the essential fields including SNMP details
    $sql = "INSERT INTO inventory_switches (
      `label`,
      `switch_ip`,
      `root_password`,
      `rack_id`,
      `rack_position`,
      `size_ru`,
      `status`,
      `city_id`,
      `country_id`,
      `notes`,
      `model_id`,
      `snmp_community`,
      `snmp_version`,
      `snmp_port`
    ) VALUES (
      :label,
      :switch_ip,
      :root_password,
      :rack_id,
      :rack_position,
      :size_ru,
      :status,
      :city_id,
      :country_id,
      :notes,
      :model_id,
      :snmp_community,
      :snmp_version,
      :snmp_port
    )";

    // Prepare the statement
    $sth = $pdo->prepare($sql);

    // BIND ALL VALUES EXPLICITLY - with type handling and defaults

    // Required fields
    $sth->bindValue(':label', $data['label']);
    $sth->bindValue(':status', isset($data['status']) && !empty($data['status']) ? $data['status'] : 'Available');

    // Optional string fields - empty string if not set
    $sth->bindValue(':switch_ip', isset($data['switch_ip']) ? $data['switch_ip'] : '');
    $sth->bindValue(':root_password', isset($data['root_password']) ? $data['root_password'] : '');
    $sth->bindValue(':rack_position', isset($data['rack_position']) ? $data['rack_position'] : '');
    $sth->bindValue(':notes', isset($data['notes']) ? $data['notes'] : '');

    // SNMP fields
    $sth->bindValue(':snmp_community', isset($data['snmp_community']) ? $data['snmp_community'] : '');
    $sth->bindValue(':snmp_version', isset($data['snmp_version']) ? intval($data['snmp_version']) : 2);
    $sth->bindValue(':snmp_port', isset($data['snmp_port']) ? intval($data['snmp_port']) : 161);

    // Optional numeric fields - NULL if not set or empty
    if (isset($data['rack_id']) && !empty($data['rack_id'])) {
      $sth->bindValue(':rack_id', intval($data['rack_id']), PDO::PARAM_INT);
    } else {
      $sth->bindValue(':rack_id', null, PDO::PARAM_NULL);
    }

    if (isset($data['size_ru']) && !empty($data['size_ru'])) {
      $sth->bindValue(':size_ru', intval($data['size_ru']), PDO::PARAM_INT);
    } else {
      $sth->bindValue(':size_ru', null, PDO::PARAM_NULL);
    }

    if (isset($data['city_id']) && !empty($data['city_id'])) {
      $sth->bindValue(':city_id', intval($data['city_id']), PDO::PARAM_INT);
    } else {
      $sth->bindValue(':city_id', null, PDO::PARAM_NULL);
    }

    if (isset($data['country_id']) && !empty($data['country_id'])) {
      $sth->bindValue(':country_id', intval($data['country_id']), PDO::PARAM_INT);
    } else {
      $sth->bindValue(':country_id', null, PDO::PARAM_NULL);
    }

    if (isset($data['model_id']) && !empty($data['model_id'])) {
      $sth->bindValue(':model_id', intval($data['model_id']), PDO::PARAM_INT);
    } else {
      $sth->bindValue(':model_id', null, PDO::PARAM_NULL);
    }

    // Log the final SQL (with parameter values for debugging)
    $logParams = array(
      ':label' => $data['label'],
      ':status' => isset($data['status']) && !empty($data['status']) ? $data['status'] : 'Active',
      ':switch_ip' => isset($data['switch_ip']) ? $data['switch_ip'] : '',
      ':root_password' => isset($data['root_password']) ? '[PASSWORD HIDDEN]' : '',
      ':rack_position' => isset($data['rack_position']) ? $data['rack_position'] : '',
      ':notes' => isset($data['notes']) ? $data['notes'] : '',
      ':rack_id' => isset($data['rack_id']) && !empty($data['rack_id']) ? intval($data['rack_id']) : null,
      ':size_ru' => isset($data['size_ru']) && !empty($data['size_ru']) ? intval($data['size_ru']) : null,
      ':city_id' => isset($data['city_id']) && !empty($data['city_id']) ? intval($data['city_id']) : null,
      ':country_id' => isset($data['country_id']) && !empty($data['country_id']) ? intval($data['country_id']) : null,
      ':model_id' => isset($data['model_id']) && !empty($data['model_id']) ? intval($data['model_id']) : null,
      ':snmp_community' => isset($data['snmp_community']) ? $data['snmp_community'] : '',
      ':snmp_version' => isset($data['snmp_version']) ? intval($data['snmp_version']) : 2,
      ':snmp_port' => isset($data['snmp_port']) ? intval($data['snmp_port']) : 161
    );
    error_log("Final SQL parameters: " . json_encode($logParams));

    // Execute the query
    $sth->execute();

    // Get the new ID
    $switch_id = $pdo->lastInsertId();

    if (!$switch_id) {
      throw new Exception("Failed to get new switch ID");
    }

    // Try to discover ports using SNMP if a community string was provided
    if (isset($data['snmp_community']) && !empty($data['snmp_community'])) {
      try {
        // Note: Port discovery is handled by a separate API endpoint
        // We could call it here, but it's better to let the user explicitly trigger it
        error_log("SNMP community provided for switch $switch_id, but port discovery should be triggered separately");
      } catch (Exception $e) {
        // Log error but don't fail the whole operation
        error_log("SNMP discovery failed: " . $e->getMessage());
      }
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $switch_id,
      'message' => 'Switch added successfully' . (isset($data['snmp_community']) ? ' and ports discovered' : '')
    ]);

  } catch (PDOException $e) {
    error_log("Database error in add_switch: " . $e->getMessage());

    // Check for specific database errors
    if (strpos($e->getMessage(), 'foreign key constraint fails') !== false) {
      $errorMsg = 'Invalid foreign key reference. Please check that rack_id, city_id, country_id, and model_id values are valid.';
    } else if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
      $errorMsg = 'A switch with this information already exists.';
    } else {
      $errorMsg = 'Database error: ' . $e->getMessage();
    }

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to add switch: ' . $errorMsg
    ]);
    exit;
  } catch (Exception $e) {
    error_log("Error in add_switch: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to add switch: ' . $e->getMessage()
    ]);
    exit;
  }
}


// Get switches - simplified
elseif($_GET['f'] == 'get_inventory_switches'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Check if tables exist
    $tablesExist = [
      'inventory_switches' => $pdo->query("SHOW TABLES LIKE 'inventory_switches'")->rowCount() > 0,
      'racks' => $pdo->query("SHOW TABLES LIKE 'racks'")->rowCount() > 0,
      'cities' => $pdo->query("SHOW TABLES LIKE 'cities'")->rowCount() > 0,
      'countries' => $pdo->query("SHOW TABLES LIKE 'countries'")->rowCount() > 0
    ];

    if (!$tablesExist['inventory_switches']) {
      header('Content-Type: application/json');
      echo json_encode([]);
      return;
    }

    // Build query with LEFT JOINs
    $sql = "
      SELECT
        s.*
        " . ($tablesExist['racks'] ? ", r.rack_name" : "") . "
        " . ($tablesExist['cities'] ? ", ci.city AS city_name" : "") . "
        " . ($tablesExist['countries'] ? ", co.country AS country_name" : "") . "
      FROM inventory_switches s
      " . ($tablesExist['racks'] ? "LEFT JOIN racks r ON s.rack_id = r.id" : "") . "
      " . ($tablesExist['cities'] ? "LEFT JOIN cities ci ON s.city_id = ci.id" : "") . "
      " . ($tablesExist['countries'] ? "LEFT JOIN countries co ON ci.country_id = co.id" : "") . "
    ";

    // Execute query
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $switches = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Handle potential missing data
    foreach ($switches as &$switch) {
      $switch['rack_name'] = $switch['rack_name'] ?? 'Unknown';
      $switch['city_name'] = $switch['city_name'] ?? 'Unknown';
      $switch['country_name'] = $switch['country_name'] ?? 'Unknown';
    }

    // Return as JSON
    header('Content-Type: application/json');
    echo json_encode($switches);

  } catch (Exception $e) {
    error_log("Error in get_inventory_switches: " . $e->getMessage());

    // Return empty array on error
    header('Content-Type: application/json');
    echo json_encode([]);
  }
}

// Get storage - simplified
elseif($_GET['f'] == 'get_inventory_storage'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Check if table exists
    $tablesResult = $pdo->query("SHOW TABLES LIKE 'inventory_storage'");
    $tableExists = $tablesResult->rowCount() > 0;

    if (!$tableExists) {
      // Just return empty array if table doesn't exist
      header('Content-Type: application/json');
      echo json_encode([]);
      return;
    }

    // Simple query
    $sql = "SELECT * FROM inventory_storage";
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $storage = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Return as JSON
    header('Content-Type: application/json');
    echo json_encode($storage);

  } catch (Exception $e) {
    error_log("Error in get_inventory_storage: " . $e->getMessage());

    // Return empty array on error
    header('Content-Type: application/json');
    echo json_encode([]);
  }
}

// Get racks - simplified
elseif($_GET['f'] == 'get_racks'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Check if table exists
    $tablesResult = $pdo->query("SHOW TABLES LIKE 'racks'");
    $tableExists = $tablesResult->rowCount() > 0;

    if (!$tableExists) {
      // Just return empty array if table doesn't exist
      header('Content-Type: application/json');
      echo json_encode([]);
      return;
    }

    // Simple query
    $sql = "SELECT * FROM racks";
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $racks = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Return as JSON
    header('Content-Type: application/json');
    echo json_encode($racks);

  } catch (Exception $e) {
    error_log("Error in get_racks: " . $e->getMessage());

    // Return empty array on error
    header('Content-Type: application/json');
    echo json_encode([]);
  }
}

// Get cities - simplified
elseif($_GET['f'] == 'get_cities'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Check if table exists
    $tablesResult = $pdo->query("SHOW TABLES LIKE 'cities'");
    $tableExists = $tablesResult->rowCount() > 0;

    if (!$tableExists) {
      // Just return empty array if table doesn't exist
      header('Content-Type: application/json');
      echo json_encode([]);
      return;
    }

    // Simple query
    $sql = "SELECT * FROM cities";
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $cities = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Return as JSON
    header('Content-Type: application/json');
    echo json_encode($cities);

  } catch (Exception $e) {
    error_log("Error in get_cities: " . $e->getMessage());

    // Return empty array on error
    header('Content-Type: application/json');
    echo json_encode([]);
  }
}

// Get countries - simplified
elseif($_GET['f'] == 'get_countries'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Check if table exists
    $tablesResult = $pdo->query("SHOW TABLES LIKE 'countries'");
    $tableExists = $tablesResult->rowCount() > 0;

    if (!$tableExists) {
      // Just return empty array if table doesn't exist
      header('Content-Type: application/json');
      echo json_encode([]);
      return;
    }

    // Simple query
    $sql = "SELECT * FROM countries";
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $countries = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Return as JSON
    header('Content-Type: application/json');
    echo json_encode($countries);

  } catch (Exception $e) {
    error_log("Error in get_countries: " . $e->getMessage());

    // Return empty array on error
    header('Content-Type: application/json');
    echo json_encode([]);
  }
}



// Get a specific blade server by ID
elseif($_GET['f'] == 'get_blade_server_by_id'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get server ID from request
    $data = json_decode(file_get_contents('php://input'), true);
    $id = isset($data['id']) ? $data['id'] : null;

    if (!$id) {
      throw new Exception('Server ID is required');
    }

    // Check if table exists
    $tablesResult = $pdo->query("SHOW TABLES LIKE 'blade_server_inventory'");
    $tableExists = $tablesResult->rowCount() > 0;

    if (!$tableExists) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'Blade server inventory table does not exist'
      ]);
      return;
    }

    // Query for the server
    $sql = "SELECT
    bsi.*,
    dc.cpu AS cpu_name,
    sw.label AS switch_name
  FROM blade_server_inventory bsi
  LEFT JOIN dedicated_cpu dc ON bsi.cpu = dc.id
  LEFT JOIN inventory_switches sw ON bsi.switch_id = sw.id
  WHERE bsi.id = :id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':id', $id);
    $sth->execute();

    // Check if server exists
    if ($sth->rowCount() === 0) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'Blade server not found'
      ]);
      return;
    }

    // Fetch server
    $server = $sth->fetch(PDO::FETCH_ASSOC);

    // Return server
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'server' => $server
    ]);

  } catch (Exception $e) {
    error_log("Error in get_blade_server_by_id: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to get blade server: ' . $e->getMessage()
    ]);
  }
}



// Replace the add_blade_server_inventory function in api_admin_inventory.php with this enhanced version:

elseif($_GET['f'] == 'add_blade_server_inventory'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log input data for debugging
    error_log("Add blade server input: " . json_encode($data));

    // Validate required fields
    if (!isset($data['label'])) {
      throw new Exception('Server label is required');
    }

    // Set default status if not provided
    if (!isset($data['status'])) {
      $data['status'] = 'Available';
    }

    // Check if chassis_id is provided and valid
    $chassis_id = null;
    $update_chassis = false;

    if (isset($data['chassis_id']) && !empty($data['chassis_id'])) {
      $chassis_id = intval($data['chassis_id']);

      // Verify that the chassis exists
      $checkChassisSql = "SELECT * FROM inventory_chassis WHERE id = :chassis_id";
      $checkChassisSth = $pdo->prepare($checkChassisSql);
      $checkChassisSth->bindValue(':chassis_id', $chassis_id, PDO::PARAM_INT);
      $checkChassisSth->execute();

      if ($checkChassisSth->rowCount() === 0) {
        throw new Exception("Chassis with ID $chassis_id not found");
      }

      // Get the chassis data to check which bays are available
      $chassisData = $checkChassisSth->fetch(PDO::FETCH_ASSOC);

      // Check if any bay is available
      $availableBay = null;
      for ($i = 1; $i <= 4; $i++) { // Most chassis have 4 bays
        $bayField = "bay$i";
        if (empty($chassisData[$bayField]) || $chassisData[$bayField] === '0' || $chassisData[$bayField] === 0 || $chassisData[$bayField] === null) {
          $availableBay = $bayField;
          break;
        }
      }

      if ($availableBay === null) {
        throw new Exception("No available bays in chassis $chassis_id. All bays are already occupied.");
      }

      // We'll update the chassis after adding the blade server
      $update_chassis = true;
      $target_bay = $availableBay;

      error_log("Will assign new blade server to chassis $chassis_id in $target_bay");
    }

    // Begin transaction for atomic operations
    $pdo->beginTransaction();

    // Prepare insert fields and values for the blade server
    $fields = [];
    $values = [];
    $params = [];

    // Define allowed fields based on table structure
    $allowedFields = [
      'label', 'cpu', 'ram', 'switch_id',
      'port1', 'port1_speed', 'port2', 'port2_speed',
      'port3', 'port3_speed', 'port4', 'port4_speed',
      'bay1', 'bay2', 'bay3', 'bay4', 'bay5',
      'bay6', 'bay7', 'bay8', 'bay9', 'bay10',
      'mac', 'ipmi', 'root', 'user', 'notes', 'status','chassis_id',
      'order_id', 'main_ip', 'additional_ips', 'ipmi_root_pass', 'ipmi_user_pass', 'idrac_version'
    ];

    // Process numeric fields correctly
    $numericFields = ['cpu', 'ram', 'switch_id',
                      'port1', 'port1_speed', 'port2', 'port2_speed',
                      'port3', 'port3_speed', 'port4', 'port4_speed',
                      'bay1', 'bay2', 'bay3', 'bay4', 'bay5',
                      'bay6', 'bay7', 'bay8', 'bay9', 'bay10',
                      'chassis_id', 'order_id', 'idrac_version'];

    foreach ($allowedFields as $field) {
      if (array_key_exists($field, $data) && $data[$field] !== '') {
        $fields[] = "`$field`";
        $values[] = ":$field";

        // Convert numeric fields to integers when appropriate
        if (in_array($field, $numericFields)) {
          if ($data[$field] === '0' || $data[$field] === 0) {
            $params[":$field"] = 0;
          } else if (is_numeric($data[$field])) {
            $params[":$field"] = intval($data[$field]);
          } else {
            // If the value is not numeric, log it and skip this field
            error_log("Warning: Non-numeric value '{$data[$field]}' for field '$field' skipped");
            array_pop($fields);
            array_pop($values);
            continue;
          }
        } else {
          $params[":$field"] = $data[$field];
        }
      }
    }

    // Check if we have any fields to insert
    if (empty($fields)) {
      throw new Exception('No valid fields provided');
    }

    // Build the SQL for blade server insertion
    $sql = "INSERT INTO blade_server_inventory (" . implode(", ", $fields) . ")
            VALUES (" . implode(", ", $values) . ")";

    // Log SQL for debugging
    error_log("SQL query for blade server: " . $sql);
    error_log("Parameters: " . json_encode($params));

    // Execute the query with detailed error handling
    try {
      $sth = $pdo->prepare($sql);
      foreach ($params as $key => $value) {
        if (is_null($value)) {
          $sth->bindValue($key, null, PDO::PARAM_NULL);
        } else if (is_int($value)) {
          $sth->bindValue($key, $value, PDO::PARAM_INT);
        } else {
          $sth->bindValue($key, $value, PDO::PARAM_STR);
        }
      }
      $sth->execute();
    } catch (PDOException $e) {
      error_log("SQL Error: " . $e->getMessage());
      $pdo->rollBack();
      throw new Exception("Database error: " . $e->getMessage());
    }

    // Get the new blade server ID
    $newBladeServerId = $pdo->lastInsertId();

    // Now update the chassis if needed
    if ($update_chassis && $newBladeServerId) {
      try {
        $updateChassisSql = "UPDATE inventory_chassis SET `$target_bay` = :blade_id WHERE id = :chassis_id";
        $updateChassisSth = $pdo->prepare($updateChassisSql);
        $updateChassisSth->bindValue(':blade_id', $newBladeServerId, PDO::PARAM_INT);
        $updateChassisSth->bindValue(':chassis_id', $chassis_id, PDO::PARAM_INT);
        $updateChassisSth->execute();

        if ($updateChassisSth->rowCount() === 0) {
          error_log("Failed to update chassis bay assignment. No rows affected.");
          // Don't throw exception here, we'll still consider the blade server creation successful
        } else {
          error_log("Successfully updated chassis $chassis_id $target_bay with blade server ID $newBladeServerId");
        }
      } catch (PDOException $e) {
        error_log("Error updating chassis bay assignment: " . $e->getMessage());
        // Continue with transaction - don't fail just because chassis update failed
      }
    }

    // Commit the transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $newBladeServerId,
      'message' => 'Blade server added successfully',
      'chassis_updated' => $update_chassis,
      'chassis_id' => $chassis_id,
      'bay' => $update_chassis ? $target_bay : null
    ]);

  } catch (Exception $e) {
    // Rollback transaction if active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in add_blade_server: " . $e->getMessage());

    // Return detailed error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to add blade server: ' . $e->getMessage()
    ]);
  }
}


// Replace the assign_port_to_server_modal function

elseif($_GET['f'] == 'assign_port_to_server_modal'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log the request data for debugging
    error_log("Port assignment request data: " . json_encode($data));

    // Validate required fields
    if (!isset($data['port_id']) || empty($data['port_id'])) {
      throw new Exception('Port ID is required');
    }
    if (!isset($data['device_id']) || empty($data['device_id'])) {
      throw new Exception('Device ID is required');
    }
    if (!isset($data['device_type']) || empty($data['device_type'])) {
      throw new Exception('Device type is required');
    }

    $port_id = intval($data['port_id']);
    $device_id = intval($data['device_id']);
    $device_type = $data['device_type'];

    // Get port information
    $getPortSql = "SELECT * FROM inventory_switch_ports WHERE id = :port_id";
    $getPortSth = $pdo->prepare($getPortSql);
    $getPortSth->bindValue(':port_id', $port_id);
    $getPortSth->execute();

    $port = $getPortSth->fetch(PDO::FETCH_ASSOC);

    if (!$port) {
      throw new Exception('Port not found');
    }

          error_log("Port found: " . json_encode($port));

      if ($port['connected_device_id'] && $port['connected_device_id'] != $device_id) {
        throw new Exception('Port is already assigned to another device');
      }

      if ($port['status'] === 'Disabled') {
        throw new Exception('Port is not available for assignment (status: ' . $port['status'] . ')');
      }

      // Begin transaction
      $pdo->beginTransaction();

      // Update port assignment - only update connection fields, leave status unchanged
      $updatePortSql = "UPDATE inventory_switch_ports SET
                        connected_device_id = :device_id,
                        connected_device_type = :device_type
                      WHERE id = :port_id";
    $updatePortSth = $pdo->prepare($updatePortSql);
    $updatePortSth->bindValue(':port_id', $port_id);
    $updatePortSth->bindValue(':device_id', $device_id);
    $updatePortSth->bindValue(':device_type', $device_type);
    $updatePortSth->execute();

    // Also update the server record
    if ($device_type === 'blade') {
      $serverTable = 'blade_server_inventory';
    } else if ($device_type === 'dedicated') {
      $serverTable = 'inventory_dedicated_servers';
    } else {
      throw new Exception('Unsupported device type');
    }

    // Verify server exists
    $getServerSql = "SELECT * FROM $serverTable WHERE id = :device_id";
    $getServerSth = $pdo->prepare($getServerSql);
    $getServerSth->bindValue(':device_id', $device_id);
    $getServerSth->execute();

    $server = $getServerSth->fetch(PDO::FETCH_ASSOC);

    if (!$server) {
      // Rollback and exit
      $pdo->rollBack();
      throw new Exception("Server not found with ID $device_id in table $serverTable");
    }

    error_log("Server found: " . json_encode($server));

    // Determine which port field to use, checking if the frontend specified a port field
    if (isset($data['port_field']) && in_array($data['port_field'], ['port1', 'port2', 'port3', 'port4'])) {
      $portField = $data['port_field'];
    } else {
      // Fallback to automatic determination if not specified
      if (empty($server['port1'])) {
        $portField = 'port1';
      } else if (empty($server['port2'])) {
        $portField = 'port2';
      } else if (empty($server['port3'])) {
        $portField = 'port3';
      } else if (empty($server['port4'])) {
        $portField = 'port4';
      } else {
        // All ports are already assigned
        $pdo->rollBack();
        throw new Exception('All NIC ports are already assigned. Please remove a connection first.');
      }
    }

    $portSpeedField = $portField . '_speed';

    // Log update details
    error_log("Updating server with port: Port field=$portField, Speed field=$portSpeedField");
    error_log("Port ID being stored: " . $port_id);

    // Update the server with the port information - store the port ID for proper foreign key relationship
    $updateServerSql = "UPDATE $serverTable SET
                        switch_id = :switch_id,
                        $portField = :port_id,
                        $portSpeedField = :port_speed
                      WHERE id = :device_id";
    $updateServerSth = $pdo->prepare($updateServerSql);
    $updateServerSth->bindValue(':switch_id', $port['switch_id']);
    $updateServerSth->bindValue(':port_id', $port_id, PDO::PARAM_INT);
    $updateServerSth->bindValue(':port_speed', $port['max_speed']);
    $updateServerSth->bindValue(':device_id', $device_id);

    try {
      $updateResult = $updateServerSth->execute();
      error_log("Update server result: " . ($updateResult ? "success" : "failed"));
    } catch (PDOException $e) {
      error_log("Error updating server: " . $e->getMessage());
      $pdo->rollBack();
      throw new Exception("Database error when updating server: " . $e->getMessage());
    }

    // Commit transaction
    $pdo->commit();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Port assigned successfully',
      'port_field' => $portField,
      'switch_id' => $port['switch_id'],
      'switch_updated' => true,
      'port_id' => $port_id,
      'port_number' => $port['port_number'] // Still return port number for display purposes
    ]);

  } catch (Exception $e) {
    // Rollback transaction if it was started
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Port assignment error: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}


  // Now in the assign_port_to_server_modal function, add more error logging:

  elseif($_GET['f'] == 'assign_port_to_server_modal'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get data
      $data = json_decode(file_get_contents('php://input'), true);

      // Log the request data for debugging
      error_log("Port assignment request data: " . json_encode($data));

      // Validate required fields
      if (!isset($data['port_id']) || empty($data['port_id'])) {
        throw new Exception('Port ID is required');
      }
      if (!isset($data['device_id']) || empty($data['device_id'])) {
        throw new Exception('Device ID is required');
      }
      if (!isset($data['device_type']) || empty($data['device_type'])) {
        throw new Exception('Device type is required');
      }

      $port_id = intval($data['port_id']);
      $device_id = intval($data['device_id']);
      $device_type = $data['device_type'];

      // Get port information
      $getPortSql = "SELECT * FROM inventory_switch_ports WHERE id = :port_id";
      $getPortSth = $pdo->prepare($getPortSql);
      $getPortSth->bindValue(':port_id', $port_id);
      $getPortSth->execute();

      $port = $getPortSth->fetch(PDO::FETCH_ASSOC);

      if (!$port) {
        throw new Exception('Port not found');
      }

      error_log("Port found: " . json_encode($port));

      if ($port['connected_device_id'] || $port['status'] === 'Disabled') {
        throw new Exception('Port is not available for assignment (status: ' . $port['status'] . ')');
      }

      // Begin transaction
      $pdo->beginTransaction();

      // Update port assignment - only update connection fields, leave status unchanged
      $updatePortSql = "UPDATE inventory_switch_ports SET
                        connected_device_id = :device_id,
                        connected_device_type = :device_type
                      WHERE id = :port_id";
      $updatePortSth = $pdo->prepare($updatePortSql);
      $updatePortSth->bindValue(':port_id', $port_id);
      $updatePortSth->bindValue(':device_id', $device_id);
      $updatePortSth->bindValue(':device_type', $device_type);
      $updatePortSth->execute();

      // Also update the server record
      if ($device_type === 'blade') {
        $serverTable = 'blade_server_inventory';
      } else if ($device_type === 'dedicated') {
        $serverTable = 'inventory_dedicated_servers';
      } else {
        throw new Exception('Unsupported device type');
      }

      // Verify server exists
      $getServerSql = "SELECT * FROM $serverTable WHERE id = :device_id";
      $getServerSth = $pdo->prepare($getServerSql);
      $getServerSth->bindValue(':device_id', $device_id);
      $getServerSth->execute();

      $server = $getServerSth->fetch(PDO::FETCH_ASSOC);

      if (!$server) {
        // Rollback and exit
        $pdo->rollBack();
        throw new Exception("Server not found with ID $device_id in table $serverTable");
      }

      error_log("Server found: " . json_encode($server));

      // Determine which port field to use, checking if the frontend specified a port field
      if (isset($data['port_field']) && in_array($data['port_field'], ['port1', 'port2', 'port3', 'port4'])) {
        $portField = $data['port_field'];
      } else {
        // Fallback to automatic determination if not specified
        if (empty($server['port1'])) {
          $portField = 'port1';
        } else if (empty($server['port2'])) {
          $portField = 'port2';
        } else if (empty($server['port3'])) {
          $portField = 'port3';
        } else if (empty($server['port4'])) {
          $portField = 'port4';
        } else {
          // All ports are already assigned
          $pdo->rollBack();
          throw new Exception('All NIC ports are already assigned. Please remove a connection first.');
        }
      }

      $portSpeedField = $portField . '_speed';

      // Log update details
      error_log("Updating server with port: Port field=$portField, Speed field=$portSpeedField, Switch ID={$port['switch_id']}, Port number={$port['port_number']}");

      // Update the server with the port information
      // Always set the switch_id from the port's switch_id
      $updateServerSql = "UPDATE $serverTable SET
                          switch_id = :switch_id,
                          $portField = :port_number,
                          $portSpeedField = :port_speed
                        WHERE id = :device_id";
      $updateServerSth = $pdo->prepare($updateServerSql);
      $updateServerSth->bindValue(':switch_id', $port['switch_id']);
      $updateServerSth->bindValue(':port_number', $port['port_number']);
      $updateServerSth->bindValue(':port_speed', $port['max_speed']);
      $updateServerSth->bindValue(':device_id', $device_id);

      try {
        $updateResult = $updateServerSth->execute();
        error_log("Update server result: " . ($updateResult ? "success" : "failed"));
      } catch (PDOException $e) {
        error_log("Error updating server: " . $e->getMessage());
        $pdo->rollBack();
        throw new Exception("Database error when updating server: " . $e->getMessage());
      }

      // Commit transaction
      $pdo->commit();

      // Return success
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'Port assigned successfully',
        'port_field' => $portField,
        'switch_id' => $port['switch_id'],
        'switch_updated' => true
      ]);

    } catch (Exception $e) {
      // Rollback transaction if it was started
      if ($pdo->inTransaction()) {
        $pdo->rollBack();
      }

      error_log("Port assignment error: " . $e->getMessage());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
      ]);
    }
  }

// Get blade servers by chassis ID
elseif($_GET['f'] == 'get_blade_servers_by_chassis'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get chassis ID from request
    $data = json_decode(file_get_contents('php://input'), true);
    $chassis_id = isset($data['chassis_id']) ? $data['chassis_id'] : null;

    if (!$chassis_id) {
      throw new Exception('Chassis ID is required');
    }

    // Check if table exists
    $tablesResult = $pdo->query("SHOW TABLES LIKE 'blade_server_inventory'");
    $tableExists = $tablesResult->rowCount() > 0;

    if (!$tableExists) {
      // Return empty array if table doesn't exist
      header('Content-Type: application/json');
      echo json_encode([]);
      return;
    }

    // Query for the servers
    $sql = "SELECT * FROM blade_server_inventory WHERE chassis_id = :chassis_id ORDER BY id ASC";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':chassis_id', $chassis_id);
    $sth->execute();

    // Fetch servers
    $servers = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Return servers
    header('Content-Type: application/json');
    echo json_encode($servers);

  } catch (Exception $e) {
    error_log("Error in get_blade_servers_by_chassis: " . $e->getMessage());

    // Return empty array on error
    header('Content-Type: application/json');
    echo json_encode([]);
  }
}

// Get chassis - simplified
elseif($_GET['f'] == 'get_inventory_chassis'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Check if tables exist
    $chassisTableExists = $pdo->query("SHOW TABLES LIKE 'inventory_chassis'")->rowCount() > 0;
    $bladeTableExists = $pdo->query("SHOW TABLES LIKE 'blade_server_inventory'")->rowCount() > 0;

    if (!$chassisTableExists) {
      // Just return empty array if chassis table doesn't exist
      header('Content-Type: application/json');
      echo json_encode([]);
      return;
    }

    // Build query with LEFT JOINs for blade labels
    $sql = "

    SELECT
      c.*,
      b1.label AS bay1_label,
      b2.label AS bay2_label,
      b3.label AS bay3_label,
      b4.label AS bay4_label,
      r.rack_name,
      ci.city AS city_name,
      co.country AS country_name
    FROM inventory_chassis c
    LEFT JOIN blade_server_inventory b1 ON c.bay1 = b1.id
    LEFT JOIN blade_server_inventory b2 ON c.bay2 = b2.id
    LEFT JOIN blade_server_inventory b3 ON c.bay3 = b3.id
    LEFT JOIN blade_server_inventory b4 ON c.bay4 = b4.id
    LEFT JOIN racks r ON c.rack_id = r.id
    LEFT JOIN cities ci ON c.city_id = ci.id
    LEFT JOIN countries co ON ci.country_id = co.id
    ";

    // Prepare and execute query
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $chassis = $sth->fetchAll(PDO::FETCH_ASSOC);

    // If blade table doesn't exist, modify response
    if (!$bladeTableExists) {
      // Remove bay label fields
      foreach ($chassis as &$item) {
        for ($i = 1; $i <= 10; $i++) {
          unset($item["bay{$i}_label"]);
        }
      }
    }

    // Return as JSON
    header('Content-Type: application/json');
    echo json_encode($chassis);

  } catch (Exception $e) {
    error_log("Error in get_inventory_chassis: " . $e->getMessage());

    // Return empty array on error
    header('Content-Type: application/json');
    echo json_encode([]);
  }
}

// Get inventory statistics - simplified version that won't fail
elseif($_GET['f'] == 'get_inventory_stats'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Create default stats
    $stats = [
      'totalDedicated' => 0,
      'totalBlades' => 0,
      'totalChassis' => 0,
      'totalSwitches' => 0,
      'activeServers' => 0,
      'defectServers' => 0,
      'inuseServers' => 0,
      'storageCapacity' => '0 GB'
    ];

    // Try to count dedicated servers if table exists
    try {
      $dedicatedResult = $pdo->query("SELECT
        COUNT(*) AS total,
        SUM(status = 'Available') AS active,
        SUM(status = 'Defect') AS defect,
        SUM(status = 'In use') AS inuse
        FROM inventory_dedicated_servers");

      if ($dedicatedResult) {
        $dedicatedStats = $dedicatedResult->fetch(PDO::FETCH_ASSOC);

        $stats['totalDedicated'] = $dedicatedStats['total'];
        $stats['activeServers'] += $dedicatedStats['active'];
        $stats['defectServers'] += $dedicatedStats['defect'];
        $stats['inuseServers'] += $dedicatedStats['inuse'];
      }
    } catch (Exception $e) {
      error_log("Error counting dedicated servers: " . $e->getMessage());
      // Continue with other stats - don't let this error stop us
    }

    // Try to count blade servers
    try {
      $bladeResult = $pdo->query("SELECT
        COUNT(*) AS total,
        SUM(status = 'Available') AS active,
        SUM(status = 'Defect') AS defect,
        SUM(status = 'In use') AS inuse
        FROM blade_server_inventory");

      if ($bladeResult) {
        $bladeStats = $bladeResult->fetch(PDO::FETCH_ASSOC);

        $stats['totalBlades'] = $bladeStats['total'];
        $stats['activeServers'] += $bladeStats['active'];
        $stats['defectServers'] += $bladeStats['defect'];
        $stats['inuseServers'] += $bladeStats['inuse'];
      }
    } catch (Exception $e) {
      error_log("Error counting blade servers: " . $e->getMessage());
    }

    // Try to count chassis
    try {
      $chassisResult = $pdo->query("SELECT COUNT(*) FROM inventory_chassis");
      if ($chassisResult) {
        $stats['totalChassis'] = $chassisResult->fetchColumn();
      }
    } catch (Exception $e) {
      error_log("Error counting chassis: " . $e->getMessage());
    }

    // Try to count switches
    try {
      $switchResult = $pdo->query("SELECT COUNT(*) FROM inventory_switches");
      if ($switchResult) {
        $stats['totalSwitches'] = $switchResult->fetchColumn();
      }
    } catch (Exception $e) {
      error_log("Error counting switches: " . $e->getMessage());
    }

    // Return stats as JSON
    header('Content-Type: application/json');
    echo json_encode($stats);

  } catch (Exception $e) {
    error_log("Error in get_inventory_stats: " . $e->getMessage());
    // Return a default set of stats instead of an error
    header('Content-Type: application/json');
    echo json_encode([
      'totalDedicated' => 0,
      'totalBlades' => 0,
      'totalChassis' => 0,
      'totalSwitches' => 0,
      'activeServers' => 0,
      'defectServers' => 0,
      'inuseServers' => 0,
      'storageCapacity' => '0 GB'
    ]);
  }
}



// New function to get RAM configurations
elseif($_GET['f'] == 'get_ram_configurations'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Build a simple query to get all RAM configuration records
    $sql = "SELECT * FROM ram_configurations ORDER BY size ASC";

    // Execute query
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $ramConfigs = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Return RAM configs as JSON
    header('Content-Type: application/json');
    echo json_encode($ramConfigs);

  } catch (Exception $e) {
    error_log("Error in get_ram_configurations: " . $e->getMessage());

    // Check if the table exists, if not return empty array
    try {
      $tableExists = $pdo->query("SHOW TABLES LIKE 'ram_configurations'")->rowCount() > 0;
      if (!$tableExists) {
        header('Content-Type: application/json');
        echo json_encode([]);
        return;
      }
    } catch (Exception $checkError) {
      // If we can't even check if the table exists, there's a serious DB issue
      error_log("Error checking if ram_configurations table exists: " . $checkError->getMessage());
    }

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'error' => 'Failed to fetch RAM configuration options',
      'details' => $e->getMessage()
    ]);
  }
}

// New function to add a CPU model
elseif($_GET['f'] == 'add_cpu_model'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['cpu']) || empty($data['cpu'])) {
      throw new Exception('CPU model name is required');
    }

    // Check if CPU already exists
    $checkSql = "SELECT COUNT(*) FROM dedicated_cpu WHERE cpu = :cpu";
    $checkSth = $pdo->prepare($checkSql);
    $checkSth->bindValue(':cpu', $data['cpu']);
    $checkSth->execute();

    if ($checkSth->fetchColumn() > 0) {
      // CPU already exists
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'This CPU model already exists'
      ]);
      return;
    }

    // Insert new CPU
    $insertSql = "INSERT INTO dedicated_cpu (cpu) VALUES (:cpu)";
    $insertSth = $pdo->prepare($insertSql);
    $insertSth->bindValue(':cpu', $data['cpu']);
    $insertSth->execute();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $pdo->lastInsertId(),
      'message' => 'CPU model added successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_cpu_model: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to add CPU model: ' . $e->getMessage()
    ]);
  }
}

// New function to add a RAM configuration
elseif($_GET['f'] == 'add_ram_configuration'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['size']) || !isset($data['description'])) {
      throw new Exception('RAM size and description are required');
    }

    // Check if configuration already exists
    $checkSql = "SELECT COUNT(*) FROM ram_configurations WHERE size = :size AND description = :description";
    $checkSth = $pdo->prepare($checkSql);
    $checkSth->bindValue(':size', $data['size']);
    $checkSth->bindValue(':description', $data['description']);
    $checkSth->execute();

    if ($checkSth->fetchColumn() > 0) {
      // RAM configuration already exists
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'This RAM configuration already exists'
      ]);
      return;
    }

    // Insert new RAM configuration
    $insertSql = "INSERT INTO ram_configurations (size, description) VALUES (:size, :description)";
    $insertSth = $pdo->prepare($insertSql);
    $insertSth->bindValue(':size', $data['size']);
    $insertSth->bindValue(':description', $data['description']);
    $insertSth->execute();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $pdo->lastInsertId(),
      'message' => 'RAM configuration added successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_ram_configuration: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to add RAM configuration: ' . $e->getMessage()
    ]);
  }
}

// New function to add a switch model
elseif($_GET['f'] == 'add_switch_model'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['name']) || empty($data['name'])) {
      throw new Exception('Switch model name is required');
    }

    if (!isset($data['size']) || empty($data['size']) || !is_numeric($data['size'])) {
      throw new Exception('Switch model size is required and must be a number');
    }

    $name = trim($data['name']);
    $size = intval($data['size']);

    // Validate size range
    if ($size < 1 || $size > 50) {
      throw new Exception('Switch model size must be between 1 and 50 U');
    }

    // Check if switch model already exists
    $checkSql = "SELECT COUNT(*) FROM inventory_switch_model WHERE name = :name";
    $checkSth = $pdo->prepare($checkSql);
    $checkSth->bindValue(':name', $name);
    $checkSth->execute();

    if ($checkSth->fetchColumn() > 0) {
      // Switch model already exists
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'This switch model already exists'
      ]);
      return;
    }

    // Insert new switch model
    $insertSql = "INSERT INTO inventory_switch_model (name, size) VALUES (:name, :size)";
    $insertSth = $pdo->prepare($insertSql);
    $insertSth->bindValue(':name', $name);
    $insertSth->bindValue(':size', $size, PDO::PARAM_INT);
    $insertSth->execute();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $pdo->lastInsertId(),
      'message' => 'Switch model added successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_switch_model: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to add switch model: ' . $e->getMessage()
    ]);
  }
}


// Function 1: get_inventory_dedicated - Gets all dedicated servers with related info
elseif($_GET['f'] == 'get_inventory_dedicated'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Check if related tables exist
    $tablesExist = [
      'ram_configurations' => $pdo->query("SHOW TABLES LIKE 'ram_configurations'")->rowCount() > 0,
      'dedicated_cpu' => $pdo->query("SHOW TABLES LIKE 'dedicated_cpu'")->rowCount() > 0,
      'inventory_switches' => $pdo->query("SHOW TABLES LIKE 'inventory_switches'")->rowCount() > 0
    ];

    // Build the main query with all necessary joins
    $sql = "
      SELECT
        ds.*,
        COALESCE(c.city, 'Unknown') AS city_name,
        COALESCE(c.datacenter, 'Unknown') AS datacenter,
        COALESCE(co.country, 'Unknown') AS country_name,
        COALESCE(r.rack_name, 'Unknown') AS rack_name,
        o.id AS order_id,
        o.owner_id AS client_id,
        CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) AS client_name,
        u.company_name
    ";

    // Add CPU name if the table exists
    if ($tablesExist['dedicated_cpu']) {
      $sql .= ", dc.cpu AS cpu_name";
    }

    // Add RAM description if the table exists
    if ($tablesExist['ram_configurations']) {
      $sql .= ", rc.description AS ram_description";
    }

    // Add switch name if the table exists
    if ($tablesExist['inventory_switches']) {
      $sql .= ", sw.label AS switch_name";
    }

    // Main table and basic joins
    $sql .= "
      FROM inventory_dedicated_servers ds
      LEFT JOIN cities c ON ds.city_id = c.id
      LEFT JOIN countries co ON c.country_id = co.id
      LEFT JOIN racks r ON ds.rack_id = r.id
      LEFT JOIN orders o ON ds.order_id = o.id
      LEFT JOIN users u ON o.owner_id = u.id
    ";

    // Add CPU join if the table exists
    if ($tablesExist['dedicated_cpu']) {
      $sql .= " LEFT JOIN dedicated_cpu dc ON ds.cpu = dc.id";
    }

    // Add RAM join if the table exists
    if ($tablesExist['ram_configurations']) {
      $sql .= " LEFT JOIN ram_configurations rc ON ds.ram = rc.id";
    }

    // Add switch join if the table exists
    if ($tablesExist['inventory_switches']) {
      $sql .= " LEFT JOIN inventory_switches sw ON ds.switch_id = sw.id";
    }

    // Add default WHERE clause
    $sql .= " WHERE 1=1";

    // Get filter parameters
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $location = isset($_GET['location']) ? $_GET['location'] : '';
    $search = isset($_GET['search']) ? $_GET['search'] : '';

    // Add filters if specified
    if ($status && $status != 'All') {
      $sql .= " AND ds.status = " . $pdo->quote($status);
    }

    // Add search conditions
    if ($search) {
      $sql .= " AND (
        ds.label LIKE " . $pdo->quote("%$search%") . " OR
        ds.cpu LIKE " . $pdo->quote("%$search%") . " OR
        ds.notes LIKE " . $pdo->quote("%$search%") . " OR
        COALESCE(c.city, '') LIKE " . $pdo->quote("%$search%") . " OR
        COALESCE(co.country, '') LIKE " . $pdo->quote("%$search%") . " OR
        CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) LIKE " . $pdo->quote("%$search%") . " OR
        COALESCE(u.company_name, '') LIKE " . $pdo->quote("%$search%") . " OR
        CAST(o.id AS CHAR) LIKE " . $pdo->quote("%$search%") . "
      )";
    }

    // Add location filter
    if ($location && $location != 'All') {
      $sql .= " AND CONCAT(COALESCE(c.city, ''), ', ', COALESCE(co.country, '')) = " . $pdo->quote($location);
    }

    // Add sorting
    $sortField = isset($_GET['sortField']) ? $_GET['sortField'] : 'id';
    $sortDirection = isset($_GET['sortDirection']) ? strtoupper($_GET['sortDirection']) : 'ASC';

    // Validate sort direction
    if (!in_array($sortDirection, ['ASC', 'DESC'])) {
      $sortDirection = 'ASC';
    }

    // Validate sort fields to prevent SQL injection
    $validSortFields = [
      'id' => 'ds.id',
      'label' => 'ds.label',
      'cpu' => 'ds.cpu',
      'status' => 'ds.status',
      'city_name' => 'c.city',
      'country_name' => 'co.country',
      'datacenter' => 'c.datacenter',
      'order_id' => 'o.id',
      'client_name' => 'u.first_name'
    ];

    // Determine sort field
    $sortColumn = isset($validSortFields[$sortField]) ? $validSortFields[$sortField] : 'ds.id';
    $sql .= " ORDER BY $sortColumn $sortDirection";

    // Log the query for debugging
    error_log("get_inventory_dedicated SQL: " . $sql);

    // Execute query
    $sth = $pdo->prepare($sql);
    $executeResult = $sth->execute();

    // Fetch results
    $servers = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Return servers as JSON
    header('Content-Type: application/json');
    echo json_encode($servers);

  } catch (Exception $e) {
    error_log("Error in get_inventory_dedicated: " . $e->getMessage());

    // Return error as JSON with more details
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'error' => 'Failed to fetch dedicated servers',
      'details' => $e->getMessage()
    ]);
  }
}

// Function 2: get_blade_server_inventory - Gets all blade servers with related info
elseif($_GET['f'] == 'get_blade_server_inventory'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Check if related tables exist
    $tablesExist = [
      'ram_configurations' => $pdo->query("SHOW TABLES LIKE 'ram_configurations'")->rowCount() > 0,
      'dedicated_cpu' => $pdo->query("SHOW TABLES LIKE 'dedicated_cpu'")->rowCount() > 0,
      'inventory_switches' => $pdo->query("SHOW TABLES LIKE 'inventory_switches'")->rowCount() > 0,
      'storage' => $pdo->query("SHOW TABLES LIKE 'storage'")->rowCount() > 0
    ];

    // Get sorting parameters
    $sortField = isset($_GET['sortField']) ? $_GET['sortField'] : 'id';
    $sortDirection = isset($_GET['sortDirection']) ? strtoupper($_GET['sortDirection']) : 'ASC';

    // Validate sort direction
    if (!in_array($sortDirection, ['ASC', 'DESC'])) {
      $sortDirection = 'ASC';
    }

    // Get filter parameters
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? $_GET['search'] : '';

    // Build query with LEFT JOINs for additional information
    $sql = "SELECT
          bsi.*,
          ch.id AS chassis_id,
          ch.label AS chassis_label,
          c.id AS city_id,
          c.city AS city_name,
          co.id AS country_id,
          co.country AS country_name,
          c.datacenter AS datacenter,
          r.id AS rack_id,
          r.rack_name AS rack_name,
          r.size AS rack_size,
          ch.position AS rack_position,
          o.id AS order_id,
          o.owner_id AS client_id,
          CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) AS client_name,
          u.company_name";

    // Add CPU name if the table exists
    if ($tablesExist['dedicated_cpu']) {
      $sql .= ", dc.cpu AS cpu_name";
    }

    // Add RAM description if the table exists
    if ($tablesExist['ram_configurations']) {
      $sql .= ", rc.description AS ram_description";
    }

    // Add switch name if the table exists
    if ($tablesExist['inventory_switches']) {
      $sql .= ", sw.label AS switch_name";
    }

    // Add storage name columns if storage table exists
    if ($tablesExist['storage']) {
      $sql .= ",
        COALESCE(s1.name, bsi.bay1) AS bay1_name,
        COALESCE(s2.name, bsi.bay2) AS bay2_name,
        COALESCE(s3.name, bsi.bay3) AS bay3_name,
        COALESCE(s4.name, bsi.bay4) AS bay4_name,
        COALESCE(s5.name, bsi.bay5) AS bay5_name,
        COALESCE(s6.name, bsi.bay6) AS bay6_name,
        COALESCE(s7.name, bsi.bay7) AS bay7_name,
        COALESCE(s8.name, bsi.bay8) AS bay8_name,
        COALESCE(s9.name, bsi.bay9) AS bay9_name,
        COALESCE(s10.name, bsi.bay10) AS bay10_name";
    }

    // Main table and basic joins
    $sql .= " FROM blade_server_inventory bsi
    LEFT JOIN inventory_chassis ch ON bsi.chassis_id = ch.id
    LEFT JOIN cities c ON ch.city_id = c.id
    LEFT JOIN countries co ON ch.country_id = co.id
    LEFT JOIN racks r ON ch.rack_id = r.id
    LEFT JOIN orders o ON bsi.order_id = o.id
    LEFT JOIN users u ON o.owner_id = u.id";

    // Add CPU join if the table exists
    if ($tablesExist['dedicated_cpu']) {
      $sql .= " LEFT JOIN dedicated_cpu dc ON bsi.cpu = dc.id";
    }

    // Add RAM join if the table exists
    if ($tablesExist['ram_configurations']) {
      $sql .= " LEFT JOIN ram_configurations rc ON bsi.ram = rc.id";
    }

    // Add switch join if the table exists
    if ($tablesExist['inventory_switches']) {
      $sql .= " LEFT JOIN inventory_switches sw ON bsi.switch_id = sw.id";
    }

    // Add storage joins if storage table exists
    if ($tablesExist['storage']) {
      $sql .= "
        LEFT JOIN storage s1 ON bsi.bay1 = s1.id
        LEFT JOIN storage s2 ON bsi.bay2 = s2.id
        LEFT JOIN storage s3 ON bsi.bay3 = s3.id
        LEFT JOIN storage s4 ON bsi.bay4 = s4.id
        LEFT JOIN storage s5 ON bsi.bay5 = s5.id
        LEFT JOIN storage s6 ON bsi.bay6 = s6.id
        LEFT JOIN storage s7 ON bsi.bay7 = s7.id
        LEFT JOIN storage s8 ON bsi.bay8 = s8.id
        LEFT JOIN storage s9 ON bsi.bay9 = s9.id
        LEFT JOIN storage s10 ON bsi.bay10 = s10.id";
    }

    // Add filters
    $sql .= " WHERE 1=1";

    if ($status && $status != 'All') {
      $sql .= " AND bsi.status = " . $pdo->quote($status);
    }

    if ($search) {
      // Combine multiple search conditions including client info
      $sql .= " AND (
        bsi.label LIKE " . $pdo->quote("%$search%") . " OR
        bsi.cpu LIKE " . $pdo->quote("%$search%") . " OR
        bsi.notes LIKE " . $pdo->quote("%$search%") . " OR
        bsi.mac LIKE " . $pdo->quote("%$search%") . " OR
        bsi.ipmi LIKE " . $pdo->quote("%$search%") . " OR
        ch.label LIKE " . $pdo->quote("%$search%") . " OR
        c.city LIKE " . $pdo->quote("%$search%") . " OR
        co.country LIKE " . $pdo->quote("%$search%") . " OR
        CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) LIKE " . $pdo->quote("%$search%") . " OR
        COALESCE(u.company_name, '') LIKE " . $pdo->quote("%$search%") . " OR
        CAST(o.id AS CHAR) LIKE " . $pdo->quote("%$search%");

      // Add storage search if table exists
      if ($tablesExist['storage']) {
        $sql .= " OR
          s1.name LIKE " . $pdo->quote("%$search%") . " OR
          s2.name LIKE " . $pdo->quote("%$search%") . " OR
          s3.name LIKE " . $pdo->quote("%$search%") . " OR
          s4.name LIKE " . $pdo->quote("%$search%");
      }

      $sql .= ")";
    }

    // Add sorting
    $validSortFields = [
      'id' => 'bsi.id',
      'label' => 'bsi.label',
      'cpu' => 'bsi.cpu',
      'status' => 'bsi.status',
      'mac' => 'bsi.mac',
      'ipmi' => 'bsi.ipmi',
      'city_name' => 'c.city',
      'country_name' => 'co.country',
      'order_id' => 'o.id',
      'client_name' => 'u.first_name',
      'chassis_label' => 'ch.label'
    ];

    // Determine sort field
    $sortColumn = isset($validSortFields[$sortField]) ? $validSortFields[$sortField] : 'bsi.id';
    $sql .= " ORDER BY $sortColumn $sortDirection";

    // Log the query for debugging
    error_log("get_blade_server_inventory SQL: " . $sql);

    // Execute query
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $bladeServers = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($bladeServers);

  } catch (Exception $e) {
    error_log("Error in get_blade_server_inventory: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'error' => 'Failed to fetch blade servers',
      'details' => $e->getMessage()
    ]);
  }
}

// Get server details (for checking IPMI IP)
elseif($_GET['f'] == 'get_server_details'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['server_id']) || empty($data['server_id'])) {
      throw new Exception('Server ID is required');
    }

    if (!isset($data['server_type']) || empty($data['server_type'])) {
      throw new Exception('Server type is required');
    }

    // Determine which table to query based on server type
    $serverTable = $data['server_type'] === 'dedicated' ? 'inventory_dedicated_servers' : 'blade_server_inventory';

    // Get server details
    $sql = "SELECT id, label, hostname, ipmi FROM $serverTable WHERE id = :server_id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':server_id', $data['server_id']);
    $sth->execute();

    if ($sth->rowCount() === 0) {
      throw new Exception('Server not found');
    }

    $server = $sth->fetch(PDO::FETCH_ASSOC);

    // Return server details as JSON
    header('Content-Type: application/json');
    echo json_encode($server);

  } catch (Exception $e) {
    error_log("Error in get_server_details: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to fetch server details: ' . $e->getMessage()
    ]);
  }
}

// Function 3: update_dedicated_server - Updates a dedicated server with proper handling
elseif($_GET['f'] == 'update_dedicated_server'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log the incoming data for debugging
    error_log("Update dedicated server input: " . json_encode($data));

    // Validate required fields
    if (!isset($data['id'])) {
      throw new Exception('Server ID is required');
    }

    // Check if server exists first
    $checkSql = "SELECT COUNT(*) FROM inventory_dedicated_servers WHERE id = :id";
    $checkSth = $pdo->prepare($checkSql);
    $checkSth->bindValue(':id', $data['id']);
    $checkSth->execute();

    if ($checkSth->fetchColumn() === 0) {
      throw new Exception("Server not found with ID: " . $data['id']);
    }

    // Prepare update fields
    $updates = [];
    $params = [':id' => $data['id']];

    // Define allowed fields - comprehensive list
    $allowedFields = [
      'label', 'cpu', 'ram', 'switch_id',
      'port1', 'port1_speed', 'port2', 'port2_speed',
      'port3', 'port3_speed', 'port4', 'port4_speed',
      'rack_id', 'position', 'size',
      'bay1', 'bay2', 'bay3', 'bay4', 'bay5',
      'bay6', 'bay7', 'bay8', 'bay9', 'bay10',
      'bay11', 'bay12', 'bay13', 'bay14', 'bay15',
      'bay16', 'bay17', 'bay18', 'bay19', 'bay20',
      'bay21', 'bay22', 'bay23', 'bay24', 'bay25', 'bay26',
      'mac', 'ipmi', 'status', 'city_id', 'country_id', 'notes',
      'main_ip', 'additional_ips', 'order_id', 'password',
      'ipmi_root_pass', 'ipmi_user_pass'
    ];

    // Process standard fields with type handling
    foreach ($allowedFields as $field) {
      if (array_key_exists($field, $data)) {
        $updates[] = "`$field` = :$field";

        // Special handling for foreign key fields
        if (in_array($field, ['rack_id', 'city_id', 'country_id', 'cpu', 'ram', 'switch_id', 'order_id']) &&
            ($data[$field] === '' || $data[$field] === '0' || $data[$field] === 0 || $data[$field] === null)) {
          $params[":$field"] = null;
          error_log("Setting $field to NULL");
        } else if (in_array($field, ['rack_id', 'city_id', 'country_id', 'cpu', 'ram', 'switch_id', 'order_id']) &&
                  is_numeric($data[$field])) {
          $params[":$field"] = intval($data[$field]);
          error_log("Setting $field to integer: " . intval($data[$field]));
        } else {
          $params[":$field"] = $data[$field];
          error_log("Setting $field to: " . $data[$field]);
        }
      }
    }

    // Explicitly handle password fields
    if (array_key_exists('ipmi_root_pass', $data)) {
      $updates[] = "`ipmi_root_pass` = :ipmi_root_pass";
      $params[":ipmi_root_pass"] = $data['ipmi_root_pass'];
    }

    if (array_key_exists('ipmi_user_pass', $data)) {
      $updates[] = "`ipmi_user_pass` = :ipmi_user_pass";
      $params[":ipmi_user_pass"] = $data['ipmi_user_pass'];
    }

    // Special handling for 'switch' field if provided (maps to switch_id)
    if (array_key_exists('switch', $data) && !array_key_exists('switch_id', $data)) {
      // Handle three cases:
      // 1. It's a numeric value (treat as ID)
      // 2. It's a string that matches a switch label (lookup the ID)
      // 3. It's empty (set to NULL)

      if (empty($data['switch'])) {
        $updates[] = "`switch_id` = NULL";
      } else if (is_numeric($data['switch'])) {
        $updates[] = "`switch_id` = :switch_id";
        $params[":switch_id"] = intval($data['switch']);
      } else {
        // Try to look up the switch ID by label
        try {
          $switchSql = "SELECT id FROM inventory_switches WHERE label = :label LIMIT 1";
          $switchSth = $pdo->prepare($switchSql);
          $switchSth->bindValue(':label', $data['switch']);
          $switchSth->execute();

          $switchId = $switchSth->fetchColumn();
          if ($switchId) {
            $updates[] = "`switch_id` = :switch_id";
            $params[":switch_id"] = intval($switchId);
          } else {
            // If no match found, store the string in the proper field
            $updates[] = "`switch` = :switch_val";
            $params[":switch_val"] = $data['switch'];
          }
        } catch (Exception $e) {
          // On any error, just store the string value
          $updates[] = "`switch` = :switch_val";
          $params[":switch_val"] = $data['switch'];
        }
      }
    }

    // Check if there are fields to update - prevent empty SET clause
    if (empty($updates)) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'No changes to update'
      ]);
      return;
    }

    // Build the SQL
    $sql = "UPDATE inventory_dedicated_servers SET " . implode(", ", $updates) . " WHERE id = :id";
    error_log("update_dedicated_server SQL: " . $sql);
    error_log("With parameters: " . json_encode($params));

    // Execute the query with try/catch for better error details
    try {
      $sth = $pdo->prepare($sql);
      foreach ($params as $key => $value) {
        if ($value === null) {
          $sth->bindValue($key, null, PDO::PARAM_NULL);
        } else if (is_int($value)) {
          $sth->bindValue($key, $value, PDO::PARAM_INT);
        } else {
          $sth->bindValue($key, $value);
        }
      }
      $sth->execute();

      // Handle IPMI IP allocation if ipmi field was updated
      if (array_key_exists('ipmi', $data)) {
        // Get the current server data to check the old IPMI IP
        $currentSql = "SELECT ipmi, label FROM inventory_dedicated_servers WHERE id = :id";
        $currentSth = $pdo->prepare($currentSql);
        $currentSth->bindValue(':id', $data['id']);
        $currentSth->execute();
        $currentServer = $currentSth->fetch(PDO::FETCH_ASSOC);

        $newIpmiIp = $data['ipmi'];
        $oldIpmiIp = $currentServer['ipmi'];
        $serverLabel = $currentServer['label'] ?? "ID " . $data['id'];

        // If there's a new IPMI IP that's different from the old one
        if (!empty($newIpmiIp) && $newIpmiIp !== $oldIpmiIp) {
          error_log("Server IPMI IP changed from '$oldIpmiIp' to '$newIpmiIp' - marking new IP as allocated");

          // Find which subnet the new IPMI IP belongs to
          $findIpSql = "SELECT id, subnet_id, is_used FROM ip_addresses WHERE ip_address = :ip_address";
          $findIpSth = $pdo->prepare($findIpSql);
          $findIpSth->bindValue(':ip_address', $newIpmiIp);
          $findIpSth->execute();

          if ($findIpSth->rowCount() > 0) {
            $ipData = $findIpSth->fetch(PDO::FETCH_ASSOC);

            // Only mark as allocated if it's not already used
            if ($ipData['is_used'] == 0) {
              $allocateIpSql = "UPDATE ip_addresses 
                               SET is_used = 1, 
                                   assigned_to = :assigned_to,
                                   device_type = :device_type,
                                   notes = :notes
                               WHERE ip_address = :ip_address";
              $allocateIpSth = $pdo->prepare($allocateIpSql);
              $allocateIpSth->bindValue(':assigned_to', $data['id']);
              $allocateIpSth->bindValue(':device_type', 'dedicated_server');
              $allocateIpSth->bindValue(':notes', "Allocated to dedicated server IPMI: $serverLabel (server_id: " . $data['id'] . ")");
              $allocateIpSth->bindValue(':ip_address', $newIpmiIp);
              $allocateIpSth->execute();

              error_log("Marked IP $newIpmiIp as allocated (in_used = 1) for server IPMI");
            }
          } else {
            error_log("Warning: IPMI IP $newIpmiIp not found in ip_addresses table");
          }
        }

        // If there was an old IPMI IP and it's different from the new one, deallocate it
        if (!empty($oldIpmiIp) && $oldIpmiIp !== $newIpmiIp) {
          error_log("Deallocating old server IPMI IP: $oldIpmiIp");

          $deallocateOldIpSql = "UPDATE ip_addresses 
                                SET is_used = 0, 
                                    assigned_to = NULL,
                                    device_type = NULL,
                                    notes = NULL
                                WHERE ip_address = :ip_address";
          $deallocateOldIpSth = $pdo->prepare($deallocateOldIpSql);
          $deallocateOldIpSth->bindValue(':ip_address', $oldIpmiIp);
          $deallocateOldIpSth->execute();

          error_log("Deallocated old IPMI IP $oldIpmiIp");
        }

        // If the new IPMI IP is empty/null and there was an old IP, deallocate the old one
        if (empty($newIpmiIp) && !empty($oldIpmiIp)) {
          error_log("Server IPMI IP cleared - deallocating old IP: $oldIpmiIp");

          $deallocateOldIpSql = "UPDATE ip_addresses 
                                SET is_used = 0, 
                                    assigned_to = NULL,
                                    device_type = NULL,
                                    notes = NULL
                                WHERE ip_address = :ip_address";
          $deallocateOldIpSth = $pdo->prepare($deallocateOldIpSql);
          $deallocateOldIpSth->bindValue(':ip_address', $oldIpmiIp);
          $deallocateOldIpSth->execute();

          error_log("Deallocated old IPMI IP $oldIpmiIp (IPMI IP was cleared)");
        }
      }

      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'Dedicated server updated successfully',
        'affected_rows' => $sth->rowCount()
      ]);
    } catch (PDOException $e) {
      throw new Exception("Database error: " . $e->getMessage());
    }

  } catch (Exception $e) {
    error_log("Error in update_dedicated_server: " . $e->getMessage());

    // Return detailed error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to update dedicated server: ' . $e->getMessage()
    ]);
  }
}

// Function 4: update_blade_server_inventory - Updates a blade server with proper handling
elseif($_GET['f'] == 'update_blade_server_inventory'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log the incoming data for debugging
    error_log("Update blade server input: " . json_encode($data));

    // Validate required fields
    if (!isset($data['id'])) {
      throw new Exception('Server ID is required');
    }

    // Check if server exists first
    $checkSql = "SELECT COUNT(*) FROM blade_server_inventory WHERE id = :id";
    $checkSth = $pdo->prepare($checkSql);
    $checkSth->bindValue(':id', $data['id']);
    $checkSth->execute();

    if ($checkSth->fetchColumn() === 0) {
      throw new Exception("Blade server not found with ID: " . $data['id']);
    }

    // Prepare update fields
    $updates = [];
    $params = [':id' => $data['id']];

    // Define allowed fields - comprehensive list
    $allowedFields = [
      'label', 'cpu', 'ram', 'switch_id',
      'port1', 'port1_speed', 'port2', 'port2_speed',
      'port3', 'port3_speed', 'port4', 'port4_speed',
      'bay1', 'bay2', 'bay3', 'bay4', 'bay5',
      'bay6', 'bay7', 'bay8', 'bay9', 'bay10',
      'mac', 'ipmi', 'root', 'user', 'notes', 'status',
      'chassis_id', 'order_id', 'main_ip', 'additional_ips',
      'password', 'ipmi_root_pass', 'ipmi_user_pass'
    ];

    // Process standard fields with type handling
    foreach ($allowedFields as $field) {
      if (array_key_exists($field, $data)) {
        $updates[] = "`$field` = :$field";

        // Special handling for foreign key fields
        if (in_array($field, ['chassis_id', 'cpu', 'ram', 'switch_id', 'order_id']) &&
            ($data[$field] === '' || $data[$field] === '0' || $data[$field] === 0 || $data[$field] === null)) {
          $params[":$field"] = null;
          error_log("Setting $field to NULL");
        } else if (in_array($field, ['chassis_id', 'cpu', 'ram', 'switch_id', 'order_id']) &&
                  is_numeric($data[$field])) {
          $params[":$field"] = intval($data[$field]);
          error_log("Setting $field to integer: " . intval($data[$field]));
        } else {
          $params[":$field"] = $data[$field];
          error_log("Setting $field to: " . $data[$field]);
        }
      }
    }

    // Explicitly handle password fields
    if (array_key_exists('ipmi_root_pass', $data)) {
      $updates[] = "`ipmi_root_pass` = :ipmi_root_pass";
      $params[":ipmi_root_pass"] = $data['ipmi_root_pass'];
    }

    if (array_key_exists('ipmi_user_pass', $data)) {
      $updates[] = "`ipmi_user_pass` = :ipmi_user_pass";
      $params[":ipmi_user_pass"] = $data['ipmi_user_pass'];
    }

    // Special handling for 'switch' field if provided (maps to switch_id)
    if (array_key_exists('switch', $data) && !array_key_exists('switch_id', $data)) {
      // Handle three cases:
      // 1. It's a numeric value (treat as ID)
      // 2. It's a string that matches a switch label (lookup the ID)
      // 3. It's empty (set to NULL)

      if (empty($data['switch'])) {
        $updates[] = "`switch_id` = NULL";
      } else if (is_numeric($data['switch'])) {
        $updates[] = "`switch_id` = :switch_id";
        $params[":switch_id"] = intval($data['switch']);
      } else {
        // Try to look up the switch ID by label
        try {
          $switchSql = "SELECT id FROM inventory_switches WHERE label = :label LIMIT 1";
          $switchSth = $pdo->prepare($switchSql);
          $switchSth->bindValue(':label', $data['switch']);
          $switchSth->execute();

          $switchId = $switchSth->fetchColumn();
          if ($switchId) {
            $updates[] = "`switch_id` = :switch_id";
            $params[":switch_id"] = intval($switchId);
          } else {
            // If no match found, store the string in the proper field
            $updates[] = "`switch` = :switch_val";
            $params[":switch_val"] = $data['switch'];
          }
        } catch (Exception $e) {
          // On any error, just store the string value
          $updates[] = "`switch` = :switch_val";
          $params[":switch_val"] = $data['switch'];
        }
      }
    }

    // Check if there are fields to update - prevent empty SET clause
    if (empty($updates)) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'No changes to update'
      ]);
      return;
    }

    // Build the SQL
    $sql = "UPDATE blade_server_inventory SET " . implode(", ", $updates) . " WHERE id = :id";
    error_log("update_blade_server_inventory SQL: " . $sql);
    error_log("With parameters: " . json_encode($params));

    // Execute the query with try/catch for better error details
    try {
      $sth = $pdo->prepare($sql);
      foreach ($params as $key => $value) {
        if ($value === null) {
          $sth->bindValue($key, null, PDO::PARAM_NULL);
        } else if (is_int($value)) {
          $sth->bindValue($key, $value, PDO::PARAM_INT);
        } else {
          $sth->bindValue($key, $value);
        }
      }
      $sth->execute();

      // Handle IPMI IP allocation if ipmi field was updated
      if (array_key_exists('ipmi', $data)) {
        // Get the current server data to check the old IPMI IP
        $currentSql = "SELECT ipmi, label FROM blade_server_inventory WHERE id = :id";
        $currentSth = $pdo->prepare($currentSql);
        $currentSth->bindValue(':id', $data['id']);
        $currentSth->execute();
        $currentServer = $currentSth->fetch(PDO::FETCH_ASSOC);

        $newIpmiIp = $data['ipmi'];
        $oldIpmiIp = $currentServer['ipmi'];
        $serverLabel = $currentServer['label'] ?? "ID " . $data['id'];

        // If there's a new IPMI IP that's different from the old one
        if (!empty($newIpmiIp) && $newIpmiIp !== $oldIpmiIp) {
          error_log("Blade server IPMI IP changed from '$oldIpmiIp' to '$newIpmiIp' - marking new IP as allocated");

          // Find which subnet the new IPMI IP belongs to
          $findIpSql = "SELECT id, subnet_id, is_used FROM ip_addresses WHERE ip_address = :ip_address";
          $findIpSth = $pdo->prepare($findIpSql);
          $findIpSth->bindValue(':ip_address', $newIpmiIp);
          $findIpSth->execute();

          if ($findIpSth->rowCount() > 0) {
            $ipData = $findIpSth->fetch(PDO::FETCH_ASSOC);

            // Only mark as allocated if it's not already used
            if ($ipData['is_used'] == 0) {
              $allocateIpSql = "UPDATE ip_addresses 
                               SET is_used = 1, 
                                   assigned_to = :assigned_to,
                                   device_type = :device_type,
                                   notes = :notes
                               WHERE ip_address = :ip_address";
              $allocateIpSth = $pdo->prepare($allocateIpSql);
              $allocateIpSth->bindValue(':assigned_to', $data['id']);
              $allocateIpSth->bindValue(':device_type', 'blade');
              $allocateIpSth->bindValue(':notes', "Allocated to blade server IPMI: $serverLabel (server_id: " . $data['id'] . ")");
              $allocateIpSth->bindValue(':ip_address', $newIpmiIp);
              $allocateIpSth->execute();

              error_log("Marked IP $newIpmiIp as allocated (in_used = 1) for blade server IPMI");
            }
          } else {
            error_log("Warning: IPMI IP $newIpmiIp not found in ip_addresses table");
          }
        }

        // If there was an old IPMI IP and it's different from the new one, deallocate it
        if (!empty($oldIpmiIp) && $oldIpmiIp !== $newIpmiIp) {
          error_log("Deallocating old blade server IPMI IP: $oldIpmiIp");

          $deallocateOldIpSql = "UPDATE ip_addresses 
                                SET is_used = 0, 
                                    assigned_to = NULL,
                                    device_type = NULL,
                                    notes = NULL
                                WHERE ip_address = :ip_address";
          $deallocateOldIpSth = $pdo->prepare($deallocateOldIpSql);
          $deallocateOldIpSth->bindValue(':ip_address', $oldIpmiIp);
          $deallocateOldIpSth->execute();

          error_log("Deallocated old IPMI IP $oldIpmiIp");
        }

        // If the new IPMI IP is empty/null and there was an old IP, deallocate the old one
        if (empty($newIpmiIp) && !empty($oldIpmiIp)) {
          error_log("Blade server IPMI IP cleared - deallocating old IP: $oldIpmiIp");

          $deallocateOldIpSql = "UPDATE ip_addresses 
                                SET is_used = 0, 
                                    assigned_to = NULL,
                                    device_type = NULL,
                                    notes = NULL
                                WHERE ip_address = :ip_address";
          $deallocateOldIpSth = $pdo->prepare($deallocateOldIpSql);
          $deallocateOldIpSth->bindValue(':ip_address', $oldIpmiIp);
          $deallocateOldIpSth->execute();

          error_log("Deallocated old IPMI IP $oldIpmiIp (IPMI IP was cleared)");
        }
      }

      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'Blade server updated successfully',
        'affected_rows' => $sth->rowCount()
      ]);
    } catch (PDOException $e) {
      throw new Exception("Database error: " . $e->getMessage());
    }

  } catch (Exception $e) {
    error_log("Error in update_blade_server_inventory: " . $e->getMessage());

    // Return detailed error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to update blade server: ' . $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'update_switch'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // EXTENSIVE DEBUG LOGGING
    error_log("====== UPDATE SWITCH DEBUG ======");
    error_log("Raw data: " . file_get_contents('php://input'));
    error_log("Parsed data: " . json_encode($data));

    // Check for possible field name variations
    $possiblePositionFields = ['rack_position', 'position', 'rack_pos', 'rackPosition'];
    $positionFieldFound = null;

    foreach ($possiblePositionFields as $field) {
        if (isset($data[$field])) {
            error_log("FOUND POSITION FIELD: '{$field}' with value: '{$data[$field]}'");
            $positionFieldFound = $field;
            // Copy to rack_position if it's not already set
            if ($field !== 'rack_position') {
                $data['rack_position'] = $data[$field];
                error_log("COPIED '{$field}' value to 'rack_position'");
            }
            break;
        }
    }

    if ($positionFieldFound === null) {
        error_log("NO POSITION FIELD FOUND IN REQUEST");
    }

    error_log("rack_position (isset): " . (isset($data['rack_position']) ? 'YES' : 'NO'));
    error_log("rack_position (value): " . (isset($data['rack_position']) ? "'" . $data['rack_position'] . "'" : 'NOT SET'));
    error_log("rack_position (empty): " . (empty($data['rack_position']) ? 'YES' : 'NO'));
    error_log("==============================");

    // Validate required fields
    if (!isset($data['id'])) {
      throw new Exception('Switch ID is required');
    }

    $switch_id = intval($data['id']);

    // Get the current switch data to make sure we're not losing information
    $currentSql = "SELECT * FROM inventory_switches WHERE id = :id";
    $currentSth = $pdo->prepare($currentSql);
    $currentSth->bindValue(':id', $switch_id);
    $currentSth->execute();

    if ($currentSth->rowCount() === 0) {
      throw new Exception("Switch with ID $switch_id not found");
    }

    $currentSwitch = $currentSth->fetch(PDO::FETCH_ASSOC);
    error_log("Current switch data: " . json_encode($currentSwitch));

    // Build SQL in a more direct way, explicitly listing fields
    $sql = "UPDATE inventory_switches SET ";
    $updateParts = [];
    $params = [':id' => $switch_id];

    // Define the fields we can update based on the database schema
    $updateableFields = [
      'label', 'switch_ip', 'root_password', 'rack_id',
      'rack_position', 'size_ru', 'status', 'city_id',
      'country_id', 'notes', 'model_id',
      'snmp_community', 'snmp_version', 'snmp_port'
    ];

    // Process each field explicitly
    foreach ($updateableFields as $field) {
      // Special handling for rack_position - always include it if it exists
      if ($field === 'rack_position' && isset($data[$field])) {
        $updateParts[] = "`$field` = :$field";
        $params[":$field"] = $data[$field]; // Allow empty string
        error_log("Adding rack_position to update: '" . $data[$field] . "'");
      }
      // For other fields, only include if they exist and aren't empty strings
      else if (array_key_exists($field, $data)) {
        $updateParts[] = "`$field` = :$field";

        // Special handling for numeric fields
        if (in_array($field, ['city_id', 'country_id', 'rack_id', 'model_id', 'size_ru', 'snmp_version', 'snmp_port'])) {
          // Convert to integer or NULL for numeric fields
          $params[":$field"] = (empty($data[$field]) || $data[$field] === '0') ? null : intval($data[$field]);
        } else {
          $params[":$field"] = $data[$field];
        }
      }
    }

    // If no updates, return success
    if (empty($updateParts)) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'No changes to update'
      ]);
      return;
    }

    // Complete the SQL
    $sql .= implode(", ", $updateParts) . " WHERE id = :id";
    error_log("SQL: " . $sql);
    error_log("SQL parameters: " . json_encode($params));

    // Execute the query
    $sth = $pdo->prepare($sql);
    foreach ($params as $key => $value) {
      if ($value === null) {
        $sth->bindValue($key, null, PDO::PARAM_NULL);
      } elseif (is_int($value)) {
        $sth->bindValue($key, $value, PDO::PARAM_INT);
      } else {
        $sth->bindValue($key, $value);
      }
    }
    $sth->execute();

    // Handle IP allocation if switch_ip was updated
    if (array_key_exists('switch_ip', $data)) {
      $newSwitchIp = $data['switch_ip'];
      $oldSwitchIp = $currentSwitch['switch_ip'];

      // If there's a new IP that's different from the old one
      if (!empty($newSwitchIp) && $newSwitchIp !== $oldSwitchIp) {
        error_log("Switch IP changed from '$oldSwitchIp' to '$newSwitchIp' - marking new IP as allocated");

        // Find which subnet the new IP belongs to
        $findIpSql = "SELECT id, subnet_id, is_used FROM ip_addresses WHERE ip_address = :ip_address";
        $findIpSth = $pdo->prepare($findIpSql);
        $findIpSth->bindValue(':ip_address', $newSwitchIp);
        $findIpSth->execute();

        if ($findIpSth->rowCount() > 0) {
          $ipData = $findIpSth->fetch(PDO::FETCH_ASSOC);

          // Only mark as allocated if it's not already used
          if ($ipData['is_used'] == 0) {
            $allocateIpSql = "UPDATE ip_addresses 
                             SET is_used = 1, 
                                 assigned_to = :assigned_to,
                                 device_type = :device_type,
                                 notes = :notes
                             WHERE ip_address = :ip_address";
            $allocateIpSth = $pdo->prepare($allocateIpSql);
            $allocateIpSth->bindValue(':assigned_to', $switch_id);
            $allocateIpSth->bindValue(':device_type', 'switch');
            $allocateIpSth->bindValue(':notes', "Allocated to switch " . ($data['label'] ?? "ID $switch_id") . " (switch_id: $switch_id)");
            $allocateIpSth->bindValue(':ip_address', $newSwitchIp);
            $allocateIpSth->execute();

            error_log("Marked IP $newSwitchIp as allocated (in_used = 1) for switch");
          }
        } else {
          error_log("Warning: IP $newSwitchIp not found in ip_addresses table");
        }
      }

              // If there was an old IP and it's different from the new one, deallocate it
        if (!empty($oldSwitchIp) && $oldSwitchIp !== $newSwitchIp) {
          error_log("Deallocating old switch IP: $oldSwitchIp");

          $deallocateOldIpSql = "UPDATE ip_addresses 
                                SET is_used = 0, 
                                    assigned_to = NULL,
                                    device_type = NULL,
                                    notes = NULL
                                WHERE ip_address = :ip_address";
          $deallocateOldIpSth = $pdo->prepare($deallocateOldIpSql);
          $deallocateOldIpSth->bindValue(':ip_address', $oldSwitchIp);
          $deallocateOldIpSth->execute();

          error_log("Deallocated old IP $oldSwitchIp");
        }

              // If the new IP is empty/null and there was an old IP, deallocate the old one
        if (empty($newSwitchIp) && !empty($oldSwitchIp)) {
          error_log("Switch IP cleared - deallocating old IP: $oldSwitchIp");

          $deallocateOldIpSql = "UPDATE ip_addresses 
                                SET is_used = 0, 
                                    assigned_to = NULL,
                                    device_type = NULL,
                                    notes = NULL
                                WHERE ip_address = :ip_address";
          $deallocateOldIpSth = $pdo->prepare($deallocateOldIpSql);
          $deallocateOldIpSth->bindValue(':ip_address', $oldSwitchIp);
          $deallocateOldIpSth->execute();

          error_log("Deallocated old IP $oldSwitchIp (switch IP was cleared)");
        }
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Switch updated successfully'
    ]);

  } catch (PDOException $e) {
    error_log("Database error in update_switch: " . $e->getMessage());

    // Check for specific database errors
    if (strpos($e->getMessage(), 'foreign key constraint fails') !== false) {
      $errorMsg = 'Invalid foreign key reference. Please check that rack_id, city_id, country_id, and model_id values are valid.';
    } else if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
      $errorMsg = 'A switch with this information already exists.';
    } else {
      $errorMsg = 'Database error: ' . $e->getMessage();
    }

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to update switch: ' . $errorMsg
    ]);
    exit;
  } catch (Exception $e) {
    error_log("Error in update_switch: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to update switch: ' . $e->getMessage()
    ]);
    exit;
  }
}

// Get single switch details by ID
elseif($_GET['f'] == 'get_switch_by_id'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data from request
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('Switch ID is required');
    }

    $switch_id = intval($data['id']);

    // Fetch switch details and join model name, if available
    $sth = $pdo->prepare("SELECT s.*, m.name AS model
                          FROM inventory_switches s
                          LEFT JOIN inventory_switch_model m ON m.id = s.model_id
                          WHERE s.id = :switch_id
                          LIMIT 1");
    $sth->bindValue(':switch_id', $switch_id, PDO::PARAM_INT);
    $sth->execute();

    if ($sth->rowCount() === 0) {
      throw new Exception('Switch not found');
    }

    $switch = $sth->fetch(PDO::FETCH_ASSOC);

    // Return switch details
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'switch'   => $switch
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error'   => $e->getMessage()
    ]);
  }
}

// Updated function to add a dedicated server (with RAM field)
elseif($_GET['f'] == 'add_dedicated_server'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log input data for debugging
    error_log("Add dedicated server input: " . json_encode($data));

    // Validate required fields
    if (!isset($data['label'])) {
      throw new Exception('Server label is required');
    }

    // Set default status if not provided
    if (!isset($data['status'])) {
      $data['status'] = 'Available';
    }

    // Prepare insert fields and values
    $fields = [];
    $values = [];
    $params = [];

    // Define allowed fields based on table structure
    $allowedFields = [
      'label', 'cpu', 'ram', 'switch',
      'port1', 'port1_speed', 'port2', 'port2_speed',
      'port3', 'port3_speed', 'port4', 'port4_speed',
      'rack_id', 'position', 'size',
      'bay1', 'bay2', 'bay3', 'bay4', 'bay5',
      'bay6', 'bay7', 'bay8', 'bay9', 'bay10',
      'bay11', 'bay12', 'bay13', 'bay14', 'bay15',
      'bay16', 'bay17', 'bay18', 'bay19', 'bay20',
      'bay21', 'bay22', 'bay23', 'bay24', 'bay25', 'bay26',
      'mac', 'ipmi', 'status', 'city_id', 'country_id', 'notes',
      'main_ip', 'additional_ips', 'ipmi_root_pass', 'ipmi_user_pass'
    ];

    foreach ($allowedFields as $field) {
      if (array_key_exists($field, $data) && $data[$field] !== '') {
        $fields[] = "`$field`";
        $values[] = ":$field";
        $params[":$field"] = $data[$field];
      }
    }

    // Check if we have any fields to insert
    if (empty($fields)) {
      throw new Exception('No valid fields provided');
    }

    // Build the SQL
    $sql = "INSERT INTO inventory_dedicated_servers (" . implode(", ", $fields) . ")
            VALUES (" . implode(", ", $values) . ")";

    // Log SQL for debugging
    error_log("SQL query for dedicated server: " . $sql);
    error_log("Parameters: " . json_encode($params));

    // Execute the query with detailed error handling
    try {
      $sth = $pdo->prepare($sql);
      foreach ($params as $key => $value) {
        $sth->bindValue($key, $value);
      }
      $sth->execute();
    } catch (PDOException $e) {
      error_log("SQL Error: " . $e->getMessage());
      throw new Exception("Database error: " . $e->getMessage());
    }

    // Get the new ID
    $newId = $pdo->lastInsertId();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $newId,
      'message' => 'Dedicated server added successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_dedicated_server: " . $e->getMessage());

    // Return detailed error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to add dedicated server: ' . $e->getMessage()
    ]);
  }
}


elseif($_GET['f'] == 'update_chassis'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id'])) {
      throw new Exception('Chassis ID is required');
    }

    $chassis_id = intval($data['id']);
    error_log("Processing update for chassis ID {$chassis_id}");

    // Get current chassis bay assignments
    $currentSql = "SELECT bay1, bay2, bay3, bay4 FROM inventory_chassis WHERE id = :id";
    $currentSth = $pdo->prepare($currentSql);
    $currentSth->bindValue(':id', $chassis_id);
    $currentSth->execute();

    if ($currentSth->rowCount() === 0) {
      throw new Exception("Chassis not found");
    }

    $currentBays = $currentSth->fetch(PDO::FETCH_ASSOC);
    error_log("Current bays: " . json_encode($currentBays));

    // Process the chassis update
    $updates = [];
    $params = [':id' => $chassis_id];

    // Define allowed fields
    $allowedFields = [
      'label', 'model', 'model_id', 'rack_id', 'position',
      'status', 'city_id', 'country_id', 'notes',
      'bay1', 'bay2', 'bay3', 'bay4'
    ];

    // Process fields
    foreach ($allowedFields as $field) {
      if (isset($data[$field])) {
        $updates[] = "`$field` = :$field";
        $params[":$field"] = $data[$field];
      }
    }

    // If no updates, return success
    if (empty($updates)) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'No changes to update'
      ]);
      return;
    }

    // Start by updating chassis
    $sql = "UPDATE inventory_chassis SET " . implode(", ", $updates) . " WHERE id = :id";
    $sth = $pdo->prepare($sql);

    foreach ($params as $key => $value) {
      $sth->bindValue($key, $value);
    }

    $sth->execute();
    error_log("Chassis update completed, now handling blade references");

    // Handle blade server updates separately without transactions
    try {
      // Check which bays changed
      for ($i = 1; $i <= 4; $i++) {
        $bayField = "bay{$i}";

        // Skip if the bay wasn't in the update data
        if (!isset($data[$bayField])) continue;

        $newBladeId = $data[$bayField];
        $oldBladeId = $currentBays[$bayField];

        error_log("Processing $bayField - Old: $oldBladeId, New: $newBladeId");

        // Handle empty values consistently
        if ($newBladeId === '' || $newBladeId === '0' || $newBladeId === 0 || $newBladeId === null) {
          $newBladeId = null;
        }

        if ($oldBladeId === '' || $oldBladeId === '0' || $oldBladeId === 0 || $oldBladeId === null) {
          $oldBladeId = null;
        }

        // Convert to integers for comparison (if not null)
        $oldBladeIdInt = ($oldBladeId !== null) ? intval($oldBladeId) : null;
        $newBladeIdInt = ($newBladeId !== null) ? intval($newBladeId) : null;

        // If no change, skip
        if ($oldBladeIdInt === $newBladeIdInt) {
          error_log("No change for $bayField");
          continue;
        }

        // If old blade exists, unassign it from this chassis
        if ($oldBladeIdInt !== null) {
          error_log("Unassigning blade $oldBladeIdInt from chassis $chassis_id");
          $clearSql = "UPDATE blade_server_inventory SET chassis_id = NULL WHERE id = :blade_id AND chassis_id = :chassis_id";
          $clearSth = $pdo->prepare($clearSql);
          $clearSth->bindValue(':blade_id', $oldBladeIdInt, PDO::PARAM_INT);
          $clearSth->bindValue(':chassis_id', $chassis_id, PDO::PARAM_INT);
          $clearSth->execute();
          error_log("Unassign affected " . $clearSth->rowCount() . " rows");
        }

        // If new blade exists, assign it to this chassis
        if ($newBladeIdInt !== null) {
          error_log("Assigning blade $newBladeIdInt to chassis $chassis_id");
          $assignSql = "UPDATE blade_server_inventory SET chassis_id = :chassis_id WHERE id = :blade_id";
          $assignSth = $pdo->prepare($assignSql);
          $assignSth->bindValue(':chassis_id', $chassis_id, PDO::PARAM_INT);
          $assignSth->bindValue(':blade_id', $newBladeIdInt, PDO::PARAM_INT);
          $assignSth->execute();
          error_log("Assign affected " . $assignSth->rowCount() . " rows");
        }
      }

      error_log("Blade reference updates completed successfully");

    } catch (Exception $e) {
      // Just log the error but don't fail the whole request
      error_log("Warning: Error updating blade references: " . $e->getMessage());
    }

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Chassis updated successfully',
      'chassis_id' => $chassis_id
    ]);

  } catch (Exception $e) {
    error_log("Error in update_chassis: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to update chassis: ' . $e->getMessage()
    ]);
  }
}


// Delete a dedicated server
// Updated function to automatically free switch ports when deleting a dedicated server
elseif($_GET['f'] == 'delete_dedicated_server'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id'])) {
      throw new Exception('Server ID is required');
    }

    $server_id = intval($data['id']);

    // Begin transaction to ensure consistency
    $pdo->beginTransaction();

    // First, release any switch ports associated with this server
    $releasePortsSql = "UPDATE inventory_switch_ports
                       SET connected_device_id = NULL,
                           connected_device_type = NULL,
                           status = 'Available'
                       WHERE connected_device_id = :server_id
                       AND connected_device_type = 'dedicated'";
    $releasePortsSth = $pdo->prepare($releasePortsSql);
    $releasePortsSth->bindValue(':server_id', $server_id);
    $releasePortsSth->execute();

    $portsReleased = $releasePortsSth->rowCount();
    error_log("Released $portsReleased ports when deleting dedicated server $server_id");

    // Delete the server
    $sql = "DELETE FROM inventory_dedicated_servers WHERE id = :id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':id', $server_id);
    $sth->execute();

    // Check if any rows were deleted
    if ($sth->rowCount() === 0) {
      // Rollback if server not found
      $pdo->rollBack();

      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'Server not found'
      ]);
      return;
    }

    // Commit the transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Dedicated server deleted successfully',
      'ports_released' => $portsReleased
    ]);

  } catch (Exception $e) {
    // Rollback if there was an error
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in delete_dedicated_server: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to delete dedicated server: ' . $e->getMessage()
    ]);
  }
}

// Updated function to automatically free switch ports when deleting a blade server
// Add this function to api_admin_inventory.php (near other helper functions)


// The complete delete_blade_server_inventory function
elseif($_GET['f'] == 'delete_blade_server_inventory'){
try {
  // Authenticate admin
  $admin_id = auth_admin();

  // Get data
  $data = json_decode(file_get_contents('php://input'), true);

  // Log received data for debugging
  error_log("Delete blade server - received data: " . json_encode($data));

  // Validate required fields
  if (!isset($data['id'])) {
    throw new Exception('Server ID is required');
  }

  $server_id = intval($data['id']);
  error_log("Attempting to delete blade server with ID: $server_id");

  // Begin transaction to ensure consistency
  $pdo->beginTransaction();

  // First, get the blade server details for later reference (for releasing resources)
  $getServerSql = "SELECT * FROM blade_server_inventory WHERE id = :id";
  $getServerSth = $pdo->prepare($getServerSql);
  $getServerSth->bindValue(':id', $server_id, PDO::PARAM_INT);
  $getServerSth->execute();

  if ($getServerSth->rowCount() === 0) {
    // Rollback and exit if server not found
    $pdo->rollBack();
    throw new Exception("Blade server with ID $server_id not found");
  }

  $serverData = $getServerSth->fetch(PDO::FETCH_ASSOC);
  error_log("Found blade server: " . json_encode($serverData));

  // Release any switch ports associated with this server
  try {
    $releasePortsSql = "UPDATE inventory_switch_ports
                       SET connected_device_id = NULL,
                           connected_device_type = NULL,
                           status = 'Available'
                       WHERE connected_device_id = :server_id
                       AND connected_device_type = 'blade'";
    $releasePortsSth = $pdo->prepare($releasePortsSql);
    $releasePortsSth->bindValue(':server_id', $server_id, PDO::PARAM_INT);
    $releasePortsSth->execute();

    $portsReleased = $releasePortsSth->rowCount();
    error_log("Released $portsReleased ports for blade server $server_id");
  } catch (Exception $e) {
    // If port release fails, roll back the transaction
    $pdo->rollBack();
    throw new Exception("Failed to release switch ports: " . $e->getMessage());
  }

  // Clear any chassis references to this server
  $chassisUpdated = clearChassisReferences($pdo, $server_id);
  if ($chassisUpdated) {
      error_log("Successfully cleared chassis references for blade server $server_id");
  } else {
      error_log("No chassis references found or cleared for blade server $server_id");
  }

  // Finally delete the server
  try {
    $deleteSql = "DELETE FROM blade_server_inventory WHERE id = :id";
    $deleteSth = $pdo->prepare($deleteSql);
    $deleteSth->bindValue(':id', $server_id, PDO::PARAM_INT);
    $deleteSth->execute();

    $rowsDeleted = $deleteSth->rowCount();
    error_log("Deleted $rowsDeleted blade server records");

    if ($rowsDeleted === 0) {
      // This should not happen since we checked existence earlier
      $pdo->rollBack();
      throw new Exception("Failed to delete blade server record");
    }
  } catch (Exception $e) {
    $pdo->rollBack();
    throw new Exception("Error deleting blade server: " . $e->getMessage());
  }

  // All operations successful, commit transaction
  $pdo->commit();

  // Return success response
  header('Content-Type: application/json');
  echo json_encode([
    'success' => true,
    'message' => 'Blade server deleted successfully',
    'ports_released' => $portsReleased,
    'chassis_updated' => $chassisUpdated
  ]);

} catch (Exception $e) {
  // Rollback if transaction is active
  if ($pdo->inTransaction()) {
    $pdo->rollBack();
  }

  error_log("Error in delete_blade_server_inventory: " . $e->getMessage());

  // Return error as JSON
  header('Content-Type: application/json');
  http_response_code(500);
  echo json_encode([
    'success' => false,
    'error' => 'Failed to delete blade server: ' . $e->getMessage()
  ]);
}
}

// Delete a chassis
elseif($_GET['f'] == 'delete_chassis'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log the request for debugging
    error_log("Delete chassis request for ID: " . (isset($data['id']) ? $data['id'] : 'none'));

    // Validate required fields
    if (!isset($data['id'])) {
      throw new Exception('Chassis ID is required');
    }

    // Check for force parameter
    $force = isset($data['force']) && $data['force'] === true;

    // First check if the blade server table exists
    $tableExists = $pdo->query("SHOW TABLES LIKE 'blade_server_inventory'")->rowCount() > 0;

    // Only check for associated blade servers if the table exists and we're not forcing deletion
    if ($tableExists && !$force) {
      try {
        // Get detailed information about any connected blade servers
        $bladesSql = "SELECT id, label, chassis_id FROM blade_server_inventory WHERE chassis_id = :id";
        $bladesSth = $pdo->prepare($bladesSql);
        $bladesSth->bindValue(':id', $data['id']);
        $bladesSth->execute();

        $blades = $bladesSth->fetchAll(PDO::FETCH_ASSOC);
        $bladeCount = count($blades);

        // Log detailed information
        error_log("Found {$bladeCount} blade servers for chassis ID: {$data['id']}");
        foreach($blades as $blade) {
          error_log("Associated blade: ID={$blade['id']}, Label={$blade['label']}, ChassisID={$blade['chassis_id']}");
        }

        if ($bladeCount > 0) {
          header('Content-Type: application/json');
          echo json_encode([
            'success' => false,
            'error' => 'Cannot delete chassis: There are ' . $bladeCount . ' blade servers associated with it. Please reassign or delete the blade servers first.',
            'associated_blades' => $blades,
            'workaround' => 'You can force delete by adding {"force": true} to your request.'
          ]);
          return;
        }
      } catch (Exception $e) {
        // Log but continue - don't block deletion if this check fails
        error_log("Error checking for associated blade servers: " . $e->getMessage());
      }
    }

    // Get chassis data before deletion for debugging
    $chassisSql = "SELECT * FROM inventory_chassis WHERE id = :id";
    $chassisSth = $pdo->prepare($chassisSql);
    $chassisSth->bindValue(':id', $data['id']);
    $chassisSth->execute();
    $chassisData = $chassisSth->fetch(PDO::FETCH_ASSOC);

    // Log detailed chassis information
    if ($chassisData) {
      error_log("Chassis to delete: " . json_encode($chassisData));

      // Check bay values
      for ($i = 1; $i <= 10; $i++) {
        $bayField = "bay{$i}";
        if (isset($chassisData[$bayField]) && !empty($chassisData[$bayField]) && $chassisData[$bayField] != '0') {
          error_log("Chassis has bay reference in {$bayField}: {$chassisData[$bayField]}");

          // If we're not forcing, try to clear the bay values
          if (!$force) {
            // Clear the bay values
            $clearBaysSql = "UPDATE inventory_chassis SET {$bayField} = NULL WHERE id = :id";
            $clearBaysSth = $pdo->prepare($clearBaysSql);
            $clearBaysSth->bindValue(':id', $data['id']);
            $clearBaysSth->execute();
            error_log("Cleared bay reference in {$bayField}");
          }
        }
      }
    } else {
      error_log("Chassis with ID {$data['id']} not found in database");
    }

    // If force is true, first clear any references from blade servers
    if ($force && $tableExists) {
      try {
        $updateBladesSql = "UPDATE blade_server_inventory SET chassis_id = NULL WHERE chassis_id = :id";
        $updateBladesSth = $pdo->prepare($updateBladesSql);
        $updateBladesSth->bindValue(':id', $data['id']);
        $updateBladesSth->execute();
        $updatedCount = $updateBladesSth->rowCount();
        error_log("Force cleared chassis_id from {$updatedCount} blade servers");
      } catch (Exception $e) {
        error_log("Error clearing chassis references: " . $e->getMessage());
      }
    }

    // Delete the chassis
    $sql = "DELETE FROM inventory_chassis WHERE id = :id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':id', $data['id']);
    $sth->execute();

    // Check if any rows were deleted
    if ($sth->rowCount() === 0) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'Chassis not found'
      ]);
      return;
    }

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Chassis deleted successfully' . ($force ? ' (forced)' : ''),
      'chassis_id' => $data['id']
    ]);

  } catch (Exception $e) {
    error_log("Error in delete_chassis: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to delete chassis: ' . $e->getMessage()
    ]);
  }
}

// Delete a switch
elseif($_GET['f'] == 'delete_switch'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id'])) {
      throw new Exception('Switch ID is required');
    }

    // Delete the switch
    $sql = "DELETE FROM inventory_switches WHERE id = :id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':id', $data['id']);
    $sth->execute();

    // Check if any rows were deleted
    if ($sth->rowCount() === 0) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'Switch not found'
      ]);
      return;
    }

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Switch deleted successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in delete_switch: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to delete switch: ' . $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'execute_idrac_batch_commands'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data from request
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (empty($data['ipmi_address'])) {
      throw new Exception('iDRAC IP address is required');
    }

    if (empty($data['commands']) || !is_array($data['commands'])) {
      throw new Exception('At least one command is required');
    }

    // Extract all parameters with defaults in one block
    $server_id = $data['server_id'] ?? null;
    $server_type = $data['server_type'] ?? 'dedicated';
    $ipmi_address = $data['ipmi_address'];
    $ipmi_username = $data['ipmi_username'] ?? 'root';
    $ipmi_password = $data['ipmi_password'] ?? '';
    $idrac_version = $data['idrac_version'] ?? 8;
    $commands = $data['commands']; // Array of commands
    $bypass_cache = $data['bypass_cache'] ?? false;

    // Define common whitelist of allowed commands with associated timeouts
    $allowed_commands = [
      'racadm hwinventory' => 60,        // More complex hardware inventory needs more time
      'racadm getsensorinfo' => 45,      // Sensor data typically returns quickly
      'racadm getsel' => 25,             // System event logs can be large
      'racadm raid get pdisks -o' => 45, // RAID disk info can be slow
      'racadm raid get controllers' => 30,
      'racadm get System.ServerInfo' => 15,
      'racadm version' => 10,
      'racadm getconfig -g idRacInfo' => 15,
      'racadm getniccfg' => 15,
      'racadm get iDRAC.NIC' => 15,
      'racadm getsysinfo' => 15,
      'racadm get System.MemorySettings' => 20,
      'racadm getraclog' => 25,          // Added fallback command
      'racadm get System.ThermalSettings' => 20
    ];

    // Results array for all commands
    $results = [];
    $all_successful = true;

    // Create temporary password file - security improved with uniqid
    $temp_password_file = tempnam(sys_get_temp_dir(), 'ssh_' . uniqid());
    if (!$temp_password_file) {
      throw new Exception('Failed to create temporary password file');
    }

    try {
      // Write password to file with error checking
      if (file_put_contents($temp_password_file, $ipmi_password) === false) {
        throw new Exception('Failed to write to temporary password file');
      }

      chmod($temp_password_file, 0600); // Secure the file

      // Process each command
      foreach ($commands as $command) {
        // Prepare cache key for this command
        $cache_key = md5($ipmi_address . $ipmi_username . $command);
        $cache_file = sys_get_temp_dir() . "/idrac_cache_{$cache_key}.json";
        $cache_ttl = 60; // Default cache TTL in seconds

        // Adjust cache TTL based on command type
        if (strpos($command, 'version') !== false ||
            strpos($command, 'getconfig') !== false ||
            strpos($command, 'System.ServerInfo') !== false) {
          $cache_ttl = 300; // 5 minutes for static information
        } else if (strpos($command, 'hwinventory') !== false) {
          $cache_ttl = 1800; // 30 minutes for hardware inventory
        }

        // Check if we have a valid cached result and bypass_cache is false
        if (!$bypass_cache && file_exists($cache_file) && (time() - filemtime($cache_file) < $cache_ttl)) {
          $cached_data = json_decode(file_get_contents($cache_file), true);
          if ($cached_data) {
            $results[$command] = [
              'success' => true,
              'command' => $command,
              'raw_output' => $cached_data['raw_output'],
              'processed_output' => $cached_data['processed_output'],
              'cached' => true,
              'cache_time' => filemtime($cache_file)
            ];

            // Skip to next command
            continue;
          }
        }

        // Verify command is allowed
        $commandAllowed = false;
        $timeout = 30; // Default timeout

        foreach ($allowed_commands as $allowed => $cmd_timeout) {
          if (strpos($command, $allowed) === 0) {
            $commandAllowed = true;
            $timeout = $cmd_timeout;
            break;
          }
        }

        if (!$commandAllowed) {
          $results[$command] = [
            'success' => false,
            'command' => $command,
            'error' => 'Command not allowed for security reasons',
            'execution_time' => 0
          ];
          $all_successful = false;
          continue;
        }

        // Prepare SSH command with proper escaping
        $ipmi_address_clean = escapeshellarg($ipmi_address);
        $ipmi_username_clean = escapeshellarg($ipmi_username);
        $command_clean = escapeshellarg($command);
        $password_file_clean = escapeshellarg($temp_password_file);

        // Start timing for this command
        $start_time = microtime(true);

        // Combined command with timeout
        $full_cmd = "timeout {$timeout}s sshpass -f {$password_file_clean} " .
                   "ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 -o ServerAliveInterval=5 " .
                   "{$ipmi_username_clean}@{$ipmi_address_clean} {$command_clean} 2>&1";

        // Execute command
        $output = [];
        $return_var = 0;
        exec($full_cmd, $output, $return_var);

        // Calculate execution time
        $execution_time = microtime(true) - $start_time;

        // Check for errors
        if ($return_var === 124) {
          $results[$command] = [
            'success' => false,
            'command' => $command,
            'error' => "Command timed out after {$timeout} seconds",
            'execution_time' => $execution_time
          ];
          $all_successful = false;
          continue;
        } elseif ($return_var !== 0) {
          $error_message = !empty($output) ? implode(" ", $output) : "Unknown error (code {$return_var})";
          $results[$command] = [
            'success' => false,
            'command' => $command,
            'error' => "Command execution failed: {$error_message}",
            'execution_time' => $execution_time
          ];
          $all_successful = false;
          continue;
        }

        // Process output based on command type
        $processed_output = [];

        if (strpos($command, 'hwinventory') !== false) {
          $processed_output = process_hwinventory_output($output);
        }
        elseif (strpos($command, 'getsensorinfo') !== false) {
          $processed_output = process_sensorinfo_output($output);
        }
        elseif (strpos($command, 'getsel') !== false || strpos($command, 'getraclog') !== false) {
          $processed_output = $output; // Just return raw output for logs
        }
        else {
          $processed_output = $output; // For other commands, just return raw output
        }

        // Cache the result
        $cache_data = [
          'raw_output' => $output,
          'processed_output' => $processed_output,
          'timestamp' => time()
        ];

        file_put_contents($cache_file, json_encode($cache_data));

        // Add to results
        $results[$command] = [
          'success' => true,
          'command' => $command,
          'raw_output' => $output,
          'processed_output' => $processed_output,
          'execution_time' => $execution_time,
          'cached' => false
        ];
      }

      // Return all results
      header('Content-Type: application/json');

      // Add cache control headers to allow browser caching
      header('Cache-Control: private, max-age=60');

      echo json_encode([
        'success' => $all_successful,
        'results' => $results,
        'total_execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
      ]);

    } finally {
      // Always clean up the temporary file
      if (file_exists($temp_password_file)) {
        unlink($temp_password_file);
      }
    }

  } catch (Exception $e) {
    error_log("Error executing iDRAC batch commands: " . $e->getMessage());

    // Clean up temporary file if it exists
    if (isset($temp_password_file) && file_exists($temp_password_file)) {
      unlink($temp_password_file);
    }

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage(),
      'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
    ]);
  }
}

// Optimized version of the original execute_idrac_command function with improved caching
elseif($_GET['f'] == 'execute_idrac_command'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data from request
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields with single check
    if (empty($data['ipmi_address']) || empty($data['command'])) {
      throw new Exception(empty($data['ipmi_address']) ? 'iDRAC IP address is required' : 'Command to execute is required');
    }

    // Extract all parameters with defaults in one block
    $server_id = $data['server_id'] ?? null;
    $server_type = $data['server_type'] ?? 'dedicated';
    $ipmi_address = $data['ipmi_address'];
    $ipmi_username = $data['ipmi_username'] ?? 'root';
    $ipmi_password = $data['ipmi_password'] ?? '';
    $idrac_version = $data['idrac_version'] ?? 8;
    $command = $data['command'];
    $bypass_cache = $data['bypass_cache'] ?? false;

    // Define hash key for caching
    $cache_key = md5($ipmi_address . $ipmi_username . $command);
    $cache_file = sys_get_temp_dir() . "/idrac_cache_{$cache_key}.json";

    // Dynamic cache TTL based on command type
    $cache_ttl = 60; // Default 60 seconds

    // Adjust TTL based on command type
    if (strpos($command, 'version') !== false ||
        strpos($command, 'getconfig') !== false ||
        strpos($command, 'System.ServerInfo') !== false) {
      $cache_ttl = 300; // 5 minutes for static information
    } else if (strpos($command, 'hwinventory') !== false) {
      $cache_ttl = 1800; // 30 minutes for hardware inventory
    }

    // Check if we have a valid cached result and bypass_cache is false
    if (!$bypass_cache && file_exists($cache_file) && (time() - filemtime($cache_file) < $cache_ttl)) {
      $cached_data = json_decode(file_get_contents($cache_file), true);
      if ($cached_data) {
        header('Content-Type: application/json');
        // Add cache control headers to help browser caching
        header('Cache-Control: private, max-age=' . $cache_ttl);
        echo json_encode([
          'success' => true,
          'command' => $command,
          'raw_output' => $cached_data['raw_output'],
          'processed_output' => $cached_data['processed_output'],
          'cached' => true,
          'cache_time' => filemtime($cache_file)
        ]);
        exit;
      }
    }

    // White list of allowed commands with associated timeouts
    $allowed_commands = [
      'racadm hwinventory' => 60,        // More complex hardware inventory needs more time
      'racadm getsensorinfo' => 45,      // Sensor data typically returns quickly
      'racadm getsel' => 25,             // System event logs can be large
      'racadm raid get pdisks -o' => 45, // RAID disk info can be slow
      'racadm raid get controllers' => 30,
      'racadm get System.ServerInfo' => 15,
      'racadm version' => 10,
      'racadm getconfig -g idRacInfo' => 15,
      'racadm getniccfg' => 15,
      'racadm get iDRAC.NIC' => 15,
      'racadm getsysinfo' => 15,
      'racadm get System.MemorySettings' => 20,
      'racadm getraclog' => 25,          // Added fallback command
      'racadm get System.ThermalSettings' => 20
    ];

    // Check if the requested command is in the whitelist
    $commandAllowed = false;
    $timeout = 30; // Default timeout

    foreach ($allowed_commands as $allowed => $cmd_timeout) {
      if (strpos($command, $allowed) === 0) {
        $commandAllowed = true;
        $timeout = $cmd_timeout;
        break;
      }
    }

    if (!$commandAllowed) {
      throw new Exception('Command not allowed for security reasons');
    }

    // Create temporary password file - security improved with uniqid
    $temp_password_file = tempnam(sys_get_temp_dir(), 'ssh_' . uniqid());
    if (!$temp_password_file) {
      throw new Exception('Failed to create temporary password file');
    }

    try {
      // Write password to file with error checking
      if (file_put_contents($temp_password_file, $ipmi_password) === false) {
        throw new Exception('Failed to write to temporary password file');
      }

      chmod($temp_password_file, 0600); // Secure the file

      // Prepare SSH command with proper escaping
      $ipmi_address_clean = escapeshellarg($ipmi_address);
      $ipmi_username_clean = escapeshellarg($ipmi_username);
      $command_clean = escapeshellarg($command);
      $password_file_clean = escapeshellarg($temp_password_file);

      // Combined command with timeout and no unnecessary test connection
      // Added timeout command to prevent hanging
      $full_cmd = "timeout {$timeout}s sshpass -f {$password_file_clean} " .
                 "ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 -o ServerAliveInterval=5 " .
                 "{$ipmi_username_clean}@{$ipmi_address_clean} {$command_clean} 2>&1";

      // Execute with better error capture
      $output = [];
      $return_var = 0;
      exec($full_cmd, $output, $return_var);

      // Check for timeout or other failures
      if ($return_var === 124) {
        throw new Exception("Command timed out after {$timeout} seconds");
      } elseif ($return_var !== 0) {
        // Check if output contains useful error information
        $error_message = !empty($output) ? implode(" ", $output) : "Unknown error (code {$return_var})";
        throw new Exception("Command execution failed: {$error_message}");
      }

      // Process output based on command type - optimized with better pattern matching
      $processed_output = [];

      if (strpos($command, 'hwinventory') !== false) {
        // Optimized hardware inventory processing
        $processed_output = process_hwinventory_output($output);
      }
      elseif (strpos($command, 'getsensorinfo') !== false) {
        // Optimized sensor info processing
        $processed_output = process_sensorinfo_output($output);
      }
      elseif (strpos($command, 'getsel') !== false || strpos($command, 'getraclog') !== false) {
        // Optimized event log processing - just return raw output
        // The client side will handle parsing for better performance
        $processed_output = $output;
      }
      else {
        // For other commands, just return the raw output
        $processed_output = $output;
      }

      // Cache the result
      $cache_data = [
        'raw_output' => $output,
        'processed_output' => $processed_output,
        'timestamp' => time()
      ];

      file_put_contents($cache_file, json_encode($cache_data));

      // Return the results
      header('Content-Type: application/json');
      // Add cache control headers to help browser caching
      header('Cache-Control: private, max-age=' . $cache_ttl);
      echo json_encode([
        'success' => true,
        'command' => $command,
        'raw_output' => $output,
        'processed_output' => $processed_output,
        'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
      ]);

    } finally {
      // Always clean up the temporary file in the finally block
      if (file_exists($temp_password_file)) {
        unlink($temp_password_file);
      }
    }

  } catch (Exception $e) {
    $error_details = "Error executing iDRAC command: " . $e->getMessage();
    if (!empty($output)) {
      $error_details .= " | Command output: " . json_encode(array_slice($output, 0, 10));
    }
    error_log($error_details);

    // Clean up temporary file if it exists
    if (isset($temp_password_file) && file_exists($temp_password_file)) {
      unlink($temp_password_file);
    }

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage(),
      'command' => $command ?? '',
      'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
    ]);
  }
}


elseif($_GET['f'] == 'power_control'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data from request
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (empty($data['ipmi_address']) || empty($data['action'])) {
      throw new Exception(empty($data['ipmi_address']) ? 'iDRAC IP address is required' : 'Power action is required');
    }

    // Extract parameters with defaults
    $server_id = $data['server_id'] ?? null;
    $server_type = $data['server_type'] ?? 'dedicated';
    $ipmi_address = $data['ipmi_address'];
    $ipmi_username = $data['ipmi_username'] ?? 'root';
    $ipmi_password = $data['ipmi_password'] ?? '';
    $action = strtolower($data['action']);

    // Validate action
    $valid_actions = ['on', 'off', 'restart', 'status'];
    if (!in_array($action, $valid_actions)) {
      throw new Exception('Invalid power action. Must be one of: ' . implode(', ', $valid_actions));
    }

    // Map action to command (simplified for example)
    $command_map = [
      'on' => 'racadm serveraction powerup',
      'off' => 'racadm serveraction powerdown',
      'restart' => 'racadm serveraction powercycle',
      'status' => 'racadm serveraction powerstatus'
    ];

    $command = $command_map[$action];

    // Create temporary password file
    $temp_password_file = tempnam(sys_get_temp_dir(), 'ssh_' . uniqid());
    if (!$temp_password_file) {
      throw new Exception('Failed to create temporary password file');
    }

    try {
      // Write password to file with error checking
      if (file_put_contents($temp_password_file, $ipmi_password) === false) {
        throw new Exception('Failed to write to temporary password file');
      }

      chmod($temp_password_file, 0600); // Secure the file

      // Prepare SSH command with proper escaping
      $ipmi_address_clean = escapeshellarg($ipmi_address);
      $ipmi_username_clean = escapeshellarg($ipmi_username);
      $command_clean = escapeshellarg($command);
      $password_file_clean = escapeshellarg($temp_password_file);

      // Set timeout based on action (power operations can take longer)
      $timeout = ($action == 'status') ? 15 : 45;

      // Combined command with timeout
      $full_cmd = "timeout {$timeout}s sshpass -f {$password_file_clean} " .
                 "ssh -o StrictHostKeyChecking=no -o ConnectTimeout=15 -o ServerAliveInterval=5 " .
                 "{$ipmi_username_clean}@{$ipmi_address_clean} {$command_clean} 2>&1";

      // Execute command
      $output = [];
      $return_var = 0;
      exec($full_cmd, $output, $return_var);

      // Check for timeout or other failures
      if ($return_var === 124) {
        throw new Exception("Command timed out after {$timeout} seconds");
      } elseif ($return_var !== 0) {
        $error_message = !empty($output) ? implode(" ", $output) : "Unknown error (code {$return_var})";
        throw new Exception("Command execution failed: {$error_message}");
      }

      // Process power status output
      if ($action == 'status') {
        $power_status = 'unknown';

        // Look for status in output
        foreach ($output as $line) {
          if (stripos($line, 'Server power status') !== false) {
            if (stripos($line, 'ON') !== false) {
              $power_status = 'on';
            } elseif (stripos($line, 'OFF') !== false) {
              $power_status = 'off';
            }
            break;
          }
        }

        // Return power status
        echo json_encode([
          'success' => true,
          'power_status' => $power_status,
          'raw_output' => $output
        ]);
      } else {
        // For power actions, just return success
        echo json_encode([
          'success' => true,
          'action' => $action,
          'message' => "Server power {$action} command executed successfully",
          'raw_output' => $output
        ]);
      }

      // Log the power action in database if server_id is provided
      if ($server_id) {
        $log_sql = "INSERT INTO server_power_log (server_id, server_type, action, timestamp, executed_by)
                    VALUES (:server_id, :server_type, :action, NOW(), :admin_id)";
        $log_sth = $pdo->prepare($log_sql);
        $log_sth->bindValue(':server_id', $server_id);
        $log_sth->bindValue(':server_type', $server_type);
        $log_sth->bindValue(':action', $action);
        $log_sth->bindValue(':admin_id', $admin_id);
        $log_sth->execute();
      }

    } finally {
      // Always clean up the temporary file
      if (file_exists($temp_password_file)) {
        unlink($temp_password_file);
      }
    }

  } catch (Exception $e) {
    error_log("Error in power_control: " . $e->getMessage());

    // Clean up temporary file if it exists
    if (isset($temp_password_file) && file_exists($temp_password_file)) {
      unlink($temp_password_file);
    }

    // Return error as JSON - ensure this always outputs valid JSON
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}


elseif($_GET['f'] == 'get_power_status'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data from request
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (empty($data['ipmi_address'])) {
      throw new Exception('IPMI address is required');
    }

    // Forward to power_control with status action
    $_GET['f'] = 'power_control';
    $data['action'] = 'status';

    // Re-encode data
    file_put_contents('php://input', json_encode($data));

    // Call power_control function
    require __DIR__ . '/power_control.php';

  } catch (Exception $e) {
    error_log("Error in get_power_status: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}



// Get switch ports
elseif($_GET['f'] == 'get_switch_ports'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['switch_id']) || empty($data['switch_id'])) {
      throw new Exception('Switch ID is required');
    }

    $switch_id = intval($data['switch_id']);

    // Modified query to better determine availability

    // Get ports from database with device labels
    $portsQuery = $pdo->prepare("
      SELECT
        sp.*,
        CASE
          WHEN sp.connected_device_type = 'blade' THEN bs.label
          WHEN sp.connected_device_type = 'dedicated' THEN ds.label
          ELSE NULL
        END AS connected_device_label
      FROM inventory_switch_ports sp
      LEFT JOIN blade_server_inventory bs ON sp.connected_device_type = 'blade' AND sp.connected_device_id = bs.id
      LEFT JOIN inventory_dedicated_servers ds ON sp.connected_device_type = 'dedicated' AND sp.connected_device_id = ds.id
      WHERE sp.switch_id = :switch_id
      ORDER BY CASE
        WHEN port_number REGEXP '^[0-9]+$' THEN CAST(port_number AS UNSIGNED)
        ELSE 999999
      END,
      port_number
    ");
    $portsQuery->bindValue(':switch_id', $switch_id, PDO::PARAM_INT);
    $portsQuery->execute();

    $ports = $portsQuery->fetchAll(PDO::FETCH_ASSOC);

    // Return ports data
    header('Content-Type: application/json');
    echo json_encode($ports);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}


elseif($_GET['f'] == 'discover_switch_ports'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data from the request
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['switch_id']) || empty($data['switch_id'])) {
      throw new Exception('Switch ID is required');
    }

    $switch_id = intval($data['switch_id']);
    $snmp_community = isset($data['snmp_community']) ? $data['snmp_community'] : null;
    $snmp_version = isset($data['snmp_version']) ? intval($data['snmp_version']) : 2;

    // Get switch information
    $getSwitchSql = "SELECT * FROM inventory_switches WHERE id = :switch_id";
    $getSwitchSth = $pdo->prepare($getSwitchSql);
    $getSwitchSth->bindValue(':switch_id', $switch_id);
    $getSwitchSth->execute();

    $switchData = $getSwitchSth->fetch(PDO::FETCH_ASSOC);
    if (!$switchData) {
      throw new Exception("Switch with ID $switch_id not found");
    }

    // If SNMP community not provided in request, use the one from the switch record
    if (empty($snmp_community) && !empty($switchData['snmp_community'])) {
      $snmp_community = $switchData['snmp_community'];
    }

    if (empty($snmp_community)) {
      throw new Exception("SNMP community string is required");
    }

    if (empty($switchData['switch_ip'])) {
      throw new Exception("Switch IP address is not set for switch ID $switch_id");
    }

    // NEW CODE: Check if any ports for this switch are already assigned to devices
    try {
      $checkPortsQuery = $pdo->prepare("
        SELECT COUNT(*) as assigned_count
        FROM inventory_switch_ports
        WHERE switch_id = :switch_id AND connected_device_id IS NOT NULL
      ");
      $checkPortsQuery->bindValue(':switch_id', $switch_id);
      $checkPortsQuery->execute();

      $result = $checkPortsQuery->fetch(PDO::FETCH_ASSOC);
      $assignedPortsCount = $result['assigned_count'];

      if ($assignedPortsCount > 0) {
        throw new Exception(
          "Cannot rediscover ports: {$assignedPortsCount} ports on this switch are currently assigned to devices. " .
          "Rediscovery is not allowed to prevent data loss. " .
          "Please unassign all ports from devices before rediscovering."
        );
      }

      // NEW CODE: Delete all existing ports since no ports are assigned
      $deletePortsSql = "DELETE FROM inventory_switch_ports WHERE switch_id = :switch_id";
      $deletePortsSth = $pdo->prepare($deletePortsSql);
      $deletePortsSth->bindValue(':switch_id', $switch_id);
      $deletePortsSth->execute();

      $deletedPortsCount = $deletePortsSth->rowCount();
      error_log("Deleted {$deletedPortsCount} existing ports for switch ID: {$switch_id} before rediscovery");

    } catch (Exception $e) {
      // Only rethrow if it's our custom exception, not a DB error
      if (strpos($e->getMessage(), "Cannot rediscover ports") === 0) {
        throw $e;
      }
      // For DB errors, just log and continue (non-critical check)
      error_log("Error checking port assignments: " . $e->getMessage());
    }

    // Set SNMP version
    switch($snmp_version) {
      case 1:
        $version = SNMP::VERSION_1;
        break;
      case 3:
        $version = SNMP::VERSION_3;
        break;
      default:
        $version = SNMP::VERSION_2C;
    }

    // Create SNMP session
    try {
      // Set timeouts to avoid hanging
      $timeout = 1000000; // 1 second
      $retries = 3;

      $session = new SNMP($version, $switchData['switch_ip'], $snmp_community, $timeout, $retries);
      $session->valueretrieval = SNMP_VALUE_PLAIN;
      $session->quick_print = true;

      // Test SNMP connectivity
      try {
        $sysName = $session->get('.*******.*******.0'); // sysName
        if (!$sysName) {
          throw new Exception("Could not get system name from switch");
        }
      } catch (Exception $e) {
        throw new Exception("SNMP connectivity test failed: " . $e->getMessage());
      }

      // Get interface data using different OIDs
      // 1. Get all interface indices
      $ifIndices = [];
      try {
        $indices = $session->walk('.*******.*******.1.1'); // ifIndex
        foreach ($indices as $oid => $value) {
          $parts = explode('.', $oid);
          $index = end($parts);
          $ifIndices[$index] = trim($value, '"');
        }
      } catch (Exception $e) {
        throw new Exception("Failed to get interface indices: " . $e->getMessage());
      }

      // 2. Get interface names/descriptions
      $ifDescriptions = [];
      $ifNames = [];
      $ifTypes = [];
      $ifSpeeds = [];
      $ifOperStatus = [];

      try {
        // Standard MIB-II descriptions
        $descriptions = $session->walk('.*******.*******.1.2'); // ifDescr
        foreach ($descriptions as $oid => $value) {
          $parts = explode('.', $oid);
          $index = end($parts);
          $ifDescriptions[$index] = trim($value, '"');
        }
      } catch (Exception $e) {
        // Non-critical
      }

      try {
        // IF-MIB names (better for newer devices)
        $names = $session->walk('.*******.********.1.1.1'); // ifName
        foreach ($names as $oid => $value) {
          $parts = explode('.', $oid);
          $index = end($parts);
          $ifNames[$index] = trim($value, '"');
        }
      } catch (Exception $e) {
        // Non-critical
      }

      try {
        // Interface types
        $types = $session->walk('.*******.*******.1.3'); // ifType
        foreach ($types as $oid => $value) {
          $parts = explode('.', $oid);
          $index = end($parts);
          $ifTypes[$index] = trim($value, '"');
        }
      } catch (Exception $e) {
        // Non-critical
      }

      try {
        // Interface speeds (in bits per second)
        $speeds = $session->walk('.*******.*******.1.5'); // ifSpeed
        foreach ($speeds as $oid => $value) {
          $parts = explode('.', $oid);
          $index = end($parts);
          // Convert bps to Mbps
          $ifSpeeds[$index] = intval(trim($value, '"')) / 1000000;
        }
      } catch (Exception $e) {
        // Non-critical
      }

      try {
        // Interface operational status (1=up, 2=down)
        $operStatus = $session->walk('.*******.*******.1.8'); // ifOperStatus
        foreach ($operStatus as $oid => $value) {
          $parts = explode('.', $oid);
          $index = end($parts);
          $ifOperStatus[$index] = trim($value, '"');
        }
      } catch (Exception $e) {
        // Non-critical
      }

      // For high-speed interfaces (>4.2Gbps), try to get ifHighSpeed
      $ifHighSpeeds = [];
      try {
        $highSpeeds = $session->walk('.*******.********.1.1.15'); // ifHighSpeed (in Mbps)
        foreach ($highSpeeds as $oid => $value) {
          $parts = explode('.', $oid);
          $index = end($parts);
          $ifHighSpeeds[$index] = intval(trim($value, '"'));
        }
      } catch (Exception $e) {
        // Non-critical
      }

      // Process and filter interfaces
      $ports = [];
      $portsDetected = 0;

      foreach ($ifIndices as $index => $ifIndex) {
        // Skip loopback, virtual, and management interfaces based on name or type
        $name = isset($ifNames[$index]) ? $ifNames[$index] : '';
        $descr = isset($ifDescriptions[$index]) ? $ifDescriptions[$index] : '';
        $type = isset($ifTypes[$index]) ? $ifTypes[$index] : '';

        // Skip interfaces that are likely not physical ports
        if (empty($name) && empty($descr)) {
          continue; // Skip unnamed interfaces
        }

        // Skip loopback, management, and virtual interfaces
        if (preg_match('/loopback|loop|null|virtual|management|mgmt|vlan|trunk|port-channel|lag|bond/i', $name . ' ' . $descr)) {
          continue;
        }

        // Skip very slow interfaces (likely console or management)
        $speed = isset($ifHighSpeeds[$index]) && $ifHighSpeeds[$index] > 0 ?
                $ifHighSpeeds[$index] :
                (isset($ifSpeeds[$index]) ? $ifSpeeds[$index] : 0);

        if ($speed < 10 && !preg_match('/ethernet|eth/i', $name . ' ' . $descr)) {
          continue; // Skip non-Ethernet interfaces with speed less than 10 Mbps
        }

        // Determine port status
        $status = 'Unknown';
        if (isset($ifOperStatus[$index])) {
          if ($ifOperStatus[$index] == '1') {
            $status = 'Available'; // Up and available
          } else if ($ifOperStatus[$index] == '2') {
            $status = 'Down'; // Down
          }
        }

        // Use ifName if available, otherwise use ifDescr
        $portNumber = $name ?: $descr;

        // Try to extract numeric port number for sorting
        $numericPort = '';
        if (preg_match('/(\d+)/', $portNumber, $matches)) {
          $numericPort = $matches[1];
        }

        // Clean up port name
        $portName = preg_replace('/^(GigabitEthernet|TenGigabitEthernet|Ethernet|FastEthernet|eth|ge\-|xe\-)/i', '', $name ?: $descr);
        $portName = trim($portName);

        // Determine port type based on speed or name
        $portType = 'Ethernet';
        if ($speed >= 10000) {
          $portType = 'TenGigabitEthernet';
        } else if ($speed >= 1000) {
          $portType = 'GigabitEthernet';
        } else if ($speed >= 100) {
          $portType = 'FastEthernet';
        }

        // Override based on name if available
        if (preg_match('/tengig|10g|xe/i', $name . ' ' . $descr)) {
          $portType = 'TenGigabitEthernet';
          if ($speed == 0) $speed = 10000;
        } else if (preg_match('/gig|1g|ge/i', $name . ' ' . $descr)) {
          $portType = 'GigabitEthernet';
          if ($speed == 0) $speed = 1000;
        } else if (preg_match('/fast|100m|fe/i', $name . ' ' . $descr)) {
          $portType = 'FastEthernet';
          if ($speed == 0) $speed = 100;
        }

        $ports[] = [
          'index' => $index,
          'port_number' => $portNumber,
          'port_name' => $portName,
          'port_type' => $portType,
          'max_speed' => $speed,
          'current_speed' => $speed, // Assume current speed is the same as max speed
          'status' => $status,
          'numeric_port' => $numericPort
        ];

        $portsDetected++;
      }

      // Sort ports by their numeric part for better readability
      usort($ports, function($a, $b) {
        // If both have numeric parts, compare them
        if ($a['numeric_port'] !== '' && $b['numeric_port'] !== '') {
          return intval($a['numeric_port']) - intval($b['numeric_port']);
        }
        // If only one has a numeric part, prioritize it
        if ($a['numeric_port'] !== '') return -1;
        if ($b['numeric_port'] !== '') return 1;
        // Otherwise fall back to string comparison
        return strcmp($a['port_number'], $b['port_number']);
      });

      // Begin transaction - we don't need to get existing ports since we've deleted them
      $pdo->beginTransaction();

      $insertPortSql = "INSERT INTO inventory_switch_ports (
                        switch_id, port_number, port_name, port_type, max_speed,
                        current_speed, status, last_updated
                      ) VALUES (
                        :switch_id, :port_number, :port_name, :port_type, :max_speed,
                        :current_speed, :status, NOW()
                      )";

      $insertPortSth = $pdo->prepare($insertPortSql);
      $portsAdded = 0;

      foreach ($ports as $port) {
        // Prepare parameters
        $params = [
          ':switch_id' => $switch_id,
          ':port_number' => $port['port_number'],
          ':port_name' => $port['port_name'],
          ':port_type' => $port['port_type'],
          ':max_speed' => $port['max_speed'],
          ':current_speed' => $port['current_speed'],
          ':status' => $port['status']
        ];

        // Insert new port
        $insertPortSth->execute($params);
        $portsAdded++;
      }

      // Commit transaction
      $pdo->commit();

      // Return success
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'ports_detected' => $portsDetected,
        'ports_added' => $portsAdded,
        'message' => "Successfully discovered $portsDetected ports ($portsAdded added)",

        // Add a marker that the client can use to trigger UI updates
        'action' => 'ports-discovered',
        'switch_id' => $switch_id
      ]);

      // Close the SNMP session
      $session->close();

    } catch (SNMPException $e) {
      if ($pdo->inTransaction()) {
        $pdo->rollBack();
      }
      throw new Exception("SNMP Error: " . $e->getMessage());
    } catch (Exception $e) {
      if ($pdo->inTransaction()) {
        $pdo->rollBack();
      }
      throw $e;
    }
  } catch (Exception $e) {
    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}




// Assign port to server - Enhanced version to support switch assignment
elseif($_GET['f'] == 'assign_port_to_server'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['port_id']) || empty($data['port_id'])) {
      throw new Exception('Port ID is required');
    }
    if (!isset($data['device_id']) || empty($data['device_id'])) {
      throw new Exception('Device ID is required');
    }
    if (!isset($data['device_type']) || empty($data['device_type'])) {
      throw new Exception('Device type is required');
    }

    $port_id = intval($data['port_id']);
    $device_id = intval($data['device_id']);
    $device_type = $data['device_type'];

    // Get port information
    $getPortSql = "SELECT * FROM inventory_switch_ports WHERE id = :port_id";
    $getPortSth = $pdo->prepare($getPortSql);
    $getPortSth->bindValue(':port_id', $port_id);
    $getPortSth->execute();

    $port = $getPortSth->fetch(PDO::FETCH_ASSOC);

    if (!$port) {
      throw new Exception('Port not found');
    }

    if ($port['connected_device_id'] || $port['status'] === 'Disabled') {
      throw new Exception('Port is not available for assignment (status: ' . $port['status'] . ')');
    }

    // Begin transaction
    $pdo->beginTransaction();

    // Update port assignment
    $updatePortSql = "UPDATE inventory_switch_ports SET
                      connected_device_id = :device_id,
                      connected_device_type = :device_type,
                      status = 'Used'
                    WHERE id = :port_id";
    $updatePortSth = $pdo->prepare($updatePortSql);
    $updatePortSth->bindValue(':port_id', $port_id);
    $updatePortSth->bindValue(':device_id', $device_id);
    $updatePortSth->bindValue(':device_type', $device_type);
    $updatePortSth->execute();

    // Also update the server record
    if ($device_type === 'blade') {
      $serverTable = 'blade_server_inventory';
    } else if ($device_type === 'dedicated') {
      $serverTable = 'inventory_dedicated_servers';
    } else {
      throw new Exception('Unsupported device type');
    }

    // Find which port field to update (port1, port2, etc.)
    // We'll get the current server record first
    $getServerSql = "SELECT * FROM $serverTable WHERE id = :device_id";
    $getServerSth = $pdo->prepare($getServerSql);
    $getServerSth->bindValue(':device_id', $device_id);
    $getServerSth->execute();

    $server = $getServerSth->fetch(PDO::FETCH_ASSOC);

    if (!$server) {
      // Rollback and exit
      $pdo->rollBack();
      throw new Exception('Server not found');
    }

    // Determine which port field to use
    $portField = 'port1';
    $portSpeedField = 'port1_speed';


    // Update the server with the port information
    // Always set the switch_id from the port's switch_id
    $updateServerSql = "UPDATE $serverTable SET
                        switch_id = :switch_id,
                        $portField = :port_number,
                        $portSpeedField = :port_speed
                      WHERE id = :device_id";
    $updateServerSth = $pdo->prepare($updateServerSql);
    $updateServerSth->bindValue(':switch_id', $port['switch_id']);
    $updateServerSth->bindValue(':port_number', $port['port_number']);
    $updateServerSth->bindValue(':port_speed', $port['max_speed']);
    $updateServerSth->bindValue(':device_id', $device_id);
    $updateServerSth->execute();

    // Commit transaction
    $pdo->commit();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Port assigned successfully',
      'port_field' => $portField,
      'switch_id' => $port['switch_id'],
      'switch_updated' => true
    ]);

  } catch (Exception $e) {
    // Rollback transaction if it was started
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}






// Toggle port status (enable/disable) using external SNMP tools
elseif($_GET['f'] == 'toggle_port_status'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data from the request
    $data = json_decode(file_get_contents('php://input'), true);

    // Log the request
    error_log("Toggle port status request: " . json_encode($data));

    // Validate required fields
    if (!isset($data['port_id']) || empty($data['port_id'])) {
      throw new Exception('Port ID is required');
    }

    if (!isset($data['action']) || !in_array($data['action'], ['enable', 'disable'])) {
      throw new Exception('Invalid action. Must be "enable" or "disable"');
    }

    $port_id = intval($data['port_id']);
    $action = $data['action'];

    // Get port details
    $getPortSql = "SELECT * FROM inventory_switch_ports WHERE id = :port_id";
    $getPortSth = $pdo->prepare($getPortSql);
    $getPortSth->bindValue(':port_id', $port_id);
    $getPortSth->execute();

    $portData = $getPortSth->fetch(PDO::FETCH_ASSOC);

    if (!$portData) {
      throw new Exception('Port not found');
    }

    // Get switch details
    $getSwitchSql = "SELECT * FROM inventory_switches WHERE id = :switch_id";
    $getSwitchSth = $pdo->prepare($getSwitchSql);
    $getSwitchSth->bindValue(':switch_id', $portData['switch_id']);
    $getSwitchSth->execute();

    $switchData = $getSwitchSth->fetch(PDO::FETCH_ASSOC);

    if (!$switchData) {
      throw new Exception('Switch not found');
    }

    // Check if snmpset command exists on the server
    exec('which snmpset 2>&1', $whichOutput, $whichReturnVal);
    $hasSnmpSet = ($whichReturnVal === 0);

    if (!$hasSnmpSet) {
      error_log("snmpset command not found - SNMP operations will be skipped");
    }

    // Validate SNMP configuration
    $snmpIp = isset($switchData['switch_ip']) ? trim($switchData['switch_ip']) : '';
    $snmpCommunity = isset($switchData['snmp_community']) ? trim($switchData['snmp_community']) : '';
    $snmpVersion = isset($switchData['snmp_version']) ? intval($switchData['snmp_version']) : 2;

    $canPerformSnmp = $hasSnmpSet && !empty($snmpIp) && !empty($snmpCommunity);

    if (!$canPerformSnmp) {
      error_log("Cannot perform SNMP - missing requirements: " .
                (!$hasSnmpSet ? "snmpset command not found; " : "") .
                (empty($snmpIp) ? "switch IP missing; " : "") .
                (empty($snmpCommunity) ? "SNMP community missing; " : ""));
    }

    // Determine the new status for database
    $newDbStatus = ($action == 'enable') ? 'Available' : 'Disabled';

    // If port is currently used, keep it as "Used" even when enabled
    if ($newDbStatus == 'Available' && $portData['connected_device_id']) {
      $newDbStatus = 'Used';
    }

    // Always update the database first
    $updateSql = "UPDATE inventory_switch_ports
                 SET status = :status
                 WHERE id = :port_id";
    $updateSth = $pdo->prepare($updateSql);
    $updateSth->bindValue(':port_id', $port_id);
    $updateSth->bindValue(':status', $newDbStatus);
    $updateSth->execute();

    error_log("Database updated with new status: $newDbStatus");

    // Try to perform SNMP operation if possible
    $snmpSuccess = false;
    $snmpOutput = [];
    $snmpError = '';

    if ($canPerformSnmp) {
      // Get the port number to use as ifIndex
      $portNumber = $portData['port_number'];
      $ifIndex = null;

      // Clean up port number and try to extract the numeric part
      if (is_numeric($portNumber)) {
        $ifIndex = intval($portNumber);
      } else {
        if (preg_match('/(\d+)/', $portNumber, $matches)) {
          $ifIndex = intval($matches[1]);
        }
      }

      if ($ifIndex !== null) {
        // ifAdminStatus OID
        $oid = ".*******.*******.1.7.$ifIndex";

        // 1 = up, 2 = down
        $statusValue = ($action == 'enable') ? 1 : 2;

        // Construct the snmpset command
        $versionParam = ($snmpVersion == 1) ? '-v1' : '-v2c';

        // Escape SNMP community string to prevent command injection
        $escapedCommunity = escapeshellarg($snmpCommunity);
        $escapedIp = escapeshellarg($snmpIp);

        // Create the command
        $cmd = "snmpset $versionParam -c $escapedCommunity $escapedIp $oid i $statusValue 2>&1";
        error_log("Executing SNMP command: " . preg_replace('/-c\s+[^\s]+/', '-c [REDACTED]', $cmd));

        // Execute the command
        exec($cmd, $snmpOutput, $snmpReturnVal);

        $outputStr = implode("\n", $snmpOutput);
        error_log("SNMP command output: $outputStr (return code: $snmpReturnVal)");

        // Check if the command was successful
        $snmpSuccess = ($snmpReturnVal === 0);

        if (!$snmpSuccess) {
          $snmpError = "SNMP command failed with code $snmpReturnVal: $outputStr";
          error_log($snmpError);
        }
      } else {
        $snmpError = "Could not determine SNMP index from port number: $portNumber";
        error_log($snmpError);
      }
    }

    // Return response based on SNMP operation status
    header('Content-Type: application/json');

    if ($canPerformSnmp && !$snmpSuccess) {
      // SNMP was attempted but failed
      echo json_encode([
        'success' => true, // Still consider it successful for UI purposes
        'message' => "Port status updated in database, but SNMP operation failed",
        'new_status' => $newDbStatus,
        'port_id' => $port_id,
        'port_number' => $portData['port_number'],
        'switch_id' => $portData['switch_id'],
        'snmp_attempted' => true,
        'snmp_success' => false,
        'snmp_error' => $snmpError,
        'snmp_output' => $snmpOutput,
        'warning' => "The switch port status may not reflect this change"
      ]);
    } else if ($canPerformSnmp && $snmpSuccess) {
      // SNMP was successful
      echo json_encode([
        'success' => true,
        'message' => "Port " . ($action == 'enable' ? 'enabled' : 'disabled') . " successfully",
        'new_status' => $newDbStatus,
        'port_id' => $port_id,
        'port_number' => $portData['port_number'],
        'switch_id' => $portData['switch_id'],
        'snmp_attempted' => true,
        'snmp_success' => true,
        'snmp_output' => $snmpOutput
      ]);
    } else {
      // SNMP was not attempted
      echo json_encode([
        'success' => true,
        'message' => "Port status updated in database only",
        'new_status' => $newDbStatus,
        'port_id' => $port_id,
        'port_number' => $portData['port_number'],
        'switch_id' => $portData['switch_id'],
        'snmp_attempted' => false,
        'warning' => "The switch port status will not be changed - missing SNMP configuration"
      ]);
    }

  } catch (Exception $e) {
    $errorMsg = $e->getMessage();
    error_log("Error in toggle_port_status: $errorMsg");

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $errorMsg
    ]);
  }
}


elseif ($_GET['f'] == 'refresh_port_status') {
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['switch_id']) || empty($data['switch_id'])) {
      throw new Exception('Switch ID is required');
    }

    $switch_id = intval($data['switch_id']);
    $port_id = isset($data['port_id']) ? intval($data['port_id']) : null;

    // Call the function to refresh port status
    $result = refreshPortStatus($switch_id, $port_id);

    // Return success
    header('Content-Type: application/json');
    echo json_encode($result);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}


// Add manual port to switch
elseif($_GET['f'] == 'add_manual_port'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['switch_id']) || empty($data['switch_id'])) {
      throw new Exception('Switch ID is required');
    }
    if (!isset($data['port_number']) || empty($data['port_number'])) {
      throw new Exception('Port number is required');
    }

    $switch_id = intval($data['switch_id']);
    $port_number = trim($data['port_number']);
    $port_name = isset($data['port_name']) ? trim($data['port_name']) : '';
    $port_type = isset($data['port_type']) ? trim($data['port_type']) : 'Ethernet';
    $max_speed = isset($data['max_speed']) && is_numeric($data['max_speed']) ? intval($data['max_speed']) : null;
    $status = isset($data['status']) ? trim($data['status']) : 'Available';

    // Validate port type
    $allowedPortTypes = ['Ethernet', 'FastEthernet', 'GigabitEthernet', 'TenGigabitEthernet', 'Management'];
    if (!in_array($port_type, $allowedPortTypes)) {
      $port_type = 'Ethernet';
    }

    // Validate status
    $allowedStatuses = ['Available', 'Disabled', 'Down'];
    if (!in_array($status, $allowedStatuses)) {
      $status = 'Available';
    }

    // Check if switch exists
    $checkSwitchSql = "SELECT id, label FROM inventory_switches WHERE id = :switch_id";
    $checkSwitchSth = $pdo->prepare($checkSwitchSql);
    $checkSwitchSth->bindValue(':switch_id', $switch_id);
    $checkSwitchSth->execute();

    if ($checkSwitchSth->rowCount() === 0) {
      throw new Exception("Switch with ID $switch_id not found");
    }

    $switchData = $checkSwitchSth->fetch(PDO::FETCH_ASSOC);

    // Check if port number already exists on this switch
    $checkPortSql = "SELECT id FROM inventory_switch_ports WHERE switch_id = :switch_id AND port_number = :port_number";
    $checkPortSth = $pdo->prepare($checkPortSql);
    $checkPortSth->bindValue(':switch_id', $switch_id);
    $checkPortSth->bindValue(':port_number', $port_number);
    $checkPortSth->execute();

    if ($checkPortSth->rowCount() > 0) {
      throw new Exception("Port number '$port_number' already exists on this switch");
    }

    // Insert the new port
    $insertPortSql = "INSERT INTO inventory_switch_ports (
                        switch_id, port_number, port_name, port_type, max_speed,
                        current_speed, status, last_updated
                      ) VALUES (
                        :switch_id, :port_number, :port_name, :port_type, :max_speed,
                        :current_speed, :status, NOW()
                      )";

    $insertPortSth = $pdo->prepare($insertPortSql);
    $insertPortSth->bindValue(':switch_id', $switch_id);
    $insertPortSth->bindValue(':port_number', $port_number);
    $insertPortSth->bindValue(':port_name', $port_name);
    $insertPortSth->bindValue(':port_type', $port_type);
    
    if ($max_speed === null) {
      $insertPortSth->bindValue(':max_speed', null, PDO::PARAM_NULL);
      $insertPortSth->bindValue(':current_speed', null, PDO::PARAM_NULL);
    } else {
      $insertPortSth->bindValue(':max_speed', $max_speed, PDO::PARAM_INT);
      $insertPortSth->bindValue(':current_speed', $max_speed, PDO::PARAM_INT);
    }
    
    $insertPortSth->bindValue(':status', $status);
    $insertPortSth->execute();

    $new_port_id = $pdo->lastInsertId();

    // Return success with the new port data
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'port_id' => $new_port_id,
      'message' => "Port '$port_number' added successfully to switch '{$switchData['label']}'",
      'port_data' => [
        'id' => $new_port_id,
        'switch_id' => $switch_id,
        'port_number' => $port_number,
        'port_name' => $port_name,
        'port_type' => $port_type,
        'max_speed' => $max_speed,
        'current_speed' => $max_speed,
        'status' => $status,
        'connected_device_id' => null,
        'connected_device_type' => null,
        'connected_device_label' => null
      ]
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// New function to add a chassis model
elseif($_GET['f'] == 'add_chassis_model'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['name']) || empty(trim($data['name']))) {
      throw new Exception('Chassis model name is required');
    }

    if (!isset($data['size']) || !is_numeric($data['size'])) {
      throw new Exception('Chassis model size is required and must be numeric');
    }

    if (!isset($data['bay_count']) || !is_numeric($data['bay_count'])) {
      throw new Exception('Bay count is required and must be numeric');
    }

    $name = trim($data['name']);
    $size = intval($data['size']);
    $bay_count = intval($data['bay_count']);

    // Basic sanity checks
    if ($size < 1 || $size > 50) {
      throw new Exception('Chassis model size must be between 1 and 50 U');
    }

    if ($bay_count < 1 || $bay_count > 96) {
      throw new Exception('Bay count must be between 1 and 96');
    }

    // Check duplicate name
    $checkSql = "SELECT COUNT(*) FROM inventory_chassis_model WHERE name = :name";
    $checkStmt = $pdo->prepare($checkSql);
    $checkStmt->bindValue(':name', $name);
    $checkStmt->execute();

    if ($checkStmt->fetchColumn() > 0) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'This chassis model already exists'
      ]);
      return;
    }

    // Insert model
    $insertSql = "INSERT INTO inventory_chassis_model (name, size, bay_count) VALUES (:name, :size, :bay_count)";
    $insertStmt = $pdo->prepare($insertSql);
    $insertStmt->bindValue(':name', $name);
    $insertStmt->bindValue(':size', $size, PDO::PARAM_INT);
    $insertStmt->bindValue(':bay_count', $bay_count, PDO::PARAM_INT);
    $insertStmt->execute();

    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $pdo->lastInsertId(),
      'message' => 'Chassis model added successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_chassis_model: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to add chassis model: ' . $e->getMessage()
    ]);
  }
}

// Get all devices inside a rack
elseif($_GET['f'] == 'get_rack_devices'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['rack_id']) || empty($data['rack_id'])) {
      throw new Exception('Rack ID is required');
    }

    $rack_id = intval($data['rack_id']);

    // Initialize results array
    $devices = [];

    // Get all dedicated servers in this rack
    $dedicatedSql = "SELECT
                      id,
                      label,
                      'dedicated' as type,
                      position,
                      size,
                      status,
                      notes,
                      cpu,
                      ram,
                      (SELECT cpu FROM dedicated_cpu WHERE id = inventory_dedicated_servers.cpu) as cpu_name,
                      (SELECT description FROM ram_configurations WHERE id = inventory_dedicated_servers.ram) as ram_description
                    FROM inventory_dedicated_servers
                    WHERE rack_id = :rack_id";
    $dedicatedSth = $pdo->prepare($dedicatedSql);
    $dedicatedSth->bindValue(':rack_id', $rack_id);
    $dedicatedSth->execute();

    $dedicatedServers = $dedicatedSth->fetchAll(PDO::FETCH_ASSOC);
    $devices = array_merge($devices, $dedicatedServers);

    // Get all switches in this rack
    $switchesSql = "SELECT
                      id,
                      label,
                      'switch' as type,
                      rack_position as position,
                      COALESCE(size_ru, 1) as size,
                      status,
                      notes,
                      (SELECT name FROM inventory_switch_model WHERE id = inventory_switches.model_id) as model
                    FROM inventory_switches
                    WHERE rack_id = :rack_id";
    $switchesSth = $pdo->prepare($switchesSql);
    $switchesSth->bindValue(':rack_id', $rack_id);
    $switchesSth->execute();

    $switches = $switchesSth->fetchAll(PDO::FETCH_ASSOC);
    $devices = array_merge($devices, $switches);

    // Get all chassis in this rack
    $chassisSql = "SELECT
                     c.id,
                     c.label,
                     'chassis' as type,
                     c.position,
                     COALESCE(m.size, 2) as size,
                     c.status,
                     c.notes,
                     m.name as model
                   FROM inventory_chassis c
                   LEFT JOIN inventory_chassis_model m ON c.model_id = m.id
                   WHERE c.rack_id = :rack_id";
    $chassisSth = $pdo->prepare($chassisSql);
    $chassisSth->bindValue(':rack_id', $rack_id);
    $chassisSth->execute();

    $chassis = $chassisSth->fetchAll(PDO::FETCH_ASSOC);
    $devices = array_merge($devices, $chassis);

    // Return the devices
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'devices' => $devices
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Get all devices not assigned to a rack
// Get all devices not assigned to a rack
elseif($_GET['f'] == 'get_unassigned_devices'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Initialize results array
    $devices = [];

    // Get unassigned dedicated servers
    $dedicatedSql = "SELECT
                       id,
                       label,
                       'dedicated' as type,
                       COALESCE(size, 1) as size,
                       status,
                       (SELECT cpu FROM dedicated_cpu WHERE id = inventory_dedicated_servers.cpu) as model
                     FROM inventory_dedicated_servers
                     WHERE (rack_id IS NULL OR rack_id = 0)
                     AND status = 'Available'
                     LIMIT 100";
    $dedicatedSth = $pdo->prepare($dedicatedSql);
    $dedicatedSth->execute();

    $dedicatedServers = $dedicatedSth->fetchAll(PDO::FETCH_ASSOC);
    $devices = array_merge($devices, $dedicatedServers);

    // Get unassigned switches
    $switchesSql = "SELECT
                      id,
                      label,
                      'switch' as type,
                      COALESCE(size_ru, 1) as size,
                      status,
                      (SELECT name FROM inventory_switch_model WHERE id = inventory_switches.model_id) as model
                    FROM inventory_switches
                    WHERE (rack_id IS NULL OR rack_id = 0)
                    AND status = 'Available'
                    LIMIT 100";
    $switchesSth = $pdo->prepare($switchesSql);
    $switchesSth->execute();

    $switches = $switchesSth->fetchAll(PDO::FETCH_ASSOC);
    $devices = array_merge($devices, $switches);

    // Get unassigned chassis - REMOVED status filter to show all unassigned chassis
    $chassisSql = "SELECT
                     c.id,
                     c.label,
                     'chassis' as type,
                     COALESCE(m.size, 2) as size,
                     c.status,
                     m.name as model
                   FROM inventory_chassis c
                   LEFT JOIN inventory_chassis_model m ON c.model_id = m.id
                   WHERE (c.rack_id IS NULL OR c.rack_id = 0)
                   LIMIT 100";
    $chassisSth = $pdo->prepare($chassisSql);
    $chassisSth->execute();

    $chassis = $chassisSth->fetchAll(PDO::FETCH_ASSOC);
    $devices = array_merge($devices, $chassis);

    // Return the devices
    header('Content-Type: application/json');
    echo json_encode($devices);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Update a rack's information
elseif($_GET['f'] == 'update_rack'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('Rack ID is required');
    }

    if (!isset($data['rack_name']) || empty($data['rack_name'])) {
      throw new Exception('Rack name is required');
    }

    $rack_id = intval($data['id']);

    // Prepare update fields
    $updates = [];
    $params = [':id' => $rack_id];

    // Process fields
    if (isset($data['rack_name'])) {
      $updates[] = "rack_name = :rack_name";
      $params[':rack_name'] = $data['rack_name'];
    }

    if (isset($data['size'])) {
      $updates[] = "size = :size";
      $params[':size'] = intval($data['size']) ?: 42; // Default to 42U if invalid
    }

    // Handle field name alignment - frontend uses country_id but DB uses country
    if (isset($data['country_id'])) {
      $updates[] = "country = :country";
      $params[':country'] = intval($data['country_id']);
    }

    // Handle field name alignment - frontend uses city_id but DB uses city
    if (isset($data['city_id'])) {
      $updates[] = "city = :city";
      $params[':city'] = intval($data['city_id']);
    }

    // If notes column exists
    if ($pdo->query("SHOW COLUMNS FROM racks LIKE 'notes'")->rowCount() > 0) {
      if (isset($data['notes'])) {
        $updates[] = "notes = :notes";
        $params[':notes'] = $data['notes'];
      }
    }

    // If there's nothing to update
    if (empty($updates)) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'No changes to update'
      ]);
      return;
    }

    // Build and execute the query
    $sql = "UPDATE racks SET " . implode(", ", $updates) . " WHERE id = :id";
    $sth = $pdo->prepare($sql);

    foreach ($params as $key => $value) {
      $sth->bindValue($key, $value);
    }

    $sth->execute();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Rack updated successfully'
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Assign a device to a rack
elseif($_GET['f'] == 'assign_device_to_rack'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['device_id']) || empty($data['device_id'])) {
      throw new Exception('Device ID is required');
    }

    if (!isset($data['device_type']) || empty($data['device_type'])) {
      throw new Exception('Device type is required');
    }

    if (!isset($data['rack_id']) || empty($data['rack_id'])) {
      throw new Exception('Rack ID is required');
    }

    if (!isset($data['position'])) {
      throw new Exception('Position is required');
    }

    $device_id = intval($data['device_id']);
    $device_type = $data['device_type'];
    $rack_id = intval($data['rack_id']);
    $position = intval($data['position']);

    // Determine which table to update based on device type
    $table = '';
    $positionColumn = '';

    switch ($device_type) {
      case 'dedicated':
        $table = 'inventory_dedicated_servers';
        $positionColumn = 'position';
        break;
      case 'switch':
        $table = 'inventory_switches';
        $positionColumn = 'rack_position';
        break;
      case 'chassis':
        $table = 'inventory_chassis';
        $positionColumn = 'position';
        break;
      default:
        throw new Exception('Unsupported device type');
    }

    // Update the device's rack assignment and position
    $sql = "UPDATE `$table` SET rack_id = :rack_id, `$positionColumn` = :position WHERE id = :id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':rack_id', $rack_id);
    $sth->bindValue(':position', $position);
    $sth->bindValue(':id', $device_id);
    $sth->execute();

    // Check if update was successful
    if ($sth->rowCount() === 0) {
      throw new Exception("Failed to update device. Device may not exist.");
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Device assigned to rack successfully'
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Remove a device from a rack
elseif($_GET['f'] == 'remove_device_from_rack'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['device_id']) || empty($data['device_id'])) {
      throw new Exception('Device ID is required');
    }

    if (!isset($data['device_type']) || empty($data['device_type'])) {
      throw new Exception('Device type is required');
    }

    $device_id = intval($data['device_id']);
    $device_type = $data['device_type'];

    // Determine which table to update based on device type
    $table = '';
    $positionColumn = '';

    switch ($device_type) {
      case 'dedicated':
        $table = 'inventory_dedicated_servers';
        $positionColumn = 'position';
        break;
      case 'switch':
        $table = 'inventory_switches';
        $positionColumn = 'rack_position';
        break;
      case 'chassis':
        $table = 'inventory_chassis';
        $positionColumn = 'position';
        break;
      default:
        throw new Exception('Unsupported device type');
    }

    // Update the device to remove rack assignment
    $sql = "UPDATE `$table` SET rack_id = NULL, `$positionColumn` = NULL WHERE id = :id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':id', $device_id);
    $sth->execute();

    // Check if update was successful
    if ($sth->rowCount() === 0) {
      throw new Exception("Failed to update device. Device may not exist.");
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Device removed from rack successfully'
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Update a device's position in a rack
elseif($_GET['f'] == 'update_device_position'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['device_id']) || empty($data['device_id'])) {
      throw new Exception('Device ID is required');
    }

    if (!isset($data['device_type']) || empty($data['device_type'])) {
      throw new Exception('Device type is required');
    }

    if (!isset($data['position'])) {
      throw new Exception('Position is required');
    }

    $device_id = intval($data['device_id']);
    $device_type = $data['device_type'];
    $position = intval($data['position']);

    // Determine which table to update based on device type
    $table = '';
    $positionColumn = '';

    switch ($device_type) {
      case 'dedicated':
        $table = 'inventory_dedicated_servers';
        $positionColumn = 'position';
        break;
      case 'switch':
        $table = 'inventory_switches';
        $positionColumn = 'rack_position';
        break;
      case 'chassis':
        $table = 'inventory_chassis';
        $positionColumn = 'position';
        break;
      default:
        throw new Exception('Unsupported device type');
    }

    // Update the device's position
    $sql = "UPDATE `$table` SET `$positionColumn` = :position WHERE id = :id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':position', $position);
    $sth->bindValue(':id', $device_id);
    $sth->execute();

    // Check if update was successful
    if ($sth->rowCount() === 0) {
      throw new Exception("Failed to update device position. Device may not exist.");
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Device position updated successfully'
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}


elseif($_GET['f'] == 'add_country'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['country']) || empty($data['country'])) {
      throw new Exception('Country name is required');
    }

    // Insert the country
    $sql = "INSERT INTO countries (country) VALUES (:country)";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':country', $data['country']);
    $sth->execute();

    // Get the new ID
    $country_id = $pdo->lastInsertId();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $country_id,
      'message' => 'Country added successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_country: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Add a new city
elseif($_GET['f'] == 'add_city'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['city']) || empty($data['city'])) {
      throw new Exception('City name is required');
    }

    if (!isset($data['country_id']) || empty($data['country_id'])) {
      throw new Exception('Country ID is required');
    }

    // Optional fields with defaults
    $datacenter = isset($data['datacenter']) ? $data['datacenter'] : '';
    $address = isset($data['address']) ? $data['address'] : '';
    $cod_postal = isset($data['cod_postal']) ? $data['cod_postal'] : '';

    // Insert the city
    $sql = "INSERT INTO cities (city, country_id, datacenter, address, cod_postal)
            VALUES (:city, :country_id, :datacenter, :address, :cod_postal)";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':city', $data['city']);
    $sth->bindValue(':country_id', $data['country_id']);
    $sth->bindValue(':datacenter', $datacenter);
    $sth->bindValue(':address', $address);
    $sth->bindValue(':cod_postal', $cod_postal);
    $sth->execute();

    // Get the new ID
    $city_id = $pdo->lastInsertId();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $city_id,
      'message' => 'City added successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_city: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'update_city_datacenter'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['city_id']) || empty($data['city_id'])) {
      throw new Exception('City ID is required');
    }

    if (!isset($data['datacenter'])) {
      throw new Exception('Datacenter name is required');
    }

    // Update the city's datacenter field
    $sql = "UPDATE cities SET datacenter = :datacenter WHERE id = :city_id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':datacenter', $data['datacenter']);
    $sth->bindValue(':city_id', $data['city_id']);
    $sth->execute();

    // Check if any rows were affected
    if ($sth->rowCount() === 0) {
      throw new Exception('City not found');
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Datacenter updated successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in update_city_datacenter: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Add a new rack
elseif($_GET['f'] == 'add_rack'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['rack_name']) || empty($data['rack_name'])) {
      throw new Exception('Rack name is required');
    }

    if (!isset($data['country']) || empty($data['country'])) {
      throw new Exception('Country is required');
    }

    if (!isset($data['city']) || empty($data['city'])) {
      throw new Exception('City is required');
    }

    // Default size if not provided
    $size = isset($data['size']) && !empty($data['size']) ? intval($data['size']) : 42;

    // Check if the table has a notes column
    $hasNotesColumn = $pdo->query("SHOW COLUMNS FROM racks LIKE 'notes'")->rowCount() > 0;

    // Build the SQL query based on whether notes column exists
    if ($hasNotesColumn) {
      $sql = "INSERT INTO racks (rack_name, size, country, city, notes)
              VALUES (:rack_name, :size, :country, :city, :notes)";
    } else {
      $sql = "INSERT INTO racks (rack_name, size, country, city)
              VALUES (:rack_name, :size, :country, :city)";
    }

    $sth = $pdo->prepare($sql);
    $sth->bindValue(':rack_name', $data['rack_name']);
    $sth->bindValue(':size', $size);
    $sth->bindValue(':country', $data['country']);
    $sth->bindValue(':city', $data['city']);

    // Bind notes parameter if the column exists
    if ($hasNotesColumn) {
      $sth->bindValue(':notes', isset($data['notes']) ? $data['notes'] : '');
    }

    $sth->execute();

    // Get the new ID
    $rack_id = $pdo->lastInsertId();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $rack_id,
      'message' => 'Rack added successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_rack: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Delete a country - with validation to prevent deleting countries with cities
elseif($_GET['f'] == 'delete_country'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('Country ID is required');
    }

    $country_id = intval($data['id']);

    // Start transaction
    $pdo->beginTransaction();

    // Check if this country has any cities
    $checkCitiesSql = "SELECT COUNT(*) FROM cities WHERE country_id = :country_id";
    $checkCitiesSth = $pdo->prepare($checkCitiesSql);
    $checkCitiesSth->bindValue(':country_id', $country_id);
    $checkCitiesSth->execute();

    $citiesCount = $checkCitiesSth->fetchColumn();

    if ($citiesCount > 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Cannot delete country - it has {$citiesCount} cities. Delete all cities first.");
    }

    // Delete the country
    $deleteSql = "DELETE FROM countries WHERE id = :id";
    $deleteSth = $pdo->prepare($deleteSql);
    $deleteSth->bindValue(':id', $country_id);
    $deleteSth->execute();

    // Check if deletion was successful
    if ($deleteSth->rowCount() === 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Country with ID {$country_id} not found.");
    }

    // Commit transaction
    $pdo->commit();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Country deleted successfully'
    ]);

  } catch (Exception $e) {
    // Rollback if transaction is active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    // Return error
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Delete a city - with validation to prevent deleting cities with racks
elseif($_GET['f'] == 'delete_city'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('City ID is required');
    }

    $city_id = intval($data['id']);

    // Start transaction
    $pdo->beginTransaction();

    // Check if this city has any racks
    $checkRacksSql = "SELECT COUNT(*) FROM racks WHERE city = :city_id";
    $checkRacksSth = $pdo->prepare($checkRacksSql);
    $checkRacksSth->bindValue(':city_id', $city_id);
    $checkRacksSth->execute();

    $racksCount = $checkRacksSth->fetchColumn();

    if ($racksCount > 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Cannot delete city - it has {$racksCount} racks. Delete all racks first.");
    }

    // Delete the city
    $deleteSql = "DELETE FROM cities WHERE id = :id";
    $deleteSth = $pdo->prepare($deleteSql);
    $deleteSth->bindValue(':id', $city_id);
    $deleteSth->execute();

    // Check if deletion was successful
    if ($deleteSth->rowCount() === 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("City with ID {$city_id} not found.");
    }

    // Commit transaction
    $pdo->commit();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'City deleted successfully'
    ]);

  } catch (Exception $e) {
    // Rollback if transaction is active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    // Return error
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Delete a rack - with validation to prevent deleting racks with devices
elseif($_GET['f'] == 'delete_rack'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('Rack ID is required');
    }

    $rack_id = intval($data['id']);

    // Start transaction
    $pdo->beginTransaction();

    // Check if this rack has any dedicated servers
    $checkDedicatedSql = "SELECT COUNT(*) FROM inventory_dedicated_servers WHERE rack_id = :rack_id";
    $checkDedicatedSth = $pdo->prepare($checkDedicatedSql);
    $checkDedicatedSth->bindValue(':rack_id', $rack_id);
    $checkDedicatedSth->execute();

    $dedicatedCount = $checkDedicatedSth->fetchColumn();

    if ($dedicatedCount > 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Cannot delete rack - it has {$dedicatedCount} dedicated servers. Remove all servers first.");
    }

    // Check if this rack has any chassis
    $checkChassisSql = "SELECT COUNT(*) FROM inventory_chassis WHERE rack_id = :rack_id";
    $checkChassisSth = $pdo->prepare($checkChassisSql);
    $checkChassisSth->bindValue(':rack_id', $rack_id);
    $checkChassisSth->execute();

    $chassisCount = $checkChassisSth->fetchColumn();

    if ($chassisCount > 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Cannot delete rack - it has {$chassisCount} chassis. Remove all chassis first.");
    }

    // Check if this rack has any switches
    $checkSwitchesSql = "SELECT COUNT(*) FROM inventory_switches WHERE rack_id = :rack_id";
    $checkSwitchesSth = $pdo->prepare($checkSwitchesSql);
    $checkSwitchesSth->bindValue(':rack_id', $rack_id);
    $checkSwitchesSth->execute();

    $switchesCount = $checkSwitchesSth->fetchColumn();

    if ($switchesCount > 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Cannot delete rack - it has {$switchesCount} switches. Remove all switches first.");
    }

    // Delete the rack
    $deleteSql = "DELETE FROM racks WHERE id = :id";
    $deleteSth = $pdo->prepare($deleteSql);
    $deleteSth->bindValue(':id', $rack_id);
    $deleteSth->execute();

    // Check if deletion was successful
    if ($deleteSth->rowCount() === 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Rack with ID {$rack_id} not found.");
    }

    // Commit transaction
    $pdo->commit();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Rack deleted successfully'
    ]);

  } catch (Exception $e) {
    // Rollback if transaction is active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    // Return error
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Update a country
elseif($_GET['f'] == 'update_country'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('Country ID is required');
    }

    if (!isset($data['country']) || empty($data['country'])) {
      throw new Exception('Country name is required');
    }

    $country_id = intval($data['id']);
    $country_name = trim($data['country']);

    // Check for duplicate country name
    $checkDuplicateSql = "SELECT COUNT(*) FROM countries WHERE country = :country AND id != :id";
    $checkDuplicateSth = $pdo->prepare($checkDuplicateSql);
    $checkDuplicateSth->bindValue(':country', $country_name);
    $checkDuplicateSth->bindValue(':id', $country_id);
    $checkDuplicateSth->execute();

    if ($checkDuplicateSth->fetchColumn() > 0) {
      throw new Exception("A country with this name already exists");
    }

    // Update the country
    $updateSql = "UPDATE countries SET country = :country WHERE id = :id";
    $updateSth = $pdo->prepare($updateSql);
    $updateSth->bindValue(':country', $country_name);
    $updateSth->bindValue(':id', $country_id);
    $updateSth->execute();

    // Check if update was successful
    if ($updateSth->rowCount() === 0) {
      throw new Exception("Country with ID {$country_id} not found or no changes were made");
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Country updated successfully'
    ]);

  } catch (Exception $e) {
    // Return error
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Update a city
elseif($_GET['f'] == 'update_city'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('City ID is required');
    }

    $city_id = intval($data['id']);

    // Build update fields
    $updateFields = [];
    $params = [':id' => $city_id];

    // Check for city name update
    if (isset($data['city'])) {
      $updateFields[] = "city = :city";
      $params[':city'] = trim($data['city']);

      // Validate city name
      if (empty($params[':city'])) {
        throw new Exception('City name cannot be empty');
      }
    }

    // Check for datacenter update
    if (isset($data['datacenter'])) {
      $updateFields[] = "datacenter = :datacenter";
      $params[':datacenter'] = trim($data['datacenter']);
    }

    // Check for country update
    if (isset($data['country_id'])) {
      $updateFields[] = "country_id = :country_id";
      $params[':country_id'] = intval($data['country_id']);

      // Validate country exists
      $checkCountrySql = "SELECT COUNT(*) FROM countries WHERE id = :country_id";
      $checkCountrySth = $pdo->prepare($checkCountrySql);
      $checkCountrySth->bindValue(':country_id', $params[':country_id']);
      $checkCountrySth->execute();

      if ($checkCountrySth->fetchColumn() === 0) {
        throw new Exception("Country with ID {$params[':country_id']} not found");
      }
    }

    // If no update fields, no need to proceed
    if (empty($updateFields)) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'No changes were made'
      ]);
      return;
    }

    // Build and execute the update query
    $updateSql = "UPDATE cities SET " . implode(", ", $updateFields) . " WHERE id = :id";
    $updateSth = $pdo->prepare($updateSql);

    foreach ($params as $key => $value) {
      $updateSth->bindValue($key, $value);
    }

    $updateSth->execute();

    // Check if update was successful
    if ($updateSth->rowCount() === 0) {
      throw new Exception("City with ID {$city_id} not found or no changes were made");
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'City updated successfully'
    ]);

  } catch (Exception $e) {
    // Return error
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Test endpoint for debugging
elseif($_GET['f'] == 'test_server_endpoint'){
  try {
    // Return a simple success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Test endpoint is working correctly',
      'time' => date('Y-m-d H:i:s')
    ]);
  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Get server by ID - checks both dedicated and blade servers based on ID
elseif($_GET['f'] == 'get_server_by_id'){
  try {
    error_log("Starting get_server_by_id function");

    // Get data from request
    $data = json_decode(file_get_contents('php://input'), true);
    error_log("Request data: " . json_encode($data));

    // TEMPORARY: Skip authentication for debugging
    // We'll add proper authentication back once we confirm the basic functionality works
    $admin_id = 1; // Default admin ID

    // Validate required fields
    if (!isset($data['server_id'])) {
      throw new Exception('Server ID is required');
    }

    $server_id = intval($data['server_id']);
    error_log("Looking up server with ID: $server_id (original value: {$data['server_id']}, type: " . gettype($data['server_id']) . ")");

    // No mock data option - using real database implementation only

    // Real database query implementation
    // Determine server type based on ID
    // IMPORTANT: For server ID 10004, we know it's a dedicated server despite being under 100000
    // Use == for loose comparison since $server_id might be a string
    if ($server_id == 10004) {
      $serverType = 'dedicated';
      error_log("Special case: Server ID 10004 is a dedicated server");
    } else {
      // If ID is over 100000, it's a dedicated server, otherwise it's a blade server
      $serverType = ($server_id > 100000) ? 'dedicated' : 'blade';
    }
    $serverTable = ($serverType === 'dedicated') ? 'inventory_dedicated_servers' : 'blade_server_inventory';
    error_log("Determined server type: $serverType based on ID range. Server ID: $server_id");

    $server = null;

    try {
      // Check if PDO connection is valid
      if (!isset($pdo) || !($pdo instanceof PDO)) {
        throw new Exception("Database connection is not available");
      }

      // First, try to get a list of tables to verify database connection
      try {
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        error_log("Database tables: " . implode(", ", $tables));
      } catch (Exception $e) {
        error_log("Error listing tables: " . $e->getMessage());
        // Continue anyway, this is just a diagnostic step
      }

      // For server ID 10004, try both tables regardless of the determined server type
      if ($server_id == 10004) {
        error_log("Special case: Trying both tables for server ID 10004");

        // First try dedicated servers table
        try {
          $tableExists = $pdo->query("SHOW TABLES LIKE 'inventory_dedicated_servers'")->rowCount() > 0;
          if ($tableExists) {
            $sql = "SELECT
                      ids.*,
                      dc.cpu AS cpu_name,
                      c.city AS city_name,
                      co.country AS country_name,
                      sw.label AS switch_name
                    FROM inventory_dedicated_servers ids
                    LEFT JOIN dedicated_cpu dc ON ids.cpu = dc.id
                    LEFT JOIN cities c ON ids.city_id = c.id
                    LEFT JOIN countries co ON ids.country_id = co.id
                    LEFT JOIN inventory_switches sw ON ids.switch_id = sw.id
                    WHERE ids.id = :id";
            $sth = $pdo->prepare($sql);
            $sth->bindValue(':id', $server_id);
            $sth->execute();

            if ($sth->rowCount() > 0) {
              $server = $sth->fetch(PDO::FETCH_ASSOC);
              $serverType = 'dedicated';
              error_log("Found server ID 10004 in dedicated servers table");
            } else {
              error_log("Server ID 10004 not found in dedicated servers table");
            }
          }
        } catch (Exception $e) {
          error_log("Error checking dedicated servers table for ID 10004: " . $e->getMessage());
        }

        // If not found in dedicated servers, try blade servers
        if ($server === null) {
          try {
            $tableExists = $pdo->query("SHOW TABLES LIKE 'blade_server_inventory'")->rowCount() > 0;
            if ($tableExists) {
              $sql = "SELECT
                        bsi.*,
                        dc.cpu AS cpu_name,
                        sw.label AS switch_name,
                        c.city AS city_name,
                        co.country AS country_name,
                        ch.label AS chassis_name
                      FROM blade_server_inventory bsi
                      LEFT JOIN dedicated_cpu dc ON bsi.cpu = dc.id
                      LEFT JOIN inventory_switches sw ON bsi.switch_id = sw.id
                      LEFT JOIN inventory_chassis ch ON bsi.chassis_id = ch.id
                      LEFT JOIN cities c ON ch.city_id = c.id
                      LEFT JOIN countries co ON ch.country_id = co.id
                      WHERE bsi.id = :id";
              $sth = $pdo->prepare($sql);
              $sth->bindValue(':id', $server_id);
              $sth->execute();

              if ($sth->rowCount() > 0) {
                $server = $sth->fetch(PDO::FETCH_ASSOC);
                $serverType = 'blade';
                error_log("Found server ID 10004 in blade servers table");
              } else {
                error_log("Server ID 10004 not found in blade servers table");
              }
            }
          } catch (Exception $e) {
            error_log("Error checking blade servers table for ID 10004: " . $e->getMessage());
          }
        }

        // If server is still null, return a clear error message
        if ($server === null) {
          header('Content-Type: application/json');
          http_response_code(404); // Use 404 Not Found status code
          echo json_encode([
            'success' => false,
            'error' => "Server not found",
            'message' => "The server with ID $server_id could not be found in our database."
          ]);
          return;
        }
      } else {
        // For all other server IDs, use the normal logic
        if ($serverType === 'dedicated') {
          // Query dedicated server
          error_log("Querying dedicated server table for ID: $server_id");

          // Check if the table exists
          try {
            $tableExists = $pdo->query("SHOW TABLES LIKE 'inventory_dedicated_servers'")->rowCount() > 0;
            if (!$tableExists) {
              error_log("Table inventory_dedicated_servers does not exist");
              // Fall back to blade servers
              $serverType = 'blade';
            } else {
              $sql = "SELECT
                        ids.*,
                        dc.cpu AS cpu_name,
                        c.city AS city_name,
                        co.country AS country_name,
                        sw.label AS switch_name
                      FROM inventory_dedicated_servers ids
                      LEFT JOIN dedicated_cpu dc ON ids.cpu = dc.id
                      LEFT JOIN cities c ON ids.city_id = c.id
                      LEFT JOIN countries co ON ids.country_id = co.id
                      LEFT JOIN inventory_switches sw ON ids.switch_id = sw.id
                      WHERE ids.id = :id";
              $sth = $pdo->prepare($sql);
              $sth->bindValue(':id', $server_id);
              $sth->execute();

              if ($sth->rowCount() === 0) {
                // If not found in dedicated servers, try blade servers as fallback
                error_log("Server not found in dedicated servers, trying blade servers as fallback");
                $serverType = 'blade';
              } else {
                $server = $sth->fetch(PDO::FETCH_ASSOC);
                error_log("Found server in dedicated servers table");
              }
            }
          } catch (Exception $e) {
            error_log("Error checking dedicated servers table: " . $e->getMessage());
            // Fall back to blade servers
            $serverType = 'blade';
          }
        }

        if ($serverType === 'blade' || $server === null) {
          // Query blade server
          error_log("Querying blade server table for ID: $server_id");

          // Check if the table exists
          try {
            $tableExists = $pdo->query("SHOW TABLES LIKE 'blade_server_inventory'")->rowCount() > 0;
            if (!$tableExists) {
              error_log("Table blade_server_inventory does not exist");
              throw new Exception("Required database tables do not exist");
            } else {
              $sql = "SELECT
                        bsi.*,
                        dc.cpu AS cpu_name,
                        sw.label AS switch_name,
                        c.city AS city_name,
                        co.country AS country_name,
                        ch.label AS chassis_name
                      FROM blade_server_inventory bsi
                      LEFT JOIN dedicated_cpu dc ON bsi.cpu = dc.id
                      LEFT JOIN inventory_switches sw ON bsi.switch_id = sw.id
                      LEFT JOIN inventory_chassis ch ON bsi.chassis_id = ch.id
                      LEFT JOIN cities c ON ch.city_id = c.id
                      LEFT JOIN countries co ON ch.country_id = co.id
                      WHERE bsi.id = :id";
              $sth = $pdo->prepare($sql);
              $sth->bindValue(':id', $server_id);
              $sth->execute();

              if ($sth->rowCount() === 0) {
                // If we tried both tables and found nothing, return a clear error message
                header('Content-Type: application/json');
                http_response_code(404); // Use 404 Not Found status code
                echo json_encode([
                  'success' => false,
                  'error' => "Server not found",
                  'message' => "The server with ID $server_id could not be found in our database."
                ]);
                return;
              }

              $server = $sth->fetch(PDO::FETCH_ASSOC);
              $serverType = 'blade'; // Confirm it's a blade server
              error_log("Found server in blade servers table");
            }
          } catch (Exception $e) {
            error_log("Error checking blade servers table: " . $e->getMessage());
            throw new Exception("Error querying blade servers: " . $e->getMessage());
          }
        }
      }
    } catch (Exception $dbError) {
      error_log("Database error: " . $dbError->getMessage());

      // Return error without fallback
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'success' => false,
        'error' => $dbError->getMessage()
      ]);
      return;
    }

    // Add server type to the response
    $server['server_type'] = $serverType;
    error_log("Server data prepared with server_type: $serverType");

    // Return server details
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'server' => $server,
      'server_type' => $serverType
    ]);
    error_log("Successfully returned server data");

  } catch (Exception $e) {
    error_log("Error in get_server_by_id: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'pxe_reinstall'){
  try {
    error_log("=== PXE REINSTALL START ===", 3, "auto.logs");
    
    // Read input once and store it
    $raw_input = file_get_contents('php://input');
    error_log("Raw input received, length: " . strlen($raw_input), 3, "auto.logs");
    
    // Authenticate admin
    $admin_id = auth_admin();
    error_log("Authentication successful, admin_id: " . $admin_id, 3, "auto.logs");

    // Get data from request
    $data = json_decode($raw_input, true);
    error_log("Decoded data: " . json_encode($data), 3, "auto.logs");

    // Validate required fields
    if (empty($data['server_id']) || empty($data['os_id']) || empty($data['ipmi_address'])) {
      throw new Exception('Server ID, OS ID, and IPMI address are required');
    }

    // Extract parameters
    $server_id = $data['server_id'];
    $server_type = $data['server_type'] ?? 'dedicated';
    $os_id = $data['os_id'];
    $ipmi_address = $data['ipmi_address'];
    $ipmi_username = $data['ipmi_username'] ?? 'root';
    $ipmi_password = $data['ipmi_password'] ?? '';
    $custom_root_password = $data['custom_root_password'] ?? null;
    $network_config = $data['network_config'] ?? [];
    $mac_address = $data['mac_address'] ?? '';

    error_log("PXE reinstall requested for server $server_id with OS $os_id", 3, "auto.logs");
    error_log("Frontend sent OS data: " . json_encode(['os_id' => $os_id, 'os_template' => $data['os_template'] ?? 'not provided']), 3, "auto.logs");

    // Validate IPMI credentials
    if (empty($ipmi_password)) {
      throw new Exception('IPMI password is required for PXE reinstall');
    }

    // Get server information
    $server_table = $server_type === 'dedicated' ? 'inventory_dedicated_servers' : 'blade_server_inventory';
    $server_sth = $pdo->prepare("SELECT * FROM $server_table WHERE id = :server_id");
    $server_sth->bindValue(':server_id', $server_id);
    $server_sth->execute();
    
    if ($server_sth->rowCount() === 0) {
      throw new Exception("Server with ID $server_id not found");
    }
    
    $server_data = $server_sth->fetch(PDO::FETCH_ASSOC);

    // Get OS information from reinstallable_os table only
    $os_sth = $pdo->prepare("SELECT * FROM reinstallable_os WHERE id = :os_id");
    $os_sth->bindValue(':os_id', $os_id);
    $os_sth->execute();
    
    if ($os_sth->rowCount() === 0) {
      throw new Exception("Operating system with ID $os_id not found in reinstallable_os table. Available OS IDs: 1=Ubuntu Server 22.04");
    }
    
    $os_info = $os_sth->fetch(PDO::FETCH_ASSOC);
    $os_template = $os_info['template_file'] ?? 'ubuntu-22.04';
    
    // Normalize template file to match our OS directory structure
    $os_template = str_replace('-server', '', $os_template); // ubuntu-22.04-server -> ubuntu-22.04

    error_log("OS template for reinstall: $os_template", 3, "auto.logs");

    // Setup PXE configuration files using database network configuration
    if (!empty($mac_address)) {
      error_log("Setting up PXE configuration files with database network config", 3, "auto.logs");
      error_log("MAC address: " . $mac_address, 3, "auto.logs");
      
      require_once 'pxe_api_integration.php';
      error_log("PXE integration file included successfully", 3, "auto.logs");
      
      $pxe_manager = new PXENetworkManager($pdo);
      error_log("PXENetworkManager instance created", 3, "auto.logs");
      
      try {
        // Use server's main_ip to get network configuration from database
        $server_main_ip = $server_data['main_ip'] ?? null;
        if (empty($server_main_ip)) {
          throw new Exception("Server main IP is required for PXE configuration");
        }
        
        // Get network configuration from database instead of using frontend values
        $reflection = new ReflectionClass($pxe_manager);
        $method = $reflection->getMethod('getNetworkConfigFromDatabase');
        $method->setAccessible(true);
        $db_network_config = $method->invoke($pxe_manager, $server_main_ip);
        $db_network_config['hostname'] = $server_data['label'] ?? "server-$server_id";
        
        error_log("Database network config: " . json_encode($db_network_config), 3, "auto.logs");
        
        $server_data_for_pxe = [
          'mac' => $mac_address,
          'id' => $server_id,
          'label' => $server_data['label'] ?? "server-$server_id"
        ];
        
        $pxe_result = $pxe_manager->setupCompleteReinstall(
          $server_id,
          $server_data_for_pxe,
          $db_network_config,
          $os_template,
          $custom_root_password
        );
        
        error_log("PXE configuration files created successfully: " . json_encode($pxe_result['details']), 3, "auto.logs");
        
      } catch (Exception $pxe_error) {
        error_log("PXE configuration failed: " . $pxe_error->getMessage(), 3, "auto.logs");
        // Continue with IPMI commands even if PXE setup fails
      }
    }

    // Create temporary password file for SSH
    $temp_password_file = tempnam(sys_get_temp_dir(), 'pxe_' . uniqid());
    if (!$temp_password_file) {
      throw new Exception('Failed to create temporary password file');
    }

    try {
      // Write password to file
      if (file_put_contents($temp_password_file, $ipmi_password) === false) {
        throw new Exception('Failed to write to temporary password file');
      }
      chmod($temp_password_file, 0600);

      // Prepare SSH command parameters
      $ipmi_address_clean = escapeshellarg($ipmi_address);
      $ipmi_username_clean = escapeshellarg($ipmi_username);
      $password_file_clean = escapeshellarg($temp_password_file);
      $os_template_clean = escapeshellarg($os_template);

      // Step 1: Set boot device to PXE (network boot) using ipmitool
      error_log("Setting boot device to PXE for server $server_id", 3, "auto.logs");
      
      // Use ipmitool instead of SSH for more reliable IPMI operations
      $pxe_boot_cmd = "timeout 30s ipmitool -I lanplus -H {$ipmi_address_clean} " .
                     "-U {$ipmi_username_clean} -f {$password_file_clean} " .
                     "chassis bootdev pxe options=persistent 2>&1";

      $pxe_output = [];
      $pxe_return = 0;
      exec($pxe_boot_cmd, $pxe_output, $pxe_return);

      if ($pxe_return !== 0) {
        error_log("PXE boot configuration failed with ipmitool: " . implode(" ", $pxe_output), 3, "auto.logs");
        
        // Try alternative ipmitool command
        $alt_pxe_cmd = "timeout 30s ipmitool -I lanplus -H {$ipmi_address_clean} " .
                      "-U {$ipmi_username_clean} -f {$password_file_clean} " .
                      "chassis bootparam set bootflag force_pxe 2>&1";
        
        $alt_output = [];
        $alt_return = 0;
        exec($alt_pxe_cmd, $alt_output, $alt_return);
        
        if ($alt_return !== 0) {
          // If ipmitool fails, try SSH as fallback
          error_log("ipmitool failed, trying SSH fallback", 3, "auto.logs");
          
          $ssh_pxe_cmd = "timeout 30s sshpass -f {$password_file_clean} " .
                        "ssh -o StrictHostKeyChecking=no -o ConnectTimeout=15 " .
                        "{$ipmi_username_clean}@{$ipmi_address_clean} " .
                        "'racadm set iDRAC.ServerBoot.FirstBootDevice PXE' 2>&1";
          
          $ssh_output = [];
          $ssh_return = 0;
          exec($ssh_pxe_cmd, $ssh_output, $ssh_return);
          
          if ($ssh_return !== 0) {
            throw new Exception("Failed to configure PXE boot: " . implode(" ", array_merge($pxe_output, $alt_output, $ssh_output)));
          }
          
          error_log("PXE boot configured successfully using SSH fallback", 3, "auto.logs");
        } else {
          error_log("PXE boot configured successfully using alternative ipmitool command", 3, "auto.logs");
        }
      } else {
        error_log("PXE boot configured successfully", 3, "auto.logs");
      }

      // Step 2: Power cycle the server to start PXE boot using ipmitool
      error_log("Power cycling server $server_id to start PXE boot", 3, "auto.logs");
      
      $power_cycle_cmd = "timeout 45s ipmitool -I lanplus -H {$ipmi_address_clean} " .
                        "-U {$ipmi_username_clean} -f {$password_file_clean} " .
                        "power cycle 2>&1";

      $power_output = [];
      $power_return = 0;
      exec($power_cycle_cmd, $power_output, $power_return);

      if ($power_return !== 0) {
        error_log("Power cycle failed with ipmitool: " . implode(" ", $power_output), 3, "auto.logs");
        
        // Try SSH as fallback
        error_log("ipmitool power cycle failed, trying SSH fallback", 3, "auto.logs");
        
        $ssh_power_cmd = "timeout 45s sshpass -f {$password_file_clean} " .
                        "ssh -o StrictHostKeyChecking=no -o ConnectTimeout=15 " .
                        "{$ipmi_username_clean}@{$ipmi_address_clean} " .
                        "'racadm serveraction powercycle' 2>&1";

        $ssh_power_output = [];
        $ssh_power_return = 0;
        exec($ssh_power_cmd, $ssh_power_output, $ssh_power_return);
        
        if ($ssh_power_return !== 0) {
          throw new Exception("Failed to power cycle server: " . implode(" ", array_merge($power_output, $ssh_power_output)));
        }
        
        error_log("Server power cycle initiated successfully using SSH fallback", 3, "auto.logs");
      } else {
        error_log("Server power cycle initiated successfully", 3, "auto.logs");
      }

      // Log the reinstall action in database
      try {
        $log_sql = "INSERT INTO server_reinstall_log (server_id, server_type, os_id, os_template, 
                    initiated_by, initiated_at, ipmi_address, custom_password_used)
                    VALUES (:server_id, :server_type, :os_id, :os_template, :admin_id, NOW(), 
                    :ipmi_address, :custom_password_used)";
        $log_sth = $pdo->prepare($log_sql);
        $log_sth->bindValue(':server_id', $server_id);
        $log_sth->bindValue(':server_type', $server_type);
        $log_sth->bindValue(':os_id', $os_id);
        $log_sth->bindValue(':os_template', $os_template);
        $log_sth->bindValue(':admin_id', $admin_id);
        $log_sth->bindValue(':ipmi_address', $ipmi_address);
        $log_sth->bindValue(':custom_password_used', $custom_root_password ? 1 : 0);
        $log_sth->execute();
        
        error_log("Reinstall action logged to database", 3, "auto.logs");
      } catch (Exception $log_error) {
        error_log("Failed to log reinstall action: " . $log_error->getMessage(), 3, "auto.logs");
        // Don't fail the operation if logging fails
      }

      // Configure switch interface with IP helper-address for PXE reinstall
      try {
        error_log("Configuring switch interface for PXE reinstall on server $server_id", 3, "auto.logs");

        // Include the subnet configuration functions
        require_once 'api_admin_subnets.php';

        // Get server information including switch details
        $server_table = $server_type === 'dedicated' ? 'inventory_dedicated_servers' : 'blade_server_inventory';
        $server_info_stmt = $pdo->prepare("SELECT * FROM $server_table WHERE id = :server_id");
        $server_info_stmt->bindValue(':server_id', $server_id);
        $server_info_stmt->execute();
        $server_info = $server_info_stmt->fetch(PDO::FETCH_ASSOC);

        if ($server_info && !empty($server_info['switch_ip']) && !empty($server_info['port1'])) {
          // Get all subnets assigned to this server (like in subnet assignment)
          $all_subnets = [];

          // Add main IP if exists
          if (!empty($server_info['main_ip'])) {
            $all_subnets[] = $server_info['main_ip'];
          }

          // Add additional IPs if exist
          if (!empty($server_info['additional_ips'])) {
            $additional_ips = array_filter(array_map('trim', explode(',', $server_info['additional_ips'])));
            $all_subnets = array_merge($all_subnets, $additional_ips);
          }

          if (!empty($all_subnets)) {
            // Calculate gateway IP from first subnet (like in subnet assignment)
            $primary_subnet = $all_subnets[0];
            $gateway_ip = $primary_subnet;
            if (strpos($primary_subnet, '/') !== false) {
              list($net_ip, $prefix) = explode('/', $primary_subnet);
              $gateway_ip = long2ip(ip2long($net_ip) + 1);
            }

            error_log("PXE Reinstall: Configuring switch {$server_info['switch_ip']} port {$server_info['port1']} with subnets: " . implode(', ', $all_subnets), 3, "auto.logs");

            // Queue switch configuration operation (same as subnet assignment)
            $operation_id = queueSwitchOperation('configure', $server_info['switch_ip'], $server_info['port1'], [
              'password' => $server_info['root_password'],
              'ip_address' => $gateway_ip,
              'subnet_info' => $all_subnets,
              'server_label' => $server_info['label'] ?? null
            ], $pdo);

            if ($operation_id) {
              error_log("PXE Reinstall: Switch configuration queued (Operation ID: $operation_id)", 3, "auto.logs");

              // Process the queue immediately (like in subnet assignment)
              processSwitchQueue($pdo);

              error_log("Switch interface configured successfully for PXE reinstall", 3, "auto.logs");
            } else {
              error_log("Failed to queue switch configuration for PXE reinstall", 3, "auto.logs");
            }
          } else {
            error_log("No subnets found for server $server_id, skipping switch configuration", 3, "auto.logs");
          }
        } else {
          error_log("Server $server_id missing switch info (switch_ip: " . ($server_info['switch_ip'] ?? 'null') . ", port1: " . ($server_info['port1'] ?? 'null') . "), skipping switch configuration", 3, "auto.logs");
        }
      } catch (Exception $switch_error) {
        error_log("Switch configuration error during PXE reinstall: " . $switch_error->getMessage(), 3, "auto.logs");
        // Don't fail the operation if switch configuration fails
      }

      // Update server status to 'Installing' if possible
      try {
        $server_table = $server_type === 'dedicated' ? 'inventory_dedicated_servers' : 'blade_server_inventory';
        $update_sql = "UPDATE $server_table SET status = 'Installing' WHERE id = :server_id";
        $update_sth = $pdo->prepare($update_sql);
        $update_sth->bindValue(':server_id', $server_id);
        $update_sth->execute();

        error_log("Server status updated to 'Installing'", 3, "auto.logs");
      } catch (Exception $update_error) {
        error_log("Failed to update server status: " . $update_error->getMessage(), 3, "auto.logs");
        // Don't fail the operation if status update fails
      }

      // Return success response
      $response = [
        'success' => true,
        'message' => 'PXE reinstall initiated successfully',
        'server_id' => $server_id,
        'os_template' => $os_template,
        'details' => [
          'pxe_configured' => true,
          'power_cycled' => true,
          'status' => 'Server will boot from network and begin OS installation'
        ]
      ];
      
      // Add PXE configuration details if they were created
      if (isset($pxe_result) && $pxe_result['success']) {
        $response['pxe_files'] = $pxe_result['details']['files'];
        $response['message'] .= ' Configuration files generated successfully.';
      } else {
        $response['message'] .= ' IPMI commands executed (PXE files may need manual configuration).';
      }
      
      echo json_encode($response);

    } finally {
      // Always clean up the temporary file
      if (file_exists($temp_password_file)) {
        unlink($temp_password_file);
      }
    }

  } catch (Exception $e) {
    error_log("Error in pxe_reinstall: " . $e->getMessage(), 3, "auto.logs");
    error_log("Stack trace: " . $e->getTraceAsString(), 3, "auto.logs");

    // Clean up temporary file if it exists
    if (isset($temp_password_file) && file_exists($temp_password_file)) {
      unlink($temp_password_file);
    }

    // Return error as JSON
    header('Content-Type: application/json');
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage(),
      'trace' => $e->getTraceAsString()
    ]);
  }
}

elseif($_GET['f'] == 'setup_pxe_dhcp'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data from request
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (empty($data['server_id']) || empty($data['mac_address']) || empty($data['network_config'])) {
      throw new Exception('Server ID, MAC address, and network configuration are required');
    }

    $server_id = $data['server_id'];
    $server_type = $data['server_type'] ?? 'dedicated';
    $mac_address = $data['mac_address'];
    $network_config = $data['network_config'];
    $dhcp_config = $data['dhcp_config'] ?? [];
    $os_template = $data['os_template'] ?? 'unknown';

    error_log("Setting up DHCP for server $server_id with MAC $mac_address");

    // Validate MAC address format
    if (!preg_match('/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/', $mac_address)) {
      throw new Exception('Invalid MAC address format');
    }

    // Validate network configuration
    if (!filter_var($network_config['ip_address'], FILTER_VALIDATE_IP)) {
      throw new Exception('Invalid IP address');
    }
    if (!filter_var($network_config['gateway'], FILTER_VALIDATE_IP)) {
      throw new Exception('Invalid gateway address');
    }
    if (!filter_var($network_config['dns_primary'], FILTER_VALIDATE_IP)) {
      throw new Exception('Invalid primary DNS address');
    }
    if (!preg_match('/^[a-zA-Z0-9-]+$/', $network_config['hostname'])) {
      throw new Exception('Invalid hostname format');
    }

    // Format MAC address consistently
    $mac_formatted = strtolower(str_replace([':', '-'], ':', preg_replace('/[^0-9A-Fa-f]/', '', $mac_address)));
    $mac_formatted = implode(':', str_split($mac_formatted, 2));

    // REAL DHCP SERVER INTEGRATION
    $scripts_path = '/usr/local/bin';
    $dhcp_script = $scripts_path . '/pxe-add-host';
    
    // Check if infrastructure is set up
    if (!file_exists($dhcp_script)) {
      // Fallback to logging only if infrastructure not set up
      error_log("Warning: PXE infrastructure not installed. DHCP reservation simulated only.");
      $dhcp_reservation_created = false;
      $script_output = "Infrastructure setup required - run network_infrastructure_setup.sh";
    } else {
      // Execute real DHCP reservation
      $hostname = $network_config['hostname'];
      $ip_address = $network_config['ip_address'];
      
      $cmd = sprintf(
        '%s %s %s %s %s 2>&1',
        escapeshellcmd($dhcp_script),
        escapeshellarg($hostname),
        escapeshellarg($mac_formatted),
        escapeshellarg($ip_address),
        escapeshellarg($server_id)
      );
      
      $output = [];
      $return_code = 0;
      exec($cmd, $output, $return_code);
      
      if ($return_code !== 0) {
        throw new Exception('DHCP reservation failed: ' . implode(' ', $output));
      }
      
      $dhcp_reservation_created = true;
      $script_output = implode(' ', $output);
      error_log("DHCP reservation created successfully for $hostname ($ip_address)");
    }

    // Create DHCP configuration entry
    $dhcp_entry = [
      'mac_address' => $mac_formatted,
      'ip_address' => $network_config['ip_address'],
      'hostname' => $network_config['hostname'],
      'subnet_mask' => $network_config['subnet_mask'] ?? '*************',
      'gateway' => $network_config['gateway'],
      'dns_primary' => $network_config['dns_primary'],
      'dns_secondary' => $network_config['dns_secondary'] ?? '',
      'tftp_server' => $dhcp_config['tftp_server'] ?? $network_config['gateway'],
      'pxe_filename' => $dhcp_config['pxe_filename'] ?? 'pxelinux.0',
      'next_server' => $dhcp_config['next_server'] ?? $dhcp_config['tftp_server'] ?? $network_config['gateway'],
      'configured_at' => date('Y-m-d H:i:s'),
      'dhcp_reservation_created' => $dhcp_reservation_created,
      'script_output' => $script_output
    ];

    // Store the configuration in the reinstall log
    $reinstall_log_query = "INSERT INTO server_reinstall_log 
      (server_id, server_type, os_id, os_template, initiated_by, ipmi_address, 
       status, dhcp_config, network_config) 
      VALUES (?, ?, 0, ?, ?, '', 'dhcp_configured', ?, ?)
      ON DUPLICATE KEY UPDATE 
      status = 'dhcp_configured', 
      dhcp_config = VALUES(dhcp_config), 
      network_config = VALUES(network_config)";
    
    $stmt = $pdo->prepare($reinstall_log_query);
    $stmt->execute([
      $server_id, 
      $server_type, 
      $os_template, 
      $admin_id,
      json_encode($dhcp_entry),
      json_encode($network_config)
    ]);
    
    echo json_encode([
      'success' => true, 
      'message' => 'DHCP reservation ' . ($dhcp_reservation_created ? 'created' : 'configured') . ' successfully',
      'dhcp_config' => $dhcp_entry,
      'infrastructure_status' => $dhcp_reservation_created ? 'active' : 'simulated'
    ]);
    
  } catch (Exception $e) {
    error_log("DHCP setup error: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
  }
}

elseif($_GET['f'] == 'setup_pxe_server'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data from request
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (empty($data['server_id']) || empty($data['mac_address']) || empty($data['network_config'])) {
      throw new Exception('Server ID, MAC address, and network configuration are required');
    }

    $server_id = $data['server_id'];
    $server_type = $data['server_type'] ?? 'dedicated';
    $mac_address = $data['mac_address'];
    $network_config = $data['network_config'];
    $dhcp_config = $data['dhcp_config'] ?? [];
    $os_template = $data['os_template'] ?? 'unknown';

    error_log("Setting up PXE server for server $server_id");

    // Format MAC address consistently
    $mac_formatted = strtolower(str_replace([':', '-'], ':', preg_replace('/[^0-9A-Fa-f]/', '', $mac_address)));
    $mac_formatted = implode(':', str_split($mac_formatted, 2));
    $hostname = $network_config['hostname'];

    // REAL PXE SERVER INTEGRATION
    $scripts_path = '/usr/local/bin';
    $pxe_script = $scripts_path . '/pxe-create-config';
    $kickstart_dir = '/var/www/html/kickstart';
    
    // Generate kickstart/preseed file content
    $kickstart_content = generateKickstartFile($network_config, $os_template);
    
    // Determine file extension based on OS
    if (stripos($os_template, 'ubuntu') !== false || stripos($os_template, 'debian') !== false) {
      $file_extension = 'preseed';
    } else {
      $file_extension = 'ks';
    }
    
    $kickstart_filename = "install-{$hostname}-" . date('Ymd-His') . ".{$file_extension}";
    
    // Save kickstart file to web-accessible directory
    if (!is_dir($kickstart_dir)) {
      if (!mkdir($kickstart_dir, 0755, true)) {
        throw new Exception("Failed to create kickstart directory");
      }
    }
    
    $kickstart_path = $kickstart_dir . '/' . $kickstart_filename;
    if (file_put_contents($kickstart_path, $kickstart_content) === false) {
      throw new Exception("Failed to save kickstart file");
    }
    chmod($kickstart_path, 0644);
    
    $kickstart_url = "http://" . $_SERVER['SERVER_NAME'] . "/kickstart/" . $kickstart_filename;
    
    // Check if PXE infrastructure is set up
    if (!file_exists($pxe_script)) {
      error_log("Warning: PXE infrastructure not installed. PXE configuration simulated only.");
      $pxe_config_created = false;
      $script_output = "Infrastructure setup required - run network_infrastructure_setup.sh";
    } else {
      // Execute real PXE configuration script
      $cmd = sprintf(
        '%s %s %s %s %s 2>&1',
        escapeshellcmd($pxe_script),
        escapeshellarg($mac_formatted),
        escapeshellarg($hostname),
        escapeshellarg($os_template),
        escapeshellarg($kickstart_url)
      );
      
      $output = [];
      $return_code = 0;
      exec($cmd, $output, $return_code);
      
      if ($return_code !== 0) {
        throw new Exception('PXE configuration failed: ' . implode(' ', $output));
      }
      
      $pxe_config_created = true;
      $script_output = implode(' ', $output);
      error_log("PXE configuration created successfully for $hostname");
    }

    // Generate PXE configuration data
    $pxe_config = [
      'mac_address' => $mac_formatted,
      'hostname' => $hostname,
      'ip_address' => $network_config['ip_address'],
      'os_template' => $os_template,
      'boot_menu_entry' => "01-" . str_replace(':', '-', $mac_formatted),
      'kickstart_file' => $kickstart_filename,
      'kickstart_url' => $kickstart_url,
      'pxe_config_file' => "/var/lib/tftpboot/pxelinux.cfg/01-" . str_replace(':', '-', $mac_formatted),
      'pxe_config_created' => $pxe_config_created,
      'script_output' => $script_output,
      'timestamp' => date('Y-m-d H:i:s')
    ];
    
    // Update the reinstall log with PXE configuration
    $update_log_query = "UPDATE server_reinstall_log 
      SET status = 'pxe_configured', pxe_config = ?, kickstart_content = ?
      WHERE server_id = ? AND status = 'dhcp_configured' 
      ORDER BY initiated_at DESC LIMIT 1";
    
    $stmt = $pdo->prepare($update_log_query);
    $result = $stmt->execute([
      json_encode($pxe_config),
      $kickstart_content,
      $server_id
    ]);
    
    if ($stmt->rowCount() === 0) {
      throw new Exception('No DHCP-configured reinstall session found for this server');
    }
    
    error_log("PXE server configuration completed for server $server_id");
    
    echo json_encode([
      'success' => true, 
      'message' => 'PXE server configuration ' . ($pxe_config_created ? 'created' : 'configured') . ' successfully',
      'pxe_config' => $pxe_config,
      'infrastructure_status' => $pxe_config_created ? 'active' : 'simulated',
      'next_step' => 'server_power_cycle'
    ]);
    
  } catch (Exception $e) {
    error_log("PXE server setup error: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
  }
}

function generateKickstartFile($network_config, $os_template) {
  // Generate a basic kickstart file for CentOS/RHEL or preseed for Debian/Ubuntu
  $content = "# Automated installation configuration for {$network_config['hostname']}\n";
  $content .= "# Generated on " . date('Y-m-d H:i:s') . "\n";
  $content .= "# OS Template: $os_template\n\n";
  
  if (stripos($os_template, 'centos') !== false || stripos($os_template, 'rhel') !== false) {
    // CentOS/RHEL Kickstart
    $content .= "install\n";
    $content .= "text\n";
    $content .= "reboot\n";
    $content .= "lang en_US.UTF-8\n";
    $content .= "keyboard us\n";
    $content .= "timezone UTC\n";
    $content .= "rootpw --plaintext changeme123\n";
    $content .= "network --bootproto=static --ip={$network_config['ip_address']} --netmask={$network_config['subnet_mask']} --gateway={$network_config['gateway']} --nameserver={$network_config['dns_primary']} --hostname={$network_config['hostname']}\n";
    $content .= "firewall --disabled\n";
    $content .= "selinux --disabled\n";
    $content .= "bootloader --location=mbr\n";
    $content .= "zerombr\n";
    $content .= "clearpart --all --initlabel\n";
    $content .= "autopart\n";
    $content .= "%packages\n@base\n%end\n";
  } else {
    // Debian/Ubuntu Preseed
    $content .= "d-i debian-installer/locale string en_US\n";
    $content .= "d-i keyboard-configuration/xkb-keymap select us\n";
    $content .= "d-i netcfg/choose_interface select auto\n";
    $content .= "d-i netcfg/disable_autoconfig boolean true\n";
    $content .= "d-i netcfg/get_ipaddress string {$network_config['ip_address']}\n";
    $content .= "d-i netcfg/get_netmask string {$network_config['subnet_mask']}\n";
    $content .= "d-i netcfg/get_gateway string {$network_config['gateway']}\n";
    $content .= "d-i netcfg/get_nameservers string {$network_config['dns_primary']}\n";
    $content .= "d-i netcfg/get_hostname string {$network_config['hostname']}\n";
    $content .= "d-i passwd/root-password password changeme123\n";
    $content .= "d-i passwd/root-password-again password changeme123\n";
    $content .= "d-i partman-auto/method string regular\n";
    $content .= "d-i partman-auto/choose_recipe select atomic\n";
    $content .= "d-i partman/confirm boolean true\n";
    $content .= "d-i partman/confirm_nooverwrite boolean true\n";
  }
  
  return $content;
}

?>