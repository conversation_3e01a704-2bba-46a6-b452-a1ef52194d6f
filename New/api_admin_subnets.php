<?php
require_once("auth_functions.php");

// --- Custom error log configuration for this script ---
ini_set('log_errors', '1'); // Ensure logging is enabled
ini_set('error_log', __DIR__ . '/ip_logs.logs'); // Log all errors from this script to ip_logs.logs
// ------------------------------------------------------



function recursiveSubnetDeletion($pdo, $subnetId) {
    // Check for child subnets
    $checkChildrenSql = "SELECT r.child_subnet_id, s.assigned_server_id
                         FROM subnet_relationships r
                         JOIN subnets s ON r.child_subnet_id = s.id
                         WHERE r.parent_subnet_id = :subnet_id";
    $checkChildrenSth = $pdo->prepare($checkChildrenSql);
    $checkChildrenSth->bindValue(':subnet_id', $subnetId);
    $checkChildrenSth->execute();

    $childrenSubnets = $checkChildrenSth->fetchAll(PDO::FETCH_ASSOC);

    // If we have children, recursively delete them
    if (count($childrenSubnets) > 0) {
        foreach ($childrenSubnets as $child) {
            $childId = $child['child_subnet_id'];

            // If this child has its own children, recursively delete them first
            recursiveSubnetDeletion($pdo, $childId);

            // Delete location mapping
            $deleteLocationSql = "DELETE FROM subnet_location_map WHERE subnet_id = :subnet_id";
            $deleteLocationSth = $pdo->prepare($deleteLocationSql);
            $deleteLocationSth->bindValue(':subnet_id', $childId);
            $deleteLocationSth->execute();

            // Delete all IP addresses associated with this subnet
            $deleteIpsSql = "DELETE FROM ip_addresses WHERE subnet_id = :subnet_id";
            $deleteIpsSth = $pdo->prepare($deleteIpsSql);
            $deleteIpsSth->bindValue(':subnet_id', $childId);
            $deleteIpsSth->execute();

            // Delete subnet relationships - using different parameter names to avoid binding issues
            $deleteRelationshipsSql = "DELETE FROM subnet_relationships
                                      WHERE parent_subnet_id = :parent_id OR child_subnet_id = :child_id";
            $deleteRelationshipsSth = $pdo->prepare($deleteRelationshipsSql);
            $deleteRelationshipsSth->bindValue(':parent_id', $childId);
            $deleteRelationshipsSth->bindValue(':child_id', $childId);
            $deleteRelationshipsSth->execute();

            // Delete the child subnet
            $deleteSubnetSql = "DELETE FROM subnets WHERE id = :subnet_id";
            $deleteSubnetSth = $pdo->prepare($deleteSubnetSql);
            $deleteSubnetSth->bindValue(':subnet_id', $childId);
            $deleteSubnetSth->execute();

            error_log("Deleted child subnet with ID $childId");
        }
    }
}





if($_GET['f'] == 'get_subnets'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get filter parameters from query string or post data
    $data = json_decode(file_get_contents('php://input'), true) ?? [];
    $status = isset($data['status']) && $data['status'] !== 'All' ? $data['status'] : null;
    $category = isset($data['category']) && $data['category'] !== 'All' ? $data['category'] : null;
    $location = isset($data['location']) && $data['location'] !== 'All' ? $data['location'] : null;
    $search = isset($data['search']) ? $data['search'] : null;

    // Build query with appropriate joins - UPDATED to use subnet_location_map
    $sql = "
      SELECT s.*,
             c.city AS location_name,
             c.datacenter,
             sw.label AS switch_name,
             CONCAT('VLAN ', s.vlan) AS vlan_name,
CASE
  WHEN s.assigned_server_id IS NOT NULL THEN 'Assigned'
  WHEN s.is_allocated = 1 THEN 'Allocated'
  WHEN s.manual_alocation IS NOT NULL AND TRIM(s.manual_alocation) != '' THEN 'Allocated'
  -- Check if this subnet has any allocated IPs
  WHEN (
    SELECT COUNT(*)
    FROM ip_addresses
    WHERE subnet_id = s.id AND is_used = 1
  ) > 0 THEN 'Unavailable'
  -- Check if this subnet has any allocated or assigned direct children
  WHEN (
    SELECT COUNT(*)
    FROM subnet_relationships r1
    JOIN subnets c1 ON r1.child_subnet_id = c1.id
    WHERE r1.parent_subnet_id = s.id AND (c1.assigned_server_id IS NOT NULL OR c1.is_allocated = 1 OR c1.manual_alocation IS NOT NULL)
  ) > 0 THEN 'Unavailable'
  -- Check if any child subnet has a manual_alocation (is allocated) - direct or indirect
  WHEN EXISTS (
    WITH RECURSIVE subnet_tree AS (
      -- Start with direct children
      SELECT c.id, c.manual_alocation
      FROM subnet_relationships r
      JOIN subnets c ON r.child_subnet_id = c.id
      WHERE r.parent_subnet_id = s.id

      UNION ALL

      -- Add all descendants
      SELECT c.id, c.manual_alocation
      FROM subnet_relationships r
      JOIN subnets c ON r.child_subnet_id = c.id
      JOIN subnet_tree st ON r.parent_subnet_id = st.id
    )
    SELECT 1 FROM subnet_tree st
    WHERE st.manual_alocation IS NOT NULL
    LIMIT 1
  ) THEN 'Unavailable'
  -- Check if any child subnet has allocated IPs (direct or indirect)
  WHEN EXISTS (
    WITH RECURSIVE subnet_tree AS (
      -- Start with direct children
      SELECT c.id
      FROM subnet_relationships r
      JOIN subnets c ON r.child_subnet_id = c.id
      WHERE r.parent_subnet_id = s.id

      UNION ALL

      -- Add all descendants
      SELECT c.id
      FROM subnet_relationships r
      JOIN subnets c ON r.child_subnet_id = c.id
      JOIN subnet_tree st ON r.parent_subnet_id = st.id
    )
    SELECT 1 FROM subnet_tree st
    JOIN ip_addresses ip ON ip.subnet_id = st.id
    WHERE ip.is_used = 1
    LIMIT 1
  ) THEN 'Unavailable'
  -- Check if this subnet is a child of an allocated or unavailable subnet
  WHEN EXISTS (
    WITH RECURSIVE parent_tree AS (
      -- Start with direct parent
      SELECT p.id, p.is_allocated, p.manual_alocation
      FROM subnet_relationships r
      JOIN subnets p ON r.parent_subnet_id = p.id
      WHERE r.child_subnet_id = s.id

      UNION ALL

      -- Add all ancestors
      SELECT p.id, p.is_allocated, p.manual_alocation
      FROM subnet_relationships r
      JOIN subnets p ON r.parent_subnet_id = p.id
      JOIN parent_tree pt ON r.child_subnet_id = pt.id
    )
    SELECT 1 FROM parent_tree pt
    WHERE pt.is_allocated = 1 OR pt.manual_alocation IS NOT NULL
    LIMIT 1
  ) THEN 'Unavailable'
  -- Check if this subnet is a parent of any subnet with allocated IPs
  WHEN EXISTS (
    WITH RECURSIVE child_tree AS (
      -- Start with direct children
      SELECT c.id
      FROM subnet_relationships r
      JOIN subnets c ON r.child_subnet_id = c.id
      WHERE r.parent_subnet_id = s.id

      UNION ALL

      -- Add all descendants
      SELECT c.id
      FROM subnet_relationships r
      JOIN subnets c ON r.child_subnet_id = c.id
      JOIN child_tree ct ON r.parent_subnet_id = ct.id
    )
    SELECT 1 FROM child_tree ct
    JOIN ip_addresses ip ON ip.subnet_id = ct.id
    WHERE ip.is_used = 1
    LIMIT 1
  ) THEN 'Unavailable'
  WHEN (
    -- Check if all children are allocated or assigned (subnet is full)
    SELECT COUNT(*)
    FROM subnet_relationships r1
    JOIN subnets c1 ON r1.child_subnet_id = c1.id
    WHERE r1.parent_subnet_id = s.id
  ) > 0 AND (
    SELECT COUNT(*)
    FROM subnet_relationships r2
    JOIN subnets c2 ON r2.child_subnet_id = c2.id
    WHERE r2.parent_subnet_id = s.id AND (c2.assigned_server_id IS NOT NULL OR c2.is_allocated = 1)
  ) = (
    SELECT COUNT(*)
    FROM subnet_relationships r3
    JOIN subnets c3 ON r3.child_subnet_id = c3.id
    WHERE r3.parent_subnet_id = s.id
  ) THEN 'Full'
  -- Check if this subnet is a parent of an allocated subnet (direct or indirect)
  WHEN EXISTS (
    WITH RECURSIVE subnet_tree AS (
      -- Start with direct children
      SELECT c.id, c.is_allocated, c.manual_alocation, c.subnet
      FROM subnet_relationships r
      JOIN subnets c ON r.child_subnet_id = c.id
      WHERE r.parent_subnet_id = s.id

      UNION ALL

      -- Add all descendants
      SELECT c.id, c.is_allocated, c.manual_alocation, c.subnet
      FROM subnet_relationships r
      JOIN subnets c ON r.child_subnet_id = c.id
      JOIN subnet_tree st ON r.parent_subnet_id = st.id
    )
    SELECT 1 FROM subnet_tree st
    WHERE st.is_allocated = 1 OR st.manual_alocation IS NOT NULL
    LIMIT 1
  ) THEN 'Unavailable'
  -- Check if any descendant of this subnet has a manual_alocation or is allocated
  WHEN EXISTS (
    WITH RECURSIVE all_descendants AS (
      -- Start with direct children
      SELECT c.id, c.manual_alocation, c.is_allocated, c.subnet
      FROM subnet_relationships r
      JOIN subnets c ON r.child_subnet_id = c.id
      WHERE r.parent_subnet_id = s.id

      UNION ALL

      -- Add all descendants recursively
      SELECT c.id, c.manual_alocation, c.is_allocated, c.subnet
      FROM subnet_relationships r
      JOIN subnets c ON r.child_subnet_id = c.id
      JOIN all_descendants ad ON r.parent_subnet_id = ad.id
    )
    SELECT 1 FROM all_descendants ad
    WHERE ad.manual_alocation IS NOT NULL OR ad.is_allocated = 1
    LIMIT 1
  ) THEN 'Unavailable'
  -- Check if any descendant of this subnet has allocated IPs
  WHEN EXISTS (
    WITH RECURSIVE all_descendants AS (
      -- Start with direct children
      SELECT c.id
      FROM subnet_relationships r
      JOIN subnets c ON r.child_subnet_id = c.id
      WHERE r.parent_subnet_id = s.id

      UNION ALL

      -- Add all descendants recursively
      SELECT c.id
      FROM subnet_relationships r
      JOIN subnets c ON r.child_subnet_id = c.id
      JOIN all_descendants ad ON r.parent_subnet_id = ad.id
    )
    SELECT 1 FROM all_descendants ad
    JOIN ip_addresses ip ON ip.subnet_id = ad.id
    WHERE ip.is_used = 1
    LIMIT 1
  ) THEN 'Unavailable'
  ELSE 'Available'
END AS status,
             CASE
               WHEN s.is_root_subnet = 1 THEN 'Root'
               WHEN s.is_customer_subnet = 1 THEN 'Customer'
               WHEN s.is_managmenet_subnet = 1 THEN 'Management'
               WHEN s.is_transit_subnet = 1 THEN 'Transit'
               ELSE 'Normal'
             END AS category,
             IFNULL(
               (SELECT COUNT(*) FROM ip_addresses WHERE subnet_id = s.id AND is_used = 1),
               0
             ) AS used_ips,
             POWER(2, (32 - s.subnet_size)) - 2 AS total_ips,
             IFNULL((SELECT COUNT(*) FROM ip_addresses WHERE subnet_id = s.id AND is_used = 1) /
             (POWER(2, (32 - s.subnet_size)) - 2) * 100, 0) AS utilization_percent,
             CONCAT('SUB-', LPAD(s.id, 4, '0')) AS subnet_id,
             INET_NTOA(INET_ATON(SUBSTRING_INDEX(s.subnet, '/', 1)) + 1) AS gateway,
             co.country AS country_name,
             s.is_public,
             s.note
      FROM subnets s
      LEFT JOIN subnet_location_map slm ON s.id = slm.subnet_id
      LEFT JOIN cities c ON slm.city_id = c.id
      LEFT JOIN countries co ON slm.country_id = co.id
      LEFT JOIN inventory_switches sw ON s.switch_id = sw.id
    ";

    // Add WHERE clause if filters are applied
    $whereConditions = [];
    $params = [];

    if ($status) {
      $whereConditions[] = "(
        CASE
          WHEN s.assigned_server_id IS NOT NULL THEN 'Assigned'
          WHEN s.is_allocated = 1 THEN 'Allocated'
          WHEN s.manual_alocation IS NOT NULL AND TRIM(s.manual_alocation) != '' THEN 'Allocated'
          -- Check if this subnet has any allocated IPs
          WHEN (
            SELECT COUNT(*)
            FROM ip_addresses
            WHERE subnet_id = s.id AND is_used = 1
          ) > 0 THEN 'Unavailable'
          -- Check if this subnet has any allocated or assigned direct children
          WHEN (
            SELECT COUNT(*)
            FROM subnet_relationships r1
            JOIN subnets c1 ON r1.child_subnet_id = c1.id
            WHERE r1.parent_subnet_id = s.id AND (c1.assigned_server_id IS NOT NULL OR c1.is_allocated = 1 OR c1.manual_alocation IS NOT NULL)
          ) > 0 THEN 'Unavailable'
          -- Check if any child subnet has a manual_alocation (is allocated) - direct or indirect
          WHEN EXISTS (
            WITH RECURSIVE subnet_tree AS (
              -- Start with direct children
              SELECT c.id, c.manual_alocation
              FROM subnet_relationships r
              JOIN subnets c ON r.child_subnet_id = c.id
              WHERE r.parent_subnet_id = s.id

              UNION ALL

              -- Add all descendants
              SELECT c.id, c.manual_alocation
              FROM subnet_relationships r
              JOIN subnets c ON r.child_subnet_id = c.id
              JOIN subnet_tree st ON r.parent_subnet_id = st.id
            )
            SELECT 1 FROM subnet_tree st
            WHERE st.manual_alocation IS NOT NULL
            LIMIT 1
          ) THEN 'Unavailable'
          -- Check if any child subnet has allocated IPs (direct or indirect)
          WHEN EXISTS (
            WITH RECURSIVE subnet_tree AS (
              -- Start with direct children
              SELECT c.id
              FROM subnet_relationships r
              JOIN subnets c ON r.child_subnet_id = c.id
              WHERE r.parent_subnet_id = s.id

              UNION ALL

              -- Add all descendants
              SELECT c.id
              FROM subnet_relationships r
              JOIN subnets c ON r.child_subnet_id = c.id
              JOIN subnet_tree st ON r.parent_subnet_id = st.id
            )
            SELECT 1 FROM subnet_tree st
            JOIN ip_addresses ip ON ip.subnet_id = st.id
            WHERE ip.is_used = 1
            LIMIT 1
          ) THEN 'Unavailable'
          WHEN (
            -- Check if all children are allocated or assigned (subnet is full)
            SELECT COUNT(*)
            FROM subnet_relationships r1
            JOIN subnets c1 ON r1.child_subnet_id = c1.id
            WHERE r1.parent_subnet_id = s.id
          ) > 0 AND (
            SELECT COUNT(*)
            FROM subnet_relationships r2
            JOIN subnets c2 ON r2.child_subnet_id = c2.id
            WHERE r2.parent_subnet_id = s.id AND (c2.assigned_server_id IS NOT NULL OR c2.is_allocated = 1)
          ) = (
            SELECT COUNT(*)
            FROM subnet_relationships r3
            JOIN subnets c3 ON r3.child_subnet_id = c3.id
            WHERE r3.parent_subnet_id = s.id
          ) THEN 'Full'
          -- Check if this subnet is a child of an allocated or unavailable subnet
          WHEN EXISTS (
            WITH RECURSIVE parent_tree AS (
              -- Start with direct parent
              SELECT p.id, p.is_allocated, p.manual_alocation
              FROM subnet_relationships r
              JOIN subnets p ON r.parent_subnet_id = p.id
              WHERE r.child_subnet_id = s.id

              UNION ALL

              -- Add all ancestors
              SELECT p.id, p.is_allocated, p.manual_alocation
              FROM subnet_relationships r
              JOIN subnets p ON r.parent_subnet_id = p.id
              JOIN parent_tree pt ON r.child_subnet_id = pt.id
            )
            SELECT 1 FROM parent_tree pt
            WHERE pt.is_allocated = 1 OR pt.manual_alocation IS NOT NULL
            LIMIT 1
          ) THEN 'Unavailable'
          -- Check if this subnet is a parent of any subnet with allocated IPs
          WHEN EXISTS (
            WITH RECURSIVE child_tree AS (
              -- Start with direct children
              SELECT c.id, c.subnet, c.is_allocated, c.manual_alocation
              FROM subnet_relationships r
              JOIN subnets c ON r.child_subnet_id = c.id
              WHERE r.parent_subnet_id = s.id

              UNION ALL

              -- Add all descendants
              SELECT c.id, c.subnet, c.is_allocated, c.manual_alocation
              FROM subnet_relationships r
              JOIN subnets c ON r.child_subnet_id = c.id
              JOIN child_tree ct ON r.parent_subnet_id = ct.id
            )
            SELECT 1 FROM child_tree ct
            WHERE ct.is_allocated = 1 OR ct.manual_alocation IS NOT NULL
            LIMIT 1
          ) THEN 'Unavailable'
          -- Check if this subnet is a parent of any subnet with allocated IPs
          WHEN EXISTS (
            WITH RECURSIVE child_tree AS (
              -- Start with direct children
              SELECT c.id
              FROM subnet_relationships r
              JOIN subnets c ON r.child_subnet_id = c.id
              WHERE r.parent_subnet_id = s.id

              UNION ALL

              -- Add all descendants
              SELECT c.id
              FROM subnet_relationships r
              JOIN subnets c ON r.child_subnet_id = c.id
              JOIN child_tree ct ON r.parent_subnet_id = ct.id
            )
            SELECT 1 FROM child_tree ct
            JOIN ip_addresses ip ON ip.subnet_id = ct.id
            WHERE ip.is_used = 1
            LIMIT 1
          ) THEN 'Unavailable'
          -- Check if any descendant of this subnet has a manual_alocation or is allocated
          WHEN EXISTS (
            WITH RECURSIVE all_descendants AS (
              -- Start with direct children
              SELECT c.id, c.manual_alocation, c.is_allocated, c.subnet
              FROM subnet_relationships r
              JOIN subnets c ON r.child_subnet_id = c.id
              WHERE r.parent_subnet_id = s.id

              UNION ALL

              -- Add all descendants recursively
              SELECT c.id, c.manual_alocation, c.is_allocated, c.subnet
              FROM subnet_relationships r
              JOIN subnets c ON r.child_subnet_id = c.id
              JOIN all_descendants ad ON r.parent_subnet_id = ad.id
            )
            SELECT 1 FROM all_descendants ad
            WHERE ad.manual_alocation IS NOT NULL OR ad.is_allocated = 1
            LIMIT 1
          ) THEN 'Unavailable'
          -- Check if any descendant of this subnet has allocated IPs
          WHEN EXISTS (
            WITH RECURSIVE all_descendants AS (
              -- Start with direct children
              SELECT c.id
              FROM subnet_relationships r
              JOIN subnets c ON r.child_subnet_id = c.id
              WHERE r.parent_subnet_id = s.id

              UNION ALL

              -- Add all descendants recursively
              SELECT c.id
              FROM subnet_relationships r
              JOIN subnets c ON r.child_subnet_id = c.id
              JOIN all_descendants ad ON r.parent_subnet_id = ad.id
            )
            SELECT 1 FROM all_descendants ad
            JOIN ip_addresses ip ON ip.subnet_id = ad.id
            WHERE ip.is_used = 1
            LIMIT 1
          ) THEN 'Unavailable'
          ELSE 'Available'
        END
      ) = :status";
      $params[':status'] = $status;
    }

    if ($category) {
      if ($category == 'Root') {
        $whereConditions[] = "s.is_root_subnet = 1";
      } else if ($category == 'Customer') {
        $whereConditions[] = "s.is_customer_subnet = 1";
      } else if ($category == 'Management') {
        $whereConditions[] = "s.is_managmenet_subnet = 1";
      } else if ($category == 'Transit') {
        $whereConditions[] = "s.is_transit_subnet = 1";
      } else if ($category == 'Normal') {
        $whereConditions[] = "s.is_root_subnet = 0 AND s.is_customer_subnet = 0 AND s.is_managmenet_subnet = 0 AND s.is_transit_subnet = 0";
      }
    }

    if ($location) {
      $whereConditions[] = "c.city = :location";
      $params[':location'] = $location;
    }

    if ($search) {
      $whereConditions[] = "(s.subnet LIKE :search OR CONCAT('SUB-', LPAD(s.id, 4, '0')) LIKE :search OR CONCAT('VLAN ', s.vlan) LIKE :search)";
      $params[':search'] = '%' . $search . '%';
    }

    if (!empty($whereConditions)) {
      $sql .= " WHERE " . implode(" AND ", $whereConditions);
    }

    // Add sorting - default to IP sorting
    $sortField = isset($data['sortField']) ? $data['sortField'] : 'cidr'; // Default to cidr
    $sortDirection = isset($data['sortDirection']) ? strtoupper($data['sortDirection']) : 'ASC'; // Default to ASC for IP addresses

    // Validate sort direction
    if (!in_array($sortDirection, ['ASC', 'DESC'])) {
      $sortDirection = 'ASC';
    }

    // Map frontend sort fields to backend fields
    $sortFieldMap = [
      'id' => 's.id',
      'cidr' => 'INET_ATON(SUBSTRING_INDEX(s.subnet, \'/\', 1)), s.subnet_size', // Sort by IP address first, then subnet size
      'name' => 'subnet_id',
      'location' => 'c.city',
      'vlan' => 's.vlan',
      'utilizationPercent' => 'utilization_percent',
      'status' => 'status',
      'category' => 'category',
      'lastUpdated' => 's.id' // Using ID as a proxy for "last updated" since we don't have that field
    ];

    // Use the mapped field, or default to proper IP sorting
    $orderByField = isset($sortFieldMap[$sortField]) ? $sortFieldMap[$sortField] : 'INET_ATON(SUBSTRING_INDEX(s.subnet, \'/\', 1)), s.subnet_size';
    $sql .= " ORDER BY " . $orderByField . " $sortDirection";

    // Execute query
    $sth = $pdo->prepare($sql);
    foreach ($params as $param => $value) {
      $sth->bindValue($param, $value);
    }
    $sth->execute();

    // Format results for the frontend
    $subnets = [];
    while ($row = $sth->fetch(PDO::FETCH_ASSOC)) {
      // Get server name if it's assigned
      $assignedServerName = 'Unassigned';
      if ($row['assigned_server_id']) {
        $serverId = $row['assigned_server_id'];
        $serverTable = ($serverId >= 1000000) ? 'inventory_dedicated_servers' : 'blade_server_inventory';
        $serverSql = "SELECT label FROM $serverTable WHERE id = :server_id";
        $serverSth = $pdo->prepare($serverSql);
        $serverSth->bindValue(':server_id', $serverId);
        $serverSth->execute();

        if ($serverSth->rowCount() > 0) {
          $assignedServerName = $serverSth->fetchColumn();
        } else {
          $assignedServerName = 'Server #' . $serverId;
        }
      }

      // Format each subnet record to match the frontend expectations
      $subnet = [
        'id' => $row['subnet_id'],
        'cidr' => $row['subnet'],
        'name' => $row['subnet_id'] . ' ' . $row['subnet'],
        'location' => $row['location_name'] ?? 'Unknown',
        'country' => $row['country_name'] ?? 'Unknown',
        'vlan' => $row['vlan_name'],
        'usedIPs' => (int)$row['used_ips'],
        'totalIPs' => (int)$row['total_ips'],
        'utilizationPercent' => round($row['utilization_percent']),
        'status' => $row['status'],
        'lastUpdated' => date('Y-m-d'), // Current date as proxy
        'gateway' => $row['gateway'],
        'assignedTo' => $row['assigned_server_id'] ? $assignedServerName : 'Unassigned',
        'manual_alocation' => $row['manual_alocation'] ? $row['manual_alocation'] : 'Subnet for ' . ($row['location_name'] ?? 'Unknown location'),
        'category' => $row['category'],
        'is_public' => $row['is_public'] ?? 0,
        'note' => $row['note'] ?? '',
        'raw_data' => $row // Include raw data for debugging
      ];

      $subnets[] = $subnet;
    }

    // Return subnets as JSON
    header('Content-Type: application/json');
    echo json_encode($subnets);

  } catch (Exception $e) {
    error_log("Error in get_subnets: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to fetch subnets: ' . $e->getMessage()
    ]);
  }
}


// Add a new subnet
elseif($_GET['f'] == 'add_subnet'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['subnet']) || empty($data['subnet'])) {
      throw new Exception('Subnet CIDR is required');
    }

    // Get IP version
    $ipVersion = isset($data['ipVersion']) ? $data['ipVersion'] : 'ipv4';

    // Validate and process based on IP version
    if ($ipVersion === 'ipv4') {
      // IPv4 validation
      if (!preg_match('/^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})\/(\d{1,2})$/', $data['subnet'], $matches)) {
        throw new Exception('Invalid IPv4 format. Expected: ***********/24');
      }

      // Validate each octet
      for ($i = 1; $i <= 4; $i++) {
        if ((int)$matches[$i] > 255) {
          throw new Exception('Invalid IPv4 address: octet exceeds 255');
        }
      }

      // Validate subnet mask
      $subnetSize = (int)$matches[5];
      if ($subnetSize < 1 || $subnetSize > 32) {
        throw new Exception('Invalid IPv4 subnet mask. Must be between 1 and 32');
      }

      // Normalize subnet to ensure it's the network address
      $ipOctets = array_slice($matches, 1, 4);
      $ipAddr = implode('.', $ipOctets);
      $netmask = ~((1 << (32 - $subnetSize)) - 1);
      $ipLong = ip2long($ipAddr);
      $networkLong = $ipLong & $netmask;
      $normalizedSubnet = long2ip($networkLong) . '/' . $subnetSize;
    } else {
      // IPv6 validation
      // Extract address and prefix
      list($ipv6Address, $prefixLength) = explode('/', $data['subnet']);

      // Validate IPv6 address format
      if (!filter_var($ipv6Address, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
        throw new Exception('Invalid IPv6 address format');
      }

      // Validate prefix length
      $subnetSize = (int)$prefixLength;
      if ($subnetSize < 1 || $subnetSize > 128) {
        throw new Exception('Invalid IPv6 prefix length. Must be between 1 and 128');
      }

      // For IPv6, we'll just use the original subnet as normalization is complex
      $normalizedSubnet = $data['subnet'];
    }

    // Check if subnet already exists
    $checkSql = "SELECT COUNT(*) FROM subnets WHERE subnet = :subnet";
    $checkSth = $pdo->prepare($checkSql);
    $checkSth->bindValue(':subnet', $normalizedSubnet);
    $checkSth->execute();

    if ($checkSth->fetchColumn() > 0) {
      throw new Exception('Subnet already exists');
    }

    // Get subnet type from request
    $subnetType = isset($data['subnet_type']) ? $data['subnet_type'] : 'normal';

    // Set flags based on subnet type
    $isRootSubnet = ($subnetType === 'root') ? 1 : 0;
    $isCustomerSubnet = ($subnetType === 'customer') ? 1 : 0;
    $isManagementSubnet = ($subnetType === 'management') ? 1 : 0;
    $isTransitSubnet = ($subnetType === 'transit') ? 1 : 0;

    // Extract other fields from request
    $isPublic = isset($data['is_public']) && $data['is_public'] === 'yes' ? 1 : 0;
    $vlan = isset($data['vlan']) && !empty($data['vlan']) ? (int)$data['vlan'] : 0;
    $isConfigured = isset($data['is_configured']) ? (int)$data['is_configured'] : 0;
    $asn = isset($data['asn']) && !empty($data['asn']) ? (int)$data['asn'] : 0;

    // Begin transaction
    $pdo->beginTransaction();

    // Insert the subnet
    $sql = "INSERT INTO subnets (
              subnet,
              subnet_size,
              vlan,
              is_root_subnet,
              is_customer_subnet,
              is_managmenet_subnet,
              is_transit_subnet,
              is_configured,
              is_public,
              asn,
              note
            ) VALUES (
              :subnet,
              :subnet_size,
              :vlan,
              :is_root_subnet,
              :is_customer_subnet,
              :is_managmenet_subnet,
              :is_transit_subnet,
              :is_configured,
              :is_public,
              :asn,
              :note
            )";

    $sth = $pdo->prepare($sql);
    $sth->bindValue(':subnet', $normalizedSubnet);
    $sth->bindValue(':subnet_size', $subnetSize);
    $sth->bindValue(':vlan', $vlan);
    $sth->bindValue(':is_root_subnet', $isRootSubnet);
    $sth->bindValue(':is_customer_subnet', $isCustomerSubnet);
    $sth->bindValue(':is_managmenet_subnet', $isManagementSubnet);
    $sth->bindValue(':is_transit_subnet', $isTransitSubnet);
    $sth->bindValue(':is_configured', $isConfigured);
    $sth->bindValue(':is_public', $isPublic);
    $sth->bindValue(':asn', $asn);
    $sth->bindValue(':note', $data['note'] ?? '');
    $sth->execute();

    // Get the newly created subnet ID
    $subnetId = $pdo->lastInsertId();

    // Add location mapping if city_id is provided
    if (isset($data['city_id']) && !empty($data['city_id'])) {
      $locationSql = "INSERT INTO subnet_location_map (
                        subnet_id,
                        city_id,
                        country_id
                      ) VALUES (
                        :subnet_id,
                        :city_id,
                        :country_id
                      )";

      $locationSth = $pdo->prepare($locationSql);
      $locationSth->bindValue(':subnet_id', $subnetId);
      $locationSth->bindValue(':city_id', $data['city_id']);
      $locationSth->bindValue(':country_id', $data['country_id']);
      $locationSth->execute();
    }

    // Generate IP addresses if requested and if it's IPv4
    if (($data['generate_ips'] ?? false) && $ipVersion === 'ipv4') {
      $ipLong = ip2long(explode('/', $normalizedSubnet)[0]);

      // Calculate number of IPs based on subnet size
      $ipCount = pow(2, (32 - $subnetSize)) - 2; // Subtract network and broadcast addresses

      // For big subnets, limit the number of IPs we create
      if ($ipCount > 1000) {
        $ipCount = 1000; // Cap at 1000 IPs
      }

      // Prepare batch insert statement
      $ipSql = "INSERT INTO ip_addresses (subnet_id, ip_address, is_used) VALUES ";
      $ipValues = [];
      $ipParams = [];

      // Start from first usable IP (network address + 1)
      for ($i = 1; $i <= $ipCount; $i++) {
        $currentIpLong = $ipLong + $i;
        $currentIp = long2ip($currentIpLong);

        $paramName = ':ip' . $i;
        $ipValues[] = "($subnetId, $paramName, 0)";
        $ipParams[$paramName] = $currentIp;
      }

      if (!empty($ipValues)) {
        $ipSql .= implode(', ', $ipValues);
        $ipSth = $pdo->prepare($ipSql);

        foreach ($ipParams as $param => $value) {
          $ipSth->bindValue($param, $value);
        }

        $ipSth->execute();
      }
    }

    // Commit transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $subnetId,
      'subnet' => $normalizedSubnet,
      'message' => 'Subnet added successfully'
    ]);

  } catch (Exception $e) {
    // Rollback transaction if it was started
    if (isset($pdo) && $pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in add_subnet: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to add subnet: ' . $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'assign_subnet_to_server'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['subnet_id']) || empty($data['subnet_id'])) {
      throw new Exception('Subnet ID is required');
    }

    if (!isset($data['server_id']) || empty($data['server_id'])) {
      throw new Exception('Server ID is required');
    }

    if (!isset($data['server_type']) || empty($data['server_type'])) {
      throw new Exception('Server type is required (dedicated or blade)');
    }

    // Parse subnet ID (support both numeric and SUB-xxxx formats)
    if (is_numeric($data['subnet_id'])) {
      $subnetId = (int)$data['subnet_id'];
    } else if (preg_match('/SUB-(\d+)/', $data['subnet_id'], $matches)) {
      $subnetId = (int)$matches[1];
    } else {
      throw new Exception('Invalid subnet ID format');
    }

    $serverId = (int)$data['server_id'];
    $serverType = $data['server_type'];
    $setAsMainIp = $data['set_as_main_ip'] ?? false;

    // Begin transaction
    $pdo->beginTransaction();

    // Verify the subnet exists
    $checkSubnetSql = "SELECT * FROM subnets WHERE id = :subnet_id";
    $checkSubnetSth = $pdo->prepare($checkSubnetSql);
    $checkSubnetSth->bindValue(':subnet_id', $subnetId);
    $checkSubnetSth->execute();

    if ($checkSubnetSth->rowCount() === 0) {
      throw new Exception('Subnet not found');
    }

    $subnet = $checkSubnetSth->fetch(PDO::FETCH_ASSOC);

    // Check if subnet is already assigned or allocated
    if ($subnet['assigned_server_id']) {
      throw new Exception('Subnet is already assigned to a server');
    }

    if ($subnet['is_allocated']) {
      throw new Exception('Subnet is already allocated');
    }

    // Check if any parent subnet is already allocated or assigned
    $checkParentsSql = "SELECT p.id, p.subnet, p.is_allocated, p.assigned_server_id, p.manual_alocation
                       FROM subnet_relationships r
                       JOIN subnets p ON r.parent_subnet_id = p.id
                       WHERE r.child_subnet_id = :subnet_id
                       AND (p.is_allocated = 1 OR p.assigned_server_id IS NOT NULL)";
    $checkParentsSth = $pdo->prepare($checkParentsSql);
    $checkParentsSth->bindValue(':subnet_id', $subnetId);
    $checkParentsSth->execute();

    $allocatedParents = $checkParentsSth->fetchAll(PDO::FETCH_ASSOC);
    if (count($allocatedParents) > 0) {
      $parent = $allocatedParents[0];
      $parentStatus = $parent['is_allocated'] ? 'allocated' : 'assigned';
      $parentDesc = $parent['is_allocated'] && $parent['manual_alocation'] ?
                   " with manual_alocation: '{$parent['manual_alocation']}'" : '';
      throw new Exception("Cannot assign this subnet: Parent subnet {$parent['subnet']} is already {$parentStatus}{$parentDesc}");
    }

    // Check if we have children and if they can be deleted
    $checkChildrenSql = "SELECT r.child_subnet_id, s.assigned_server_id, s.is_allocated, s.subnet, s.manual_alocation
                         FROM subnet_relationships r
                         JOIN subnets s ON r.child_subnet_id = s.id
                         WHERE r.parent_subnet_id = :subnet_id";
    $checkChildrenSth = $pdo->prepare($checkChildrenSql);
    $checkChildrenSth->bindValue(':subnet_id', $subnetId);
    $checkChildrenSth->execute();

    $childrenSubnets = $checkChildrenSth->fetchAll(PDO::FETCH_ASSOC);

    // If we have children, check if any are assigned or allocated
    if (count($childrenSubnets) > 0) {
        $assignedOrAllocatedChildren = array_filter($childrenSubnets, function($child) {
            return $child['assigned_server_id'] !== null || $child['is_allocated'] == 1;
        });

        if (count($assignedOrAllocatedChildren) > 0) {
            $child = reset($assignedOrAllocatedChildren); // Get the first child
            $childStatus = $child['is_allocated'] ? 'allocated' : 'assigned';
            $childDesc = $child['is_allocated'] && $child['manual_alocation'] ?
                       " with manual_alocation: '{$child['manual_alocation']}'" : '';
            throw new Exception("Cannot assign this subnet: Child subnet {$child['subnet']} is already {$childStatus}{$childDesc}");
        }

        // Recursively delete all child subnets
        error_log("Deleting " . count($childrenSubnets) . " child subnets of parent subnet $subnetId");
        recursiveSubnetDeletion($pdo, $subnetId);
    }

    // Determine the server table based on server type
    $serverTable = ($serverType === 'dedicated') ? 'inventory_dedicated_servers' : 'blade_server_inventory';
    
    error_log("assign_subnet_to_server: Using table '$serverTable' for server ID $serverId (type: $serverType)");

    // Verify the server exists and get its current main/additional IPs
    $checkServerSql = "SELECT id, main_ip, additional_ips FROM $serverTable WHERE id = :server_id";
    $checkServerSth = $pdo->prepare($checkServerSql);
    $checkServerSth->bindValue(':server_id', $serverId);
    $checkServerSth->execute();

    if ($checkServerSth->rowCount() === 0) {
      throw new Exception('Server not found');
    }

    $server = $checkServerSth->fetch(PDO::FETCH_ASSOC);

    // The full subnet in CIDR notation
    $subnetCidr = $subnet['subnet'];

    // Update server's IPs
    if ($setAsMainIp || !$server['main_ip']) {
      // Set as main IP if no main IP exists or explicitly requested
      error_log("assign_subnet_to_server: Setting main_ip to '$subnetCidr' in table '$serverTable' for server ID $serverId");
      $updateServerSql = "UPDATE $serverTable SET main_ip = :main_ip WHERE id = :server_id";
      $updateServerSth = $pdo->prepare($updateServerSql);
      $updateServerSth->bindValue(':main_ip', $subnetCidr);
      $updateServerSth->bindValue(':server_id', $serverId);
      $updateServerSth->execute();
      
      $rowsAffected = $updateServerSth->rowCount();
      error_log("assign_subnet_to_server: Main IP update affected $rowsAffected row(s)");
    } else {
      // Add to additional IPs
      // Ensure additional_ips is not null
      $additionalIps = $server['additional_ips'] ?? '';

      // If additional_ips is empty, set the new subnet
      // If not empty, append the new subnet
      $newAdditionalIps = $additionalIps
        ? (strpos($additionalIps, $subnetCidr) === false
           ? $additionalIps . ',' . $subnetCidr
           : $additionalIps)
        : $subnetCidr;

      $updateAdditionalIpsSql = "UPDATE $serverTable SET additional_ips = :additional_ips WHERE id = :server_id";
      $updateAdditionalIpsSth = $pdo->prepare($updateAdditionalIpsSql);
      $updateAdditionalIpsSth->bindValue(':additional_ips', $newAdditionalIps);
      $updateAdditionalIpsSth->bindValue(':server_id', $serverId);
      $updateAdditionalIpsSth->execute();
    }

    // Update the subnet with the server ID and mark as allocated
    $updateSubnetSql = "UPDATE subnets SET
                        assigned_server_id = :server_id,
                        is_allocated = 1,
                        is_configured = 1
                      WHERE id = :subnet_id";
    $updateSubnetSth = $pdo->prepare($updateSubnetSql);
    $updateSubnetSth->bindValue(':subnet_id', $subnetId);
    $updateSubnetSth->bindValue(':server_id', $serverId);
    $updateSubnetSth->execute();

    // Commit transaction
    $pdo->commit();

    // Configure switch BEFORE sending response
    $switchConfigResult = ['success' => false, 'message' => 'No switch configuration attempted'];
    
    try {
      error_log("Starting immediate switch configuration for server $serverId");
      
      // Get server and switch info for configuration
      $getServerSql = "SELECT s.*, sw.switch_ip, sw.root_password, s.label
                       FROM $serverTable s
                       LEFT JOIN inventory_switches sw ON s.switch_id = sw.id
                       WHERE s.id = :server_id";
      $getServerSth = $pdo->prepare($getServerSql);
      $getServerSth->bindValue(':server_id', $serverId);
      $getServerSth->execute();
      
      if ($getServerSth->rowCount() > 0) {
        $serverInfo = $getServerSth->fetch(PDO::FETCH_ASSOC);
        
        if ($serverInfo['switch_ip'] && $serverInfo['root_password'] && $serverInfo['port1']) {
          // Resolve port name
          $serverPort = $serverInfo['port1'];
          if (is_numeric($serverPort)) {
            $getPortSql = "SELECT port_name, port_number FROM inventory_switch_ports WHERE id = :port_id AND switch_id = :switch_id";
            $getPortSth = $pdo->prepare($getPortSql);
            $getPortSth->bindValue(':port_id', $serverPort);
            $getPortSth->bindValue(':switch_id', $serverInfo['switch_id']);
            $getPortSth->execute();
            
            if ($getPortSth->rowCount() > 0) {
              $portInfo = $getPortSth->fetch(PDO::FETCH_ASSOC);
              $serverPort = !empty($portInfo['port_number']) ? $portInfo['port_number'] : $portInfo['port_name'];
            }
          }
          
          // Build list of ALL subnets assigned to server
          $allSubnets = [];
          
          // Get the updated server info (after our changes)
          $getUpdatedServerSql = "SELECT main_ip, additional_ips FROM $serverTable WHERE id = :server_id";
          $getUpdatedServerSth = $pdo->prepare($getUpdatedServerSql);
          $getUpdatedServerSth->bindValue(':server_id', $serverId);
          $getUpdatedServerSth->execute();
          $updatedServerInfo = $getUpdatedServerSth->fetch(PDO::FETCH_ASSOC);
          
          if (!empty($updatedServerInfo['main_ip'])) {
            $allSubnets[] = trim($updatedServerInfo['main_ip']);
          }
          
          if (!empty($updatedServerInfo['additional_ips'])) {
            $extra = array_filter(array_map('trim', explode(',', $updatedServerInfo['additional_ips'])));
            $allSubnets = array_merge($allSubnets, $extra);
          }
          
          if (!empty($allSubnets)) {
            // Primary gateway IP from first subnet
            $primarySubnet = $allSubnets[0];
            $configIp = $primarySubnet;
            if (strpos($primarySubnet, '/') !== false) {
              list($netIp, $pref) = explode('/', $primarySubnet);
              $configIp = long2ip(ip2long($netIp) + 1);
            }
            
            // Queue switch configuration operation
            $operationId = queueSwitchOperation('configure', $serverInfo['switch_ip'], $serverPort, [
              'password' => $serverInfo['root_password'],
              'ip_address' => $configIp,
              'subnet_info' => $allSubnets,
              'server_label' => $serverInfo['label'] ?? null
            ], $pdo);
            
            if ($operationId) {
              error_log("Switch configuration queued for server $serverId (Operation ID: $operationId)");
              
              // Process the queue immediately (before sending response)
              processSwitchQueue($pdo);
              
              // Check if the operation completed
              $checkOpSql = "SELECT status, error_message FROM automation_in_progres WHERE id = :op_id";
              $checkOpSth = $pdo->prepare($checkOpSql);
              $checkOpSth->bindValue(':op_id', $operationId);
              $checkOpSth->execute();
              
              if ($checkOpSth->rowCount() > 0) {
                $opStatus = $checkOpSth->fetch(PDO::FETCH_ASSOC);
                if ($opStatus['status'] === 'completed') {
                  $switchConfigResult = ['success' => true, 'message' => 'Switch configured successfully'];
                } else if ($opStatus['status'] === 'failed') {
                  $switchConfigResult = ['success' => false, 'message' => 'Switch configuration failed: ' . $opStatus['error_message']];
                } else {
                  $switchConfigResult = ['success' => true, 'message' => 'Switch configuration queued for processing'];
                }
              }
            } else {
              error_log("Failed to queue switch configuration for server $serverId");
              $switchConfigResult = ['success' => false, 'message' => 'Failed to queue switch configuration'];
            }
          } else {
            error_log("No subnets found for server $serverId after assignment");
            $switchConfigResult = ['success' => false, 'message' => 'No subnets to configure'];
          }
        } else {
          error_log("Switch configuration skipped for server $serverId: missing switch IP, password, or port information");
          $switchConfigResult = ['success' => true, 'message' => 'No switch configured for this server'];
        }
      } else {
        error_log("Server $serverId not found for switch configuration");
        $switchConfigResult = ['success' => false, 'message' => 'Server not found'];
      }
    } catch (Exception $e) {
      error_log("Exception during switch configuration for server $serverId: " . $e->getMessage());
      $switchConfigResult = ['success' => false, 'message' => 'Switch configuration error: ' . $e->getMessage()];
    }

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Subnet assigned successfully',
      'subnet_assigned' => $subnetCidr,
      'assignment_type' => $setAsMainIp ? 'main' : 'additional',
      'children_deleted' => count($childrenSubnets),
      'switch_config' => $switchConfigResult
    ]);

  } catch (Exception $e) {
    // Rollback transaction if it was started
    if (isset($pdo) && $pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in assign_subnet_to_server: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(400);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}


elseif($_GET['f'] == 'unallocate_subnet_server_side'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['subnet_cidr']) || empty($data['subnet_cidr'])) {
      throw new Exception('Subnet CIDR is required');
    }

    // Check if this is part of a subnet swap (skip promotion of additional IPs)
    $skipPromotion = $data['skip_promotion'] ?? false;

    error_log("Starting unallocation process for subnet {$data['subnet_cidr']}" . ($skipPromotion ? " (skip promotion - subnet swap)" : " (normal unallocation)"));
    
    // Begin transaction for database work (BEFORE sending response)
    $pdo->beginTransaction();

      // First, get the server information before updating the subnet
      $getServerSql = "SELECT s.assigned_server_id, s.subnet
                       FROM subnets s
                       WHERE s.subnet = :subnet_cidr AND s.assigned_server_id IS NOT NULL";
      $getServerSth = $pdo->prepare($getServerSql);
      $getServerSth->bindValue(':subnet_cidr', $data['subnet_cidr']);
      $getServerSth->execute();

      $serverInfo = null;
      $serverType = null;
      $serverId = null;
      if ($getServerSth->rowCount() > 0) {
        $subnetData = $getServerSth->fetch(PDO::FETCH_ASSOC);
        $serverId = $subnetData['assigned_server_id'];
        
        // Try to find server in dedicated servers first
        $checkDedicatedSql = "SELECT 'dedicated' as type FROM inventory_dedicated_servers WHERE id = :server_id";
        $checkDedicatedSth = $pdo->prepare($checkDedicatedSql);
        $checkDedicatedSth->bindValue(':server_id', $serverId);
        $checkDedicatedSth->execute();
        
        if ($checkDedicatedSth->rowCount() > 0) {
          $serverType = 'dedicated';
          $serverInfo = ['id' => $serverId, 'type' => 'dedicated'];
          error_log("Found server $serverId in dedicated servers table");
        } else {
          // Try blade servers
          $checkBladeSql = "SELECT 'blade' as type FROM blade_server_inventory WHERE id = :server_id";
          $checkBladeSth = $pdo->prepare($checkBladeSql);
          $checkBladeSth->bindValue(':server_id', $serverId);
          $checkBladeSth->execute();
          
          if ($checkBladeSth->rowCount() > 0) {
            $serverType = 'blade';
            $serverInfo = ['id' => $serverId, 'type' => 'blade'];
            error_log("Found server $serverId in blade servers table");
          }
        }
      } else {
        error_log("No server assignment found for subnet {$data['subnet_cidr']}");
      }

      // Remove the subnet from BOTH server tables for complete cleanup
      $subnetCidr = $data['subnet_cidr'];
      $serverTables = ['inventory_dedicated_servers', 'blade_server_inventory'];
      
      foreach ($serverTables as $tableName) {
        // Get all servers that have this subnet in their main_ip or additional_ips
        $getServersWithSubnetSql = "SELECT id, main_ip, additional_ips 
                                   FROM $tableName 
                                   WHERE main_ip = :subnet_cidr 
                                   OR additional_ips LIKE :subnet_pattern";
        $getServersWithSubnetSth = $pdo->prepare($getServersWithSubnetSql);
        $getServersWithSubnetSth->bindValue(':subnet_cidr', $subnetCidr);
        $getServersWithSubnetSth->bindValue(':subnet_pattern', "%$subnetCidr%");
        $getServersWithSubnetSth->execute();
        
        while ($server = $getServersWithSubnetSth->fetch(PDO::FETCH_ASSOC)) {
          $currentServerId = $server['id'];
          
          // Check if this subnet is the main IP
          if ($server['main_ip'] === $subnetCidr) {
            if ($skipPromotion) {
              // Simply clear the main_ip, keep additional_ips intact
              // This is part of a subnet swap, so don't auto-promote
              $updateServerSql = "UPDATE $tableName SET main_ip = NULL WHERE id = :server_id";
              $updateServerSth = $pdo->prepare($updateServerSql);
              $updateServerSth->bindValue(':server_id', $currentServerId);
              $updateServerSth->execute();
              error_log("Cleared main_ip after unallocating $subnetCidr on server $currentServerId (subnet swap - keeping additional_ips intact)");
            } else {
              // Regular unallocation - promote first additional IP to main_ip
              $addList = array_filter(array_map('trim', explode(',', $server['additional_ips'] ?? '')));
              $newMain = null;
              if (count($addList) > 0) {
                $newMain = array_shift($addList);
              }
              $updateServerSql = "UPDATE $tableName SET main_ip = :new_main, additional_ips = :new_add WHERE id = :server_id";
              $updateServerSth = $pdo->prepare($updateServerSql);
              $updateServerSth->bindValue(':new_main', $newMain, $newMain ? PDO::PARAM_STR : PDO::PARAM_NULL);
              $updateServerSth->bindValue(':new_add', count($addList) ? implode(',', $addList) : null, count($addList) ? PDO::PARAM_STR : PDO::PARAM_NULL);
              $updateServerSth->bindValue(':server_id', $currentServerId);
              $updateServerSth->execute();
              error_log("Promoted IP $newMain to main_ip after unallocating $subnetCidr on server $currentServerId (regular unallocation)");
            }
          }
          
          // Check if it's in additional IPs
          if (!empty($server['additional_ips'])) {
            $additionalIps = explode(',', $server['additional_ips']);
            $updatedAdditionalIps = array_filter($additionalIps, function($ip) use ($subnetCidr) {
              return trim($ip) !== $subnetCidr;
            });
            
            // Update server with modified additional IPs (only if something changed)
            if (count($updatedAdditionalIps) !== count($additionalIps)) {
              $updateServerSql = "UPDATE $tableName SET additional_ips = :additional_ips WHERE id = :server_id";
              $updateServerSth = $pdo->prepare($updateServerSql);
              $updateServerSth->bindValue(':additional_ips', !empty($updatedAdditionalIps) ? implode(',', $updatedAdditionalIps) : null,
                                         !empty($updatedAdditionalIps) ? PDO::PARAM_STR : PDO::PARAM_NULL);
              $updateServerSth->bindValue(':server_id', $currentServerId);
              $updateServerSth->execute();
              error_log("Removed additional IP $subnetCidr from $tableName server $currentServerId");
            }
          }
        }
      }

      // Update the subnet to remove ALL allocation (server assignment AND manual allocation)
      $sql = "UPDATE subnets
              SET assigned_server_id = NULL,
                  is_allocated = 0,
                  manual_alocation = NULL,
                  is_configured = 0
              WHERE subnet = :subnet_cidr";

      $sth = $pdo->prepare($sql);
      $sth->bindValue(':subnet_cidr', $data['subnet_cidr']);
      $sth->execute();

      // Commit transaction BEFORE sending response
      $pdo->commit();
      error_log("Database update completed successfully for subnet {$data['subnet_cidr']}");

      // Return success response after database is updated
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'Subnet unallocated successfully! Switch unconfiguration running in background.',
        'subnet_cidr' => $data['subnet_cidr']
      ]);

      // Flush output to client immediately after database work is done
      if (function_exists('fastcgi_finish_request')) {
        fastcgi_finish_request();
      } else {
        // Fallback for non-FPM environments
        if (ob_get_level()) {
          ob_end_flush();
        }
        flush();
      }

      // NOW handle switch unconfiguration in background (after response sent)
// NOW handle switch reconfiguration in background (after response sent)
      if ($serverInfo && $serverType) {
        try {
          error_log("Starting background switch reconfiguration for server {$serverInfo['id']} after subnet unallocation");
          
          // Get the server table name
          $serverTable = $serverType === 'dedicated' ? 'inventory_dedicated_servers' : 'blade_server_inventory';
          
          // Get the updated server info with new IP configuration
          $getUpdatedServerSql = "SELECT s.*, sw.switch_ip, sw.root_password, s.label, s.port1
                                  FROM $serverTable s
                                  LEFT JOIN inventory_switches sw ON s.switch_id = sw.id
                                  WHERE s.id = :server_id";
          $getUpdatedServerSth = $pdo->prepare($getUpdatedServerSql);
          $getUpdatedServerSth->bindValue(':server_id', $serverInfo['id']);
          $getUpdatedServerSth->execute();
          
          if ($getUpdatedServerSth->rowCount() > 0) {
            $updatedServerInfo = $getUpdatedServerSth->fetch(PDO::FETCH_ASSOC);
            
            // Build list of all subnets still assigned to the server
            $remainingSubnets = [];
            if (!empty($updatedServerInfo['main_ip'])) {
              $remainingSubnets[] = trim($updatedServerInfo['main_ip']);
            }
            if (!empty($updatedServerInfo['additional_ips'])) {
              $additionalSubnets = array_filter(array_map('trim', explode(',', $updatedServerInfo['additional_ips'])));
              $remainingSubnets = array_merge($remainingSubnets, $additionalSubnets);
            }
            
            if ($updatedServerInfo['switch_ip'] && $updatedServerInfo['root_password'] && $updatedServerInfo['port1']) {
              // Resolve port name
              $serverPort = $updatedServerInfo['port1'];
              if (is_numeric($serverPort)) {
                $getPortSql = "SELECT port_name, port_number FROM inventory_switch_ports WHERE id = :port_id AND switch_id = :switch_id";
                $getPortSth = $pdo->prepare($getPortSql);
                $getPortSth->bindValue(':port_id', $serverPort);
                $getPortSth->bindValue(':switch_id', $updatedServerInfo['switch_id']);
                $getPortSth->execute();
                
                if ($getPortSth->rowCount() > 0) {
                  $portInfo = $getPortSth->fetch(PDO::FETCH_ASSOC);
                  $serverPort = !empty($portInfo['port_number']) ? $portInfo['port_number'] : $portInfo['port_name'];
                }
              }
              
              if (!empty($remainingSubnets)) {
                // Reconfigure with remaining subnets
                error_log("Reconfiguring switch interface with " . count($remainingSubnets) . " remaining subnet(s): " . implode(', ', $remainingSubnets));
                
                // Calculate gateway IP from first subnet
                $primarySubnet = $remainingSubnets[0];
                $configIp = $primarySubnet;
                if (strpos($primarySubnet, '/') !== false) {
                  list($netIp, $pref) = explode('/', $primarySubnet);
                  $configIp = long2ip(ip2long($netIp) + 1);
                }
                
                error_log("Primary subnet after unallocation: $primarySubnet, Gateway IP: $configIp");
                
                // Queue switch reconfiguration operation to prevent race conditions
                $operationId = queueSwitchOperation('configure', $updatedServerInfo['switch_ip'], $serverPort, [
                  'password' => $updatedServerInfo['root_password'],
                  'ip_address' => $configIp,
                  'subnet_info' => $remainingSubnets,
                  'server_label' => $updatedServerInfo['label'] ?? null
                ], $pdo);
                
                if ($operationId) {
                  error_log("Background switch reconfiguration queued for server {$serverInfo['id']} (Operation ID: $operationId)");
                  $result = ['success' => true, 'queued' => true, 'operation_id' => $operationId];
                } else {
                  error_log("Failed to queue switch reconfiguration for server {$serverInfo['id']}");
                  $result = ['success' => false, 'error' => 'Failed to queue operation'];
                }
                
                if ($result && $result['success']) {
                  error_log("Background switch reconfiguration completed successfully for server {$serverInfo['id']} with subnets: " . implode(', ', $remainingSubnets));
                } else {
                  error_log("Background switch reconfiguration failed for server {$serverInfo['id']}: " . ($result['error'] ?? 'Unknown error'));
                }
              } else {
                // No subnets remaining, just unconfigure the interface
                error_log("No subnets remaining for server {$serverInfo['id']}, unconfiguring interface");
                
                // Queue switch unconfiguration operation to prevent race conditions
                $operationId = queueSwitchOperation('unconfigure', $updatedServerInfo['switch_ip'], $serverPort, [
                  'password' => $updatedServerInfo['root_password'],
                  'server_label' => $updatedServerInfo['label'] ?? null
                ], $pdo);
                
                if ($operationId) {
                  error_log("Background switch unconfiguration queued for server {$serverInfo['id']} (Operation ID: $operationId)");
                  $result = ['success' => true, 'queued' => true, 'operation_id' => $operationId];
                } else {
                  error_log("Failed to queue switch unconfiguration for server {$serverInfo['id']}");
                  $result = ['success' => false, 'error' => 'Failed to queue operation'];
                }
                
                if ($result && $result['success']) {
                  error_log("Background switch unconfiguration completed successfully for server {$serverInfo['id']}");
                } else {
                  error_log("Background switch unconfiguration failed for server {$serverInfo['id']}: " . ($result['error'] ?? 'Unknown error'));
                }
              }
            } else {
              error_log("Background switch reconfiguration skipped for server {$serverInfo['id']}: missing switch IP, password, or port information");
            }
          } else {
            error_log("Background switch reconfiguration skipped for server {$serverInfo['id']}: server not found");
          }
          
        } catch (Exception $switchError) {
          error_log("Error during background switch reconfiguration: " . $switchError->getMessage());
        }
      } else {
        error_log("No server information found for subnet {$data['subnet_cidr']}, skipping switch reconfiguration");
      }
      
      // Process any queued switch operations to ensure they run in order
      processSwitchQueue($pdo);

  } catch (Exception $e) {
    // Rollback transaction if it was started
    if (isset($pdo) && $pdo->inTransaction()) {
      $pdo->rollBack();
    }
    
    error_log("Error in unallocate_subnet_server_side: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to unallocate subnet: ' . $e->getMessage()
    ]);
  }
}

// Unassign subnet from a server
elseif($_GET['f'] == 'allocate_subnet'){
  // Debug: Log the allocation request
  $post_data = file_get_contents('php://input');
  file_put_contents('/tmp/allocate_debug.log', date('Y-m-d H:i:s') . " - Allocate endpoint called\n", FILE_APPEND);
  file_put_contents('/tmp/allocate_debug.log', "Raw POST data: $post_data\n", FILE_APPEND);
  
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode($post_data, true);
    file_put_contents('/tmp/allocate_debug.log', "Parsed data: " . json_encode($data) . "\n", FILE_APPEND);

    // Validate required fields
    if (!isset($data['subnet_id']) || empty($data['subnet_id'])) {
      throw new Exception('Subnet ID is required');
    }

    if (!isset($data['manual_alocation']) || empty($data['manual_alocation'])) {
      file_put_contents('/tmp/allocate_debug.log', "ERROR: manual_alocation missing or empty. Available keys: " . implode(', ', array_keys($data ?: [])) . "\n", FILE_APPEND);
      throw new Exception('manual_alocation is required');
    }
    
    file_put_contents('/tmp/allocate_debug.log', "manual_alocation found: '{$data['manual_alocation']}'\n", FILE_APPEND);

    // Parse subnet ID (support both numeric and SUB-xxxx formats)
    if (is_numeric($data['subnet_id'])) {
      $subnetId = (int)$data['subnet_id'];
    } else if (preg_match('/SUB-(\d+)/', $data['subnet_id'], $matches)) {
      $subnetId = (int)$matches[1];
    } else {
      throw new Exception('Invalid subnet ID format');
    }

    $manual_alocation = trim($data['manual_alocation']);

    // Begin transaction
    $pdo->beginTransaction();

    // Verify the subnet exists
    $checkSubnetSql = "SELECT * FROM subnets WHERE id = :subnet_id";
    $checkSubnetSth = $pdo->prepare($checkSubnetSql);
    $checkSubnetSth->bindValue(':subnet_id', $subnetId);
    $checkSubnetSth->execute();

    if ($checkSubnetSth->rowCount() === 0) {
      throw new Exception('Subnet not found');
    }

    $subnet = $checkSubnetSth->fetch(PDO::FETCH_ASSOC);

    // Check if subnet is already assigned or allocated
    if ($subnet['assigned_server_id']) {
      throw new Exception('Subnet is already assigned to a server');
    }

    if ($subnet['is_allocated']) {
      throw new Exception('Subnet is already allocated');
    }

    // Check if any parent subnet is already allocated or assigned
    $checkParentsSql = "SELECT p.id, p.subnet, p.is_allocated, p.assigned_server_id, p.manual_alocation
                       FROM subnet_relationships r
                       JOIN subnets p ON r.parent_subnet_id = p.id
                       WHERE r.child_subnet_id = :subnet_id
                       AND (p.is_allocated = 1 OR p.assigned_server_id IS NOT NULL)";
    $checkParentsSth = $pdo->prepare($checkParentsSql);
    $checkParentsSth->bindValue(':subnet_id', $subnetId);
    $checkParentsSth->execute();

    $allocatedParents = $checkParentsSth->fetchAll(PDO::FETCH_ASSOC);
    if (count($allocatedParents) > 0) {
      $parent = $allocatedParents[0];
      $parentStatus = $parent['is_allocated'] ? 'allocated' : 'assigned';
      $parentDesc = $parent['is_allocated'] && $parent['manual_alocation'] ?
                   " with manual_alocation: '{$parent['manual_alocation']}'" : '';
      throw new Exception("Cannot allocate this subnet: Parent subnet {$parent['subnet']} is already {$parentStatus}{$parentDesc}");
    }

    // Check if we have children and if they can be deleted
    $checkChildrenSql = "SELECT r.child_subnet_id, s.assigned_server_id, s.is_allocated, s.subnet, s.manual_alocation
                         FROM subnet_relationships r
                         JOIN subnets s ON r.child_subnet_id = s.id
                         WHERE r.parent_subnet_id = :subnet_id";
    $checkChildrenSth = $pdo->prepare($checkChildrenSql);
    $checkChildrenSth->bindValue(':subnet_id', $subnetId);
    $checkChildrenSth->execute();

    $childrenSubnets = $checkChildrenSth->fetchAll(PDO::FETCH_ASSOC);

    // If we have children, check if any are assigned or allocated
    if (count($childrenSubnets) > 0) {
        $assignedOrAllocatedChildren = array_filter($childrenSubnets, function($child) {
            return $child['assigned_server_id'] !== null || $child['is_allocated'] == 1;
        });

        if (count($assignedOrAllocatedChildren) > 0) {
            $child = reset($assignedOrAllocatedChildren); // Get the first child
            $childStatus = $child['is_allocated'] ? 'allocated' : 'assigned';
            $childDesc = $child['is_allocated'] && $child['manual_alocation'] ?
                       " with manual_alocation: '{$child['manual_alocation']}'" : '';
            throw new Exception("Cannot allocate this subnet: Child subnet {$child['subnet']} is already {$childStatus}{$childDesc}");
        }

        // Recursively delete all child subnets
        error_log("Deleting " . count($childrenSubnets) . " child subnets of parent subnet $subnetId");
        recursiveSubnetDeletion($pdo, $subnetId);
    }

    // Update the subnet to mark it as allocated
    $updateSubnetSql = "UPDATE subnets SET
                        assigned_server_id = :server_id,
                        is_allocated = 1,
                        is_configured = 1
                      WHERE id = :subnet_id";
    $updateSubnetSth = $pdo->prepare($updateSubnetSql);
    $updateSubnetSth->bindValue(':subnet_id', $subnetId);
    $updateSubnetSth->bindValue(':server_id', $serverId);
    $updateSubnetSth->execute();

    // Log the allocation
    error_log("Allocated subnet ID: $subnetId with manual_alocation: $manual_alocation");

    // Get all parent subnets to ensure they're marked as unavailable in the UI
    $getParentsSql = "WITH RECURSIVE parent_tree AS (
                        -- Start with direct parent
                        SELECT p.id, p.subnet
                        FROM subnet_relationships r
                        JOIN subnets p ON r.parent_subnet_id = p.id
                        WHERE r.child_subnet_id = :subnet_id

                        UNION ALL

                        -- Add all ancestors
                        SELECT p.id, p.subnet
                        FROM subnet_relationships r
                        JOIN subnets p ON r.parent_subnet_id = p.id
                        JOIN parent_tree pt ON r.child_subnet_id = pt.id
                      )
                      SELECT id, subnet FROM parent_tree";
    $getParentsSth = $pdo->prepare($getParentsSql);
    $getParentsSth->bindValue(':subnet_id', $subnetId);
    $getParentsSth->execute();

    $parentSubnets = $getParentsSth->fetchAll(PDO::FETCH_ASSOC);
    error_log("Found " . count($parentSubnets) . " parent subnets that will be marked as unavailable in the UI");

    // Log all parent subnets for debugging
    foreach ($parentSubnets as $parent) {
        error_log("Parent subnet: {$parent['subnet']} (ID: {$parent['id']}) will be marked as unavailable");
    }

    // Commit transaction
    $pdo->commit();

    // Get all parent subnets to ensure they're marked as unavailable in the UI
    $getParentsSql = "WITH RECURSIVE parent_tree AS (
                        -- Start with direct parent
                        SELECT p.id, p.subnet
                        FROM subnet_relationships r
                        JOIN subnets p ON r.parent_subnet_id = p.id
                        WHERE r.child_subnet_id = :subnet_id

                        UNION ALL

                        -- Add all ancestors
                        SELECT p.id, p.subnet
                        FROM subnet_relationships r
                        JOIN subnets p ON r.parent_subnet_id = p.id
                        JOIN parent_tree pt ON r.child_subnet_id = pt.id
                      )
                      SELECT id, subnet FROM parent_tree";
    $getParentsSth = $pdo->prepare($getParentsSql);
    $getParentsSth->bindValue(':subnet_id', $subnetId);
    $getParentsSth->execute();

    $parentSubnets = $getParentsSth->fetchAll(PDO::FETCH_ASSOC);
    error_log("Found " . count($parentSubnets) . " parent subnets that will be marked as unavailable in the UI");

    // Log all parent subnets for debugging
    foreach ($parentSubnets as $parent) {
        error_log("Parent subnet: {$parent['subnet']} (ID: {$parent['id']}) will be marked as unavailable");
    }

    // Return success response with parent subnet information
    header('Content-Type: application/json');

    // Format parent subnets for the response
    $formattedParents = array_map(function($parent) {
        return [
            'id' => $parent['id'],
            'subnet' => $parent['subnet'],
            'formatted_id' => 'SUB-' . str_pad($parent['id'], 4, '0', STR_PAD_LEFT)
        ];
    }, $parentSubnets);

    echo json_encode([
      'success' => true,
      'message' => 'Subnet allocated successfully',
      'subnet_id' => $subnetId,
      'manual_alocation' => $manual_alocation,
      'parent_subnets' => $formattedParents
    ]);

  } catch (Exception $e) {
    // Rollback transaction if it was started
    if (isset($pdo) && $pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in allocate_subnet: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(400);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'deallocate_subnet'){
  // Debug: Write to a file we can check
  file_put_contents('/tmp/deallocate_debug.log', date('Y-m-d H:i:s') . " - Deallocate endpoint called\n", FILE_APPEND);
  
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    file_put_contents('/tmp/deallocate_debug.log', date('Y-m-d H:i:s') . " - Admin authenticated: $admin_id\n", FILE_APPEND);

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);
    file_put_contents('/tmp/deallocate_debug.log', date('Y-m-d H:i:s') . " - POST data: " . json_encode($data) . "\n", FILE_APPEND);

    // Validate required fields
    if (!isset($data['subnet_id']) || empty($data['subnet_id'])) {
      throw new Exception('Subnet ID is required');
    }

    // Parse subnet ID (support both numeric and SUB-xxxx formats)
    if (is_numeric($data['subnet_id'])) {
      $subnetId = (int)$data['subnet_id'];
    } else if (preg_match('/SUB-(\d+)/', $data['subnet_id'], $matches)) {
      $subnetId = (int)$matches[1];
    } else {
      throw new Exception('Invalid subnet ID format');
    }

    // Begin transaction
    $pdo->beginTransaction();

    // Verify the subnet exists
    $checkSubnetSql = "SELECT * FROM subnets WHERE id = :subnet_id";
    $checkSubnetSth = $pdo->prepare($checkSubnetSql);
    $checkSubnetSth->bindValue(':subnet_id', $subnetId);
    $checkSubnetSth->execute();

    if ($checkSubnetSth->rowCount() === 0) {
      throw new Exception('Subnet not found');
    }

    $subnet = $checkSubnetSth->fetch(PDO::FETCH_ASSOC);
    file_put_contents('/tmp/deallocate_debug.log', date('Y-m-d H:i:s') . " - Subnet found: ID=$subnetId, is_allocated={$subnet['is_allocated']}, manual_alocation='{$subnet['manual_alocation']}'\n", FILE_APPEND);

    // Check if subnet is actually allocated (either is_allocated=1 OR manual_alocation has a value)
    if (!$subnet['is_allocated'] && (empty($subnet['manual_alocation']) || $subnet['manual_alocation'] === null)) {
      file_put_contents('/tmp/deallocate_debug.log', date('Y-m-d H:i:s') . " - ERROR: Subnet is not allocated\n", FILE_APPEND);
      throw new Exception('Subnet is not allocated');
    }

    // Update the subnet to mark it as not allocated
    $updateSubnetSql = "UPDATE subnets SET
                        is_allocated = 0,
                        manual_alocation = NULL,
                        is_configured = 0
                      WHERE id = :subnet_id";
    $updateSubnetSth = $pdo->prepare($updateSubnetSql);
    $updateSubnetSth->bindValue(':subnet_id', $subnetId);
    $updateSubnetSth->execute();

    // Commit transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Subnet deallocated successfully'
    ]);

  } catch (Exception $e) {
    // Rollback transaction if it was started
    if (isset($pdo) && $pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in deallocate_subnet: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(400);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'unassign_subnet'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['subnet_id']) || empty($data['subnet_id'])) {
      throw new Exception('Subnet ID is required');
    }

    // Check if ID is numeric or contains a prefix like "SUB-0001"
    if (is_numeric($data['subnet_id'])) {
      $subnetId = (int)$data['subnet_id'];
    } else if (preg_match('/SUB-(\d+)/', $data['subnet_id'], $matches)) {
      $subnetId = (int)$matches[1];
    } else {
      throw new Exception('Invalid subnet ID format');
    }

    // Debug log
    error_log("Attempting to unassign subnet ID: $subnetId");

    // Verify the subnet exists
    $checkSubnetSql = "SELECT * FROM subnets WHERE id = :subnet_id";
    $checkSubnetSth = $pdo->prepare($checkSubnetSql);
    $checkSubnetSth->bindValue(':subnet_id', $subnetId);
    $checkSubnetSth->execute();

    if ($checkSubnetSth->rowCount() === 0) {
      throw new Exception('Subnet not found');
    }

    $subnet = $checkSubnetSth->fetch(PDO::FETCH_ASSOC);

    // Begin transaction
    $pdo->beginTransaction();

    // If subnet is assigned, we need to clean up server references
    if ($subnet['assigned_server_id']) {
      $serverId = $subnet['assigned_server_id'];

      // Determine server type
      $checkDedicatedSql = "SELECT id FROM inventory_dedicated_servers WHERE id = :server_id";
      $checkDedicatedSth = $pdo->prepare($checkDedicatedSql);
      $checkDedicatedSth->bindValue(':server_id', $serverId);
      $checkDedicatedSth->execute();

      $isDedicated = $checkDedicatedSth->rowCount() > 0;
      $serverTable = $isDedicated ? 'inventory_dedicated_servers' : 'blade_server_inventory';

      // Get server information including main_ip and additional_ips
      $getServerSql = "SELECT id, main_ip, additional_ips FROM $serverTable WHERE id = :server_id";
      $getServerSth = $pdo->prepare($getServerSql);
      $getServerSth->bindValue(':server_id', $serverId);
      $getServerSth->execute();

      if ($getServerSth->rowCount() > 0) {
        $server = $getServerSth->fetch(PDO::FETCH_ASSOC);
        $subnetCidr = $subnet['subnet'];

        // Check if this subnet is used as the main IP
        $isMainIp = ($server['main_ip'] === $subnetCidr);

        // If it's the main IP, we need to find a replacement from additional IPs
        if ($isMainIp) {
          $newMainIp = null;
          $newAdditionalIps = [];

          // Parse additional IPs
          $additionalIps = !empty($server['additional_ips']) ? explode(',', $server['additional_ips']) : [];

          if (!empty($additionalIps)) {
            // The first additional IP will become the new main IP
            $newMainIp = trim($additionalIps[0]);

            // Remove the new main IP from additional IPs list
            array_shift($additionalIps);
            $newAdditionalIps = $additionalIps;
          }

          // Update server with new main IP and modified additional IPs
          $updateServerSql = "UPDATE $serverTable SET
                             main_ip = :main_ip,
                             additional_ips = :additional_ips
                             WHERE id = :server_id";
          $updateServerSth = $pdo->prepare($updateServerSql);
          $updateServerSth->bindValue(':main_ip', $newMainIp, $newMainIp ? PDO::PARAM_STR : PDO::PARAM_NULL);
          $updateServerSth->bindValue(':additional_ips', !empty($newAdditionalIps) ? implode(',', $newAdditionalIps) : null,
                                     !empty($newAdditionalIps) ? PDO::PARAM_STR : PDO::PARAM_NULL);
          $updateServerSth->bindValue(':server_id', $serverId);
          $updateServerSth->execute();
        } else {
          // This subnet is in additional IPs, so remove it from there
          $additionalIps = !empty($server['additional_ips']) ? explode(',', $server['additional_ips']) : [];
          $updatedAdditionalIps = array_filter($additionalIps, function($ip) use ($subnetCidr) {
            return trim($ip) !== $subnetCidr;
          });

          // Update server with modified additional IPs
          $updateServerSql = "UPDATE $serverTable SET
                             additional_ips = :additional_ips
                             WHERE id = :server_id";
          $updateServerSth = $pdo->prepare($updateServerSql);
          $updateServerSth->bindValue(':additional_ips', !empty($updatedAdditionalIps) ? implode(',', $updatedAdditionalIps) : null,
                                     !empty($updatedAdditionalIps) ? PDO::PARAM_STR : PDO::PARAM_NULL);
          $updateServerSth->bindValue(':server_id', $serverId);
          $updateServerSth->execute();
        }
      }
    }

    // Update the subnet to unassign it
    $updateSubnetSql = "UPDATE subnets SET
                       assigned_server_id = NULL
                     WHERE id = :subnet_id";
    $updateSubnetSth = $pdo->prepare($updateSubnetSql);
    $updateSubnetSth->bindValue(':subnet_id', $subnetId);
    $updateSubnetSth->execute();

    // Commit transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Subnet unassigned successfully'
    ]);

  } catch (Exception $e) {
    // Rollback transaction if it was started
    if (isset($pdo) && $pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in unassign_subnet: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to unassign subnet: ' . $e->getMessage()
    ]);
  }
}
// Delete a subnet
elseif($_GET['f'] == 'delete_subnet'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['subnet_id']) || empty($data['subnet_id'])) {
      throw new Exception('Subnet ID is required');
    }

    // Check if ID is numeric or contains a prefix like "SUB-0001"
    if (is_numeric($data['subnet_id'])) {
      $subnetId = (int)$data['subnet_id'];
    } else if (preg_match('/SUB-(\d+)/', $data['subnet_id'], $matches)) {
      $subnetId = (int)$matches[1];
    } else {
      throw new Exception('Invalid subnet ID format');
    }

    // Debug log
    error_log("Attempting to delete subnet ID: $subnetId");

    // Check for force delete flag
    $force = isset($data['force']) && $data['force'];

    // Verify the subnet exists
    $checkSubnetSql = "SELECT * FROM subnets WHERE id = :subnet_id";
    $checkSubnetSth = $pdo->prepare($checkSubnetSql);
    $checkSubnetSth->bindValue(':subnet_id', $subnetId, PDO::PARAM_INT);
    $checkSubnetSth->execute();

    if ($checkSubnetSth->rowCount() === 0) {
      throw new Exception('Subnet not found');
    }

    $subnet = $checkSubnetSth->fetch(PDO::FETCH_ASSOC);
    error_log("Found subnet: " . json_encode($subnet));

    // Begin transaction
    $pdo->beginTransaction();

    // Check if subnet is assigned to a server and not force delete
    if ($subnet['assigned_server_id'] && !$force) {
      $pdo->rollBack();
      throw new Exception('Cannot delete subnet: it is assigned to a server. Unassign it first or use force delete.');
    }

    // NEW: Prevent deletion of a subnet that is itself allocated / reserved unless force delete is requested
    if ((($subnet['is_allocated'] ?? 0) == 1 || !empty($subnet['manual_alocation'])) && !$force) {
      $pdo->rollBack();
      throw new Exception('Cannot delete subnet: it is allocated / reserved. Use force delete to override.');
    }

    // Check if this is a parent subnet with child subnets
    $checkChildrenSql = "SELECT COUNT(*) FROM subnet_relationships WHERE parent_subnet_id = :subnet_id";
    $checkChildrenSth = $pdo->prepare($checkChildrenSql);
    $checkChildrenSth->bindValue(':subnet_id', $subnetId, PDO::PARAM_INT);
    $checkChildrenSth->execute();

    $hasChildren = $checkChildrenSth->fetchColumn() > 0;

    // If the subnet has children, ensure NONE of the descendants are assigned / allocated / reserved / have used IPs
    if ($hasChildren) {
      $checkBusyDescendantsSql = "
        WITH RECURSIVE subnet_tree AS (
          SELECT id, assigned_server_id, is_allocated, manual_alocation FROM subnets WHERE id = :subnet_id
          UNION ALL
          SELECT s.id, s.assigned_server_id, s.is_allocated, s.manual_alocation
          FROM subnet_relationships r
          JOIN subnets s ON s.id = r.child_subnet_id
          JOIN subnet_tree st ON st.id = r.parent_subnet_id
        )
        SELECT COUNT(*) AS cnt
        FROM subnet_tree st
        LEFT JOIN ip_addresses ip ON ip.subnet_id = st.id AND ip.is_used = 1
        WHERE st.id != :subnet_id
          AND (
                st.assigned_server_id IS NOT NULL
                OR st.is_allocated = 1
                OR st.manual_alocation IS NOT NULL
                OR ip.id IS NOT NULL
              )";

      $checkBusyDescendantsSth = $pdo->prepare($checkBusyDescendantsSql);
      $checkBusyDescendantsSth->bindValue(':subnet_id', $subnetId, PDO::PARAM_INT);
      $checkBusyDescendantsSth->execute();
      $busyCount = (int)$checkBusyDescendantsSth->fetchColumn();

      if ($busyCount > 0 && !$force) {
        $pdo->rollBack();
        throw new Exception('Cannot delete subnet: One or more descendant subnets are in use (assigned / allocated / reserved) or contain used IP addresses. Use force delete to override.');
      }

      // Safe (or forced) to delete children – remove them recursively now
      recursiveSubnetDeletion($pdo, $subnetId);
    }

    // ------------------------------------------------------------------
    // Handle cases where the subnet we are deleting is currently linked
    // to a server. We need to clear the relationship and potentially
    // promote an additional subnet to the server's main IP so that the
    // server record remains valid. This block also sets a flag so that
    // we can trigger switch re-configuration later on.
    // ------------------------------------------------------------------
    if ($subnet['assigned_server_id']) {
      try {
        // Determine if the server is dedicated or blade
        $checkDedicatedSql = "SELECT id FROM inventory_dedicated_servers WHERE id = :srv";
        $checkDedicatedSt  = $pdo->prepare($checkDedicatedSql);
        $checkDedicatedSt->bindValue(':srv', $subnet['assigned_server_id'], PDO::PARAM_INT);
        $checkDedicatedSt->execute();

        $isDedicated = $checkDedicatedSt->rowCount() > 0;
        $serverTable = $isDedicated ? 'inventory_dedicated_servers' : 'blade_server_inventory';

        // Fetch the server's current IP information
        $srvInfoSt = $pdo->prepare("SELECT id, main_ip, additional_ips FROM $serverTable WHERE id = :srv");
        $srvInfoSt->bindValue(':srv', $subnet['assigned_server_id'], PDO::PARAM_INT);
        $srvInfoSt->execute();

        if ($srvInfoSt->rowCount()) {
          $srvInfo = $srvInfoSt->fetch(PDO::FETCH_ASSOC);

          // Mark for later switch re-configuration
          $serverNeedsReconfigure = true;
          $serverTypeAfterDelete  = $isDedicated ? 'dedicated' : 'blade';
          $serverIdAfterDelete    = (int)$srvInfo['id'];

          $subnetCidr = $subnet['subnet'];

          // If the subnet we are deleting was the server's main IP, promote the first additional IP (if available)
          if ($srvInfo['main_ip'] === $subnetCidr) {
            $additionalList = array_filter(array_map('trim', explode(',', $srvInfo['additional_ips'] ?? '')));

            $newMainIp = null;
            if (count($additionalList) > 0) {
              $newMainIp = array_shift($additionalList);
            }

            $updSt = $pdo->prepare("UPDATE $serverTable SET main_ip = :main, additional_ips = :add WHERE id = :srv");
            $updSt->bindValue(':main', $newMainIp, $newMainIp ? PDO::PARAM_STR : PDO::PARAM_NULL);
            $updSt->bindValue(':add', count($additionalList) ? implode(',', $additionalList) : null, count($additionalList) ? PDO::PARAM_STR : PDO::PARAM_NULL);
            $updSt->bindValue(':srv', $serverIdAfterDelete, PDO::PARAM_INT);
            $updSt->execute();
            error_log("Promoted IP {$newMainIp} to main_ip after removing {$subnetCidr} from server {$serverIdAfterDelete}");
          }
        }
      } catch (Exception $cleanupEx) {
        error_log("Warning: Error handling server IP clean-up during subnet deletion: " . $cleanupEx->getMessage());
        // Continue with deletion
      }
    }

    // Delete location mapping
    try {
      $deleteLocationSql = "DELETE FROM subnet_location_map WHERE subnet_id = :subnet_id";
      $deleteLocationSth = $pdo->prepare($deleteLocationSql);
      $deleteLocationSth->bindValue(':subnet_id', $subnetId, PDO::PARAM_INT);
      $deleteLocationSth->execute();
      error_log("Deleted location mapping: " . $deleteLocationSth->rowCount() . " records");
    } catch (Exception $e) {
      error_log("Warning: Error deleting location mapping: " . $e->getMessage());
      // Continue with deletion
    }

    // Delete all IP addresses associated with this subnet
    try {
      $deleteIpsSql = "DELETE FROM ip_addresses WHERE subnet_id = :subnet_id";
      $deleteIpsSth = $pdo->prepare($deleteIpsSql);
      $deleteIpsSth->bindValue(':subnet_id', $subnetId, PDO::PARAM_INT);
      $deleteIpsSth->execute();
      error_log("Deleted IP addresses: " . $deleteIpsSth->rowCount() . " records");
    } catch (Exception $e) {
      error_log("Warning: Error deleting IP addresses: " . $e->getMessage());
      // Continue with deletion
    }

    // Delete subnet relationships
    try {
      $deleteRelationshipsSql = "DELETE FROM subnet_relationships
                                 WHERE parent_subnet_id = :subnet_id OR child_subnet_id = :subnet_id";
      $deleteRelationshipsSth = $pdo->prepare($deleteRelationshipsSql);
      $deleteRelationshipsSth->bindValue(':subnet_id', $subnetId, PDO::PARAM_INT);
      $deleteRelationshipsSth->execute();
      error_log("Deleted relationships: " . $deleteRelationshipsSth->rowCount() . " records");
    } catch (Exception $e) {
      error_log("Warning: Error deleting relationships: " . $e->getMessage());
      // Continue with deletion
    }

    // Finally, delete the subnet itself
    $deleteSubnetSql = "DELETE FROM subnets WHERE id = :subnet_id";
    $deleteSubnetSth = $pdo->prepare($deleteSubnetSql);
    $deleteSubnetSth->bindValue(':subnet_id', $subnetId, PDO::PARAM_INT);
    $deleteSubnetSth->execute();

    if ($deleteSubnetSth->rowCount() === 0) {
      // This shouldn't happen as we verified the subnet exists, but just in case
      $pdo->rollBack();
      throw new Exception("Failed to delete subnet with ID $subnetId");
    }

    error_log("Successfully deleted subnet with ID $subnetId");

    // Commit transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Subnet deleted successfully',
      'subnet_id' => $subnetId
    ]);

    // -------------------------------------------------------------
    // Let the HTTP response finish so the client is not blocked
    // while we perform potentially lengthy switch operations.
    // -------------------------------------------------------------
    if (function_exists('fastcgi_finish_request')) {
      fastcgi_finish_request();
    } else {
      if (ob_get_level()) {
        @ob_end_flush();
      }
      @flush();
    }

    // If the subnet we deleted was providing the server's main IP,
    // we may have promoted a new subnet to main_ip. Re-configure the
    // switch interface now so the running config matches the DB.
    if (isset($serverNeedsReconfigure) && $serverNeedsReconfigure) {
      try {
        error_log("Background switch reconfiguration triggered for server {$serverIdAfterDelete}");

        $serverTableBg = $serverTypeAfterDelete === 'dedicated' ? 'inventory_dedicated_servers' : 'blade_server_inventory';

        $srvQ = $pdo->prepare("SELECT s.*, sw.switch_ip, sw.root_password, s.label, s.port1 FROM $serverTableBg s LEFT JOIN inventory_switches sw ON s.switch_id = sw.id WHERE s.id = :sid");
        $srvQ->bindValue(':sid', $serverIdAfterDelete);
        $srvQ->execute();

        if ($srvQ->rowCount()) {
          $srv = $srvQ->fetch(PDO::FETCH_ASSOC);

          // Build list of all subnets assigned to server
          $subnetsToConfigure = [];
          if (!empty($srv['main_ip'])) {
            $subnetsToConfigure[] = trim($srv['main_ip']);
          }
          if (!empty($srv['additional_ips'])) {
            $subnetsToConfigure = array_merge($subnetsToConfigure, array_filter(array_map('trim', explode(',', $srv['additional_ips']))));
          }

          if ($srv['switch_ip'] && $srv['root_password'] && $srv['port1'] && !empty($subnetsToConfigure)) {
            // Resolve port name if it is an ID
            $resolvedPort = $srv['port1'];
            if (is_numeric($resolvedPort)) {
              $pSt = $pdo->prepare("SELECT port_name, port_number FROM inventory_switch_ports WHERE id = :pid AND switch_id = :sid");
              $pSt->bindValue(':pid', $resolvedPort);
              $pSt->bindValue(':sid', $srv['switch_id']);
              $pSt->execute();
              if ($pSt->rowCount()) {
                $pInfo = $pSt->fetch(PDO::FETCH_ASSOC);
                $resolvedPort = !empty($pInfo['port_number']) ? $pInfo['port_number'] : $pInfo['port_name'];
              }
            }

            // Calculate gateway IP from first subnet
            $firstSubnet = $subnetsToConfigure[0];
            $gatewayIp = $firstSubnet;
            if (strpos($firstSubnet, '/') !== false) {
              list($netIp, $pref) = explode('/', $firstSubnet);
              $gatewayIp = long2ip(ip2long($netIp) + 1);
            }

            // Queue switch configuration operation to prevent race conditions
            $operationId = queueSwitchOperation('configure', $srv['switch_ip'], $resolvedPort, [
              'password' => $srv['root_password'],
              'ip_address' => $gatewayIp,
              'subnet_info' => $subnetsToConfigure,
              'server_label' => $srv['label'] ?? null
            ]);
            
            if ($operationId) {
              error_log("Switch reconfiguration queued for server {$serverIdAfterDelete} (Operation ID: $operationId)");
              $cfgResult = ['success' => true, 'queued' => true, 'operation_id' => $operationId];
            } else {
              error_log("Failed to queue switch reconfiguration for server {$serverIdAfterDelete}");
              $cfgResult = ['success' => false, 'error' => 'Failed to queue operation'];
            }

            if ($cfgResult && $cfgResult['success']) {
              error_log("Switch reconfiguration completed successfully for server {$serverIdAfterDelete}");
            } else {
              error_log("Switch reconfiguration failed for server {$serverIdAfterDelete}: " . ($cfgResult['error'] ?? 'unknown error'));
            }
          } else {
            error_log("Switch reconfiguration skipped for server {$serverIdAfterDelete}: insufficient data or no subnets");
          }
        }
      } catch (Exception $reconfEx) {
        error_log("Exception during background switch reconfiguration: " . $reconfEx->getMessage());
      }
      
      // Process any queued switch operations to ensure they run in order
      processSwitchQueue($pdo);
    }

  } catch (Exception $e) {
    // Rollback transaction if it was started
    if (isset($pdo) && $pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in delete_subnet: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to delete subnet: ' . $e->getMessage()
    ]);
  }
}


// Divide subnet into smaller subnets
// Divide subnet into smaller subnets
elseif($_GET['f'] == 'divide_subnet'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['subnet_id']) || empty($data['subnet_id'])) {
      throw new Exception('Parent subnet ID is required');
    }

    if (!isset($data['new_subnet_size']) || empty($data['new_subnet_size'])) {
      throw new Exception('New subnet size is required');
    }

    // Parse subnet ID (support both numeric and SUB-xxxx formats)
    if (is_numeric($data['subnet_id'])) {
      $subnetId = (int)$data['subnet_id'];
    } else if (preg_match('/SUB-(\d+)/', $data['subnet_id'], $matches)) {
      $subnetId = (int)$matches[1];
    } else {
      throw new Exception('Invalid subnet ID format');
    }

    $newSubnetSize = (int)$data['new_subnet_size'];

    // Verify the subnet exists
    $checkSubnetSql = "SELECT * FROM subnets WHERE id = :subnet_id";
    $checkSubnetSth = $pdo->prepare($checkSubnetSql);
    $checkSubnetSth->bindValue(':subnet_id', $subnetId);
    $checkSubnetSth->execute();

    if ($checkSubnetSth->rowCount() === 0) {
      throw new Exception('Parent subnet not found');
    }

    $parentSubnet = $checkSubnetSth->fetch(PDO::FETCH_ASSOC);

    // Check existing direct children (size-matching ones)
    $existingChildrenSql = "SELECT s.subnet FROM subnets s
                            JOIN subnet_relationships r ON r.child_subnet_id = s.id
                            WHERE r.parent_subnet_id = :subnet_id";
    $existingChildrenStmt = $pdo->prepare($existingChildrenSql);
    $existingChildrenStmt->bindValue(':subnet_id', $subnetId);
    $existingChildrenStmt->execute();
    $existingChildCidrsArr = $existingChildrenStmt->fetchAll(PDO::FETCH_COLUMN, 0);

    $childCount = count($existingChildCidrsArr);

    if ($childCount >= pow(2, (int)$newSubnetSize - (int)$parentSubnetSize)) {
      throw new Exception('Cannot divide this subnet: it already has the maximum number of child subnets (' . $childCount . ').');
    }

    // Check if parent subnet is assigned
    if ($parentSubnet['assigned_server_id']) {
      throw new Exception('Cannot divide subnet: it is assigned to a server. Unassign it first.');
    }

    // Validate the new subnet size
    $parentSubnetSize = $parentSubnet['subnet_size'];
    if ($newSubnetSize <= $parentSubnetSize) {
      throw new Exception('New subnet size must be larger than parent subnet size');
    }

    if ($newSubnetSize > 31) { // Commonly /32 are not used for networks
      throw new Exception('New subnet size cannot be larger than /31');
    }

    // Calculate the number of new subnets
    $numNewSubnets = pow(2, $newSubnetSize - $parentSubnetSize);

    // Check if the number of subnets is reasonable
    if ($numNewSubnets > 256) {
      throw new Exception('Dividing this subnet would create too many subnets (' . $numNewSubnets . ')');
    }

    // Get parent subnet network address
    $parentNetworkAddressStr = explode('/', $parentSubnet['subnet'])[0];
    $parentNetworkAddress = ip2long($parentNetworkAddressStr);

    // Calculate the size of each new subnet
    $newSubnetAddressIncrement = pow(2, 32 - $newSubnetSize);

    // IMPORTANT: Do not use transactions if they're causing issues
    // Instead, we'll track created subnet IDs and handle errors manually

    // Create a table for tracking parent-child subnet relationships if it doesn't exist
    $pdo->exec("CREATE TABLE IF NOT EXISTS subnet_relationships (
      id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
      parent_subnet_id INT NOT NULL,
      child_subnet_id INT NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE KEY unique_relationship (parent_subnet_id, child_subnet_id),
      KEY idx_parent (parent_subnet_id),
      KEY idx_child (child_subnet_id),
      CONSTRAINT fk_parent_subnet FOREIGN KEY (parent_subnet_id) REFERENCES subnets(id) ON DELETE CASCADE,
      CONSTRAINT fk_child_subnet FOREIGN KEY (child_subnet_id) REFERENCES subnets(id) ON DELETE CASCADE
    )");

    // Create the new subnets
    $newSubnetIds = [];
    $errorOccurred = false;
    $errorMessage = '';

    for ($i = 0; $i < $numNewSubnets; $i++) {
      if ($errorOccurred) break;

      $newNetworkAddress = $parentNetworkAddress + ($i * $newSubnetAddressIncrement);
      $newNetworkAddressStr = long2ip($newNetworkAddress);
      $newSubnet = $newNetworkAddressStr . '/' . $newSubnetSize;

      // Skip if subnet already exists in DB (as child of this parent or elsewhere)
      if (in_array($newSubnet, $existingChildCidrsArr, true)) {
        // Already present, just ensure relationship exists
        try {
          // First get subnet id
          $existingSubSt = $pdo->prepare("SELECT id FROM subnets WHERE subnet = :sn LIMIT 1");
          $existingSubSt->bindValue(':sn', $newSubnet);
          $existingSubSt->execute();
          if ($existingSubSt->rowCount()) {
            $existingId = (int)$existingSubSt->fetchColumn();
            // Insert relationship if missing
            $relSt = $pdo->prepare("INSERT IGNORE INTO subnet_relationships (parent_subnet_id, child_subnet_id) VALUES (:p, :c)");
            $relSt->bindValue(':p', $subnetId);
            $relSt->bindValue(':c', $existingId);
            $relSt->execute();
          }
        } catch (Exception $relEx) {
          error_log('Error ensuring relationship for existing child ' . $newSubnet . ': ' . $relEx->getMessage());
        }
        continue; // move to next iteration
      }

      try {
        // Insert the new subnet
        $insertSubnetSql = "INSERT INTO subnets (
                            subnet,
                            subnet_size,
                            switch_id,
                            vlan,
                            is_root_subnet,
                            is_customer_subnet,
                            is_managmenet_subnet,
                            is_transit_subnet,
                            is_configured,
                            is_public,
                            asn
                          ) VALUES (
                            :subnet,
                            :subnet_size,
                            :switch_id,
                            :vlan,
                            0,
                            :is_customer_subnet,
                            :is_managmenet_subnet,
                            :is_transit_subnet,
                            0,
                            :is_public,
                            :asn
                          )";

        $insertSubnetSth = $pdo->prepare($insertSubnetSql);
        $insertSubnetSth->bindValue(':subnet', $newSubnet);
        $insertSubnetSth->bindValue(':subnet_size', $newSubnetSize);
        $insertSubnetSth->bindValue(':switch_id', $parentSubnet['switch_id'],
                                  $parentSubnet['switch_id'] ? PDO::PARAM_INT : PDO::PARAM_NULL);
        $insertSubnetSth->bindValue(':vlan', $parentSubnet['vlan']);
        $insertSubnetSth->bindValue(':is_customer_subnet', $parentSubnet['is_customer_subnet']);
        $insertSubnetSth->bindValue(':is_managmenet_subnet', $parentSubnet['is_managmenet_subnet'] ?? 0);
        $insertSubnetSth->bindValue(':is_transit_subnet', $parentSubnet['is_transit_subnet'] ?? 0);
        $insertSubnetSth->bindValue(':is_public', $parentSubnet['is_public'] ?? 0);
        $insertSubnetSth->bindValue(':asn', $parentSubnet['asn']);
        $insertSubnetSth->execute();

        $newSubnetId = $pdo->lastInsertId();
        $newSubnetIds[] = $newSubnetId;

        // Create the parent-child relationship
        $insertRelationshipSql = "INSERT INTO subnet_relationships (parent_subnet_id, child_subnet_id)
                                 VALUES (:parent_id, :child_id)";
        $insertRelationshipSth = $pdo->prepare($insertRelationshipSql);
        $insertRelationshipSth->bindValue(':parent_id', $subnetId);
        $insertRelationshipSth->bindValue(':child_id', $newSubnetId);
        $insertRelationshipSth->execute();

        // Copy location information if any
        $copyLocationSql = "INSERT INTO subnet_location_map (subnet_id, city_id, country_id, datacenter, rack_id)
                            SELECT :new_subnet_id, city_id, country_id, datacenter, rack_id
                            FROM subnet_location_map
                            WHERE subnet_id = :parent_subnet_id";
        $copyLocationSth = $pdo->prepare($copyLocationSql);
        $copyLocationSth->bindValue(':new_subnet_id', $newSubnetId);
        $copyLocationSth->bindValue(':parent_subnet_id', $subnetId);
        $copyLocationSth->execute();

        // Generate IP addresses if requested
        if (isset($data['generate_ips']) && $data['generate_ips']) {
          // Calculate number of IPs based on subnet size (limited to 1000 for large subnets)
          $ipCount = min(pow(2, (32 - $newSubnetSize)) - 2, 1000);

          if ($ipCount > 0) {
            // Insert IPs in batches to avoid overwhelming the database
            $batchSize = 100;
            for ($batch = 0; $batch < ceil($ipCount / $batchSize); $batch++) {
              $start = $batch * $batchSize + 1;
              $end = min(($batch + 1) * $batchSize, $ipCount);

              $ipSql = "INSERT INTO ip_addresses (subnet_id, ip_address, is_used) VALUES ";
              $ipValues = [];
              $ipParams = [];

              for ($j = $start; $j <= $end; $j++) {
                $currentIpLong = $newNetworkAddress + $j;
                $currentIp = long2ip($currentIpLong);

                $paramName = ':ip' . ($j - $start + 1);
                $ipValues[] = "($newSubnetId, $paramName, 0)";
                $ipParams[$paramName] = $currentIp;
              }

              if (!empty($ipValues)) {
                $ipSql .= implode(', ', $ipValues);
                $ipSth = $pdo->prepare($ipSql);

                foreach ($ipParams as $param => $value) {
                  $ipSth->bindValue($param, $value);
                }

                $ipSth->execute();
              }
            }
          }
        }
      } catch (Exception $subnetError) {
        $errorOccurred = true;
        $errorMessage = $subnetError->getMessage();
        error_log("Error creating subnet $i: " . $errorMessage);

        // Continue with partial success - we won't delete what's been created
      }
    }

    // Return response based on results
    header('Content-Type: application/json');

    if ($errorOccurred && count($newSubnetIds) == 0) {
      // Complete failure
      http_response_code(500);
      echo json_encode([
        'success' => false,
        'error' => 'Failed to divide subnet: ' . $errorMessage
      ]);
    } else if ($errorOccurred) {
      // Partial success
      echo json_encode([
        'success' => true,
        'partial' => true,
        'message' => 'Subnet partially divided. ' . count($newSubnetIds) . ' of ' . $numNewSubnets . ' subnets created. Error: ' . $errorMessage,
        'new_subnet_ids' => $newSubnetIds,
        'parent_subnet_id' => $subnetId
      ]);
    } else {
      // Complete success
      echo json_encode([
        'success' => true,
        'message' => 'Subnet divided successfully into ' . $numNewSubnets . ' smaller subnets',
        'new_subnet_ids' => $newSubnetIds,
        'parent_subnet_id' => $subnetId
      ]);
    }

  } catch (Exception $e) {
    error_log("Error in divide_subnet: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to divide subnet: ' . $e->getMessage()
    ]);
  }
}

// API endpoint to generate IPs for an existing subnet
elseif($_GET['f'] == 'generate_subnet_ips'){
  try {
    // Enable detailed error logging
    error_log("Starting generate_subnet_ips endpoint");

    // Authenticate admin
    $admin_id = auth_admin();
    error_log("Admin authenticated: $admin_id");

    // Get data
    $rawData = file_get_contents('php://input');
    error_log("Raw request data: $rawData");

    $data = json_decode($rawData, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
      error_log("JSON decode error: " . json_last_error_msg());
      throw new Exception('Invalid JSON data: ' . json_last_error_msg());
    }

    error_log("Parsed request data: " . print_r($data, true));

    // Check if we should exclude the gateway
    $excludeGateway = isset($data['exclude_gateway']) && $data['exclude_gateway'] === true;
    error_log("Exclude gateway: " . ($excludeGateway ? 'true' : 'false'));

    // Validate required fields
    if (!isset($data['subnet_id']) || empty($data['subnet_id'])) {
      throw new Exception('Subnet ID is required');
    }

    // Extract subnet ID (handle SUB-xxxx format if needed)
    $subnetId = $data['subnet_id'];
    if (is_string($subnetId) && strpos($subnetId, 'SUB-') === 0) {
      $subnetId = (int)str_replace('SUB-', '', $subnetId);
    }
    error_log("Processing subnet ID: $subnetId");

    // Get subnet information
    $subnetSql = "SELECT subnet, subnet_size, manual_alocation FROM subnets WHERE id = :subnet_id";
    $subnetSth = $pdo->prepare($subnetSql);
    $subnetSth->bindValue(':subnet_id', $subnetId, PDO::PARAM_INT);
    $subnetSth->execute();

    if ($subnetSth->rowCount() === 0) {
      error_log("Subnet not found with ID: $subnetId");
      throw new Exception('Subnet not found');
    }

    $subnet = $subnetSth->fetch(PDO::FETCH_ASSOC);
    error_log("Subnet data: " . print_r($subnet, true));

    // Calculate the gateway IP (typically the first host IP in the subnet)
    $gatewayLong = null;
    if ($excludeGateway) {
      $networkParts = explode('/', $subnet['subnet']);
      if (count($networkParts) == 2) {
        $networkIpLong = ip2long($networkParts[0]);
        $subnetMask = (int)$networkParts[1];
        if ($networkIpLong !== false) {
          // Special case for /32 subnets: gateway is the same as the network address
          if ($subnetMask == 32) {
            $gatewayLong = $networkIpLong; // Gateway is the same as network address for /32
            $gatewayIp = long2ip($gatewayLong);
            error_log("Calculated gateway IP for /32 subnet: $gatewayIp (long: $gatewayLong)");
          } else {
            $gatewayLong = $networkIpLong + 1; // Gateway is typically network + 1 for other subnets
            $gatewayIp = long2ip($gatewayLong);
            error_log("Calculated gateway IP: $gatewayIp (long: $gatewayLong)");
          }
        } else {
          error_log("Failed to convert network IP to long: {$networkParts[0]}");
        }
      } else {
        error_log("Invalid subnet format for gateway calculation: {$subnet['subnet']}");
      }
    } else {
      error_log("exclude_gateway is false, not calculating gateway");
    }

    $subnetCidr = $subnet['subnet'];
    $subnetSize = $subnet['subnet_size'];

    // Check if this is an IPv4 subnet
    if (!strpos($subnetCidr, '.')) {
      error_log("Not an IPv4 subnet: $subnetCidr");
      throw new Exception('IP generation is only supported for IPv4 subnets');
    }

    // Check if IPs already exist for this subnet
    $checkIpsSql = "SELECT COUNT(*) FROM ip_addresses WHERE subnet_id = :subnet_id";
    $checkIpsSth = $pdo->prepare($checkIpsSql);
    $checkIpsSth->bindValue(':subnet_id', $subnetId, PDO::PARAM_INT);
    $checkIpsSth->execute();

    $existingIpsCount = $checkIpsSth->fetchColumn();
    error_log("Existing IP count: $existingIpsCount");

    // If IPs already exist, ask for confirmation to regenerate
    if ($existingIpsCount > 0 && !($data['force'] ?? false)) {
      error_log("Asking for confirmation to regenerate IPs");
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'This subnet already has ' . $existingIpsCount . ' IP addresses. Use force=true to regenerate.',
        'existing_ips' => $existingIpsCount,
        'needs_confirmation' => true
      ]);
      exit;
    }

    // Begin transaction
    error_log("Beginning transaction");
    $pdo->beginTransaction();

    // If we're regenerating, delete existing IPs first
    if ($existingIpsCount > 0) {
      error_log("Deleting existing IPs");
      $deleteIpsSql = "DELETE FROM ip_addresses WHERE subnet_id = :subnet_id";
      $deleteIpsSth = $pdo->prepare($deleteIpsSql);
      $deleteIpsSth->bindValue(':subnet_id', $subnetId, PDO::PARAM_INT);
      $deleteIpsSth->execute();
      error_log("Deleted existing IPs: " . $deleteIpsSth->rowCount());
    }

    // Generate IP addresses
    error_log("Generating IP addresses for subnet: $subnetCidr");
    $networkParts = explode('/', $subnetCidr);
    if (count($networkParts) != 2) {
      error_log("Invalid CIDR format: $subnetCidr");
      throw new Exception("Invalid CIDR format: $subnetCidr");
    }

    $ipLong = ip2long($networkParts[0]);
    if ($ipLong === false) {
      error_log("Invalid IP address: {$networkParts[0]}");
      throw new Exception("Invalid IP address: {$networkParts[0]}");
    }
    error_log("Network address long value: $ipLong");

    // Calculate number of IPs based on subnet size
    $ipCount = pow(2, (32 - $subnetSize)) - 2; // Subtract network and broadcast addresses
    error_log("Calculated IP count: $ipCount");

    // For big subnets, limit the number of IPs we create
    if ($ipCount > 1000) {
      error_log("Limiting IP count to 1000 (was $ipCount)");
      $ipCount = 1000; // Cap at 1000 IPs
    }

    // Insert IPs in batches to avoid overwhelming the database
    $batchSize = 100;
    $totalInserted = 0;
    $totalBatches = ceil($ipCount / $batchSize);
    error_log("Will insert IPs in $totalBatches batches of $batchSize");

    for ($batch = 0; $batch < $totalBatches; $batch++) {
      $start = $batch * $batchSize + 1;
      $end = min(($batch + 1) * $batchSize, $ipCount);
      error_log("Processing batch $batch: IPs $start to $end");

      $ipSql = "INSERT INTO ip_addresses (subnet_id, ip_address, is_used) VALUES ";
      $ipValues = [];
      $ipParams = [];

      for ($i = $start; $i <= $end; $i++) {
        $currentIpLong = $ipLong + $i;
        $currentIp = long2ip($currentIpLong);

        // Skip the gateway IP if exclude_gateway is true
        if ($excludeGateway && $gatewayLong && $currentIpLong === $gatewayLong) {
          error_log("Skipping gateway IP: $currentIp");
          continue;
        }

        $paramName = ':ip' . ($i - $start + 1);
        $ipValues[] = "($subnetId, $paramName, 0)";
        $ipParams[$paramName] = $currentIp;
      }

      if (!empty($ipValues)) {
        $ipSql .= implode(', ', $ipValues);
        $ipSth = $pdo->prepare($ipSql);

        foreach ($ipParams as $param => $value) {
          $ipSth->bindValue($param, $value);
        }

        error_log("Executing batch insert for " . count($ipValues) . " IPs");
        $ipSth->execute();
        $insertedInBatch = ($end - $start + 1);
        $totalInserted += $insertedInBatch;
        error_log("Inserted $insertedInBatch IPs in this batch, total: $totalInserted");
      }
    }

    // Commit transaction
    error_log("Committing transaction");
    $pdo->commit();

    // Return success response
    error_log("Returning success response, total IPs inserted: $totalInserted");
    header('Content-Type: application/json');
    $response = [
      'success' => true,
      'message' => 'Successfully generated ' . $totalInserted . ' IP addresses for subnet ' . $subnetCidr,
      'subnet_id' => $subnetId,
      'ip_count' => $totalInserted,
      'regenerated' => $existingIpsCount > 0
    ];
    error_log("Response data: " . print_r($response, true));
    echo json_encode($response);

  } catch (Exception $e) {
    // Rollback transaction if active
    if ($pdo->inTransaction()) {
      error_log("Rolling back transaction due to error");
      $pdo->rollBack();
    }

    $errorMessage = $e->getMessage();
    $errorTrace = $e->getTraceAsString();
    error_log("Error in generate_subnet_ips: $errorMessage");
    error_log("Stack trace: $errorTrace");

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    $errorResponse = [
      'success' => false,
      'error' => 'Failed to generate IP addresses: ' . $errorMessage
    ];
    error_log("Error response: " . print_r($errorResponse, true));
    echo json_encode($errorResponse);
  }
}

// API endpoint to get IP addresses for a subnet
elseif($_GET['f'] == 'get_subnet_ips'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['subnet_id']) || empty($data['subnet_id'])) {
      throw new Exception('Subnet ID is required');
    }

    // Extract subnet ID (handle SUB-xxxx format if needed)
    $subnetId = $data['subnet_id'];
    if (is_string($subnetId) && strpos($subnetId, 'SUB-') === 0) {
      $subnetId = (int)str_replace('SUB-', '', $subnetId);
    }

    // Get subnet information
    $subnetSql = "SELECT subnet, subnet_size FROM subnets WHERE id = :subnet_id";
    $subnetSth = $pdo->prepare($subnetSql);
    $subnetSth->bindValue(':subnet_id', $subnetId, PDO::PARAM_INT);
    $subnetSth->execute();

    if ($subnetSth->rowCount() === 0) {
      throw new Exception('Subnet not found');
    }

    $subnet = $subnetSth->fetch(PDO::FETCH_ASSOC);

    // Get IP addresses for this subnet
    $ipSql = "SELECT ip_address, is_used, assigned_to, notes FROM ip_addresses WHERE subnet_id = :subnet_id ORDER BY INET_ATON(ip_address) ASC";
    $ipSth = $pdo->prepare($ipSql);
    $ipSth->bindValue(':subnet_id', $subnetId, PDO::PARAM_INT);
    $ipSth->execute();

    error_log("Fetching IPs for subnet ID: $subnetId, found: " . $ipSth->rowCount() . " IPs");

    $ips = $ipSth->fetchAll(PDO::FETCH_ASSOC);

    // Return IP addresses as JSON
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'subnet_id' => $subnetId,
      'subnet_cidr' => $subnet['subnet'],
      'subnet_size' => $subnet['subnet_size'],
      'ip_count' => count($ips),
      'ips' => $ips
    ]);

  } catch (Exception $e) {
    error_log("Error in get_subnet_ips: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to get IP addresses: ' . $e->getMessage()
    ]);
  }
}

// API endpoint to allocate an IP address
elseif($_GET['f'] == 'allocate_ip'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['ip_address']) || empty($data['ip_address'])) {
      throw new Exception('IP address is required');
    }

    if (!isset($data['subnet_id']) || empty($data['subnet_id'])) {
      throw new Exception('Subnet ID is required');
    }

    if (!isset($data['manual_alocation']) || empty($data['manual_alocation'])) {
      throw new Exception('manual_alocation is required');
    }

    // Parse subnet ID (support both numeric and SUB-xxxx formats)
    if (is_numeric($data['subnet_id'])) {
      $subnetId = (int)$data['subnet_id'];
    } else if (preg_match('/SUB-(\d+)/', $data['subnet_id'], $matches)) {
      $subnetId = (int)$matches[1];
    } else {
      throw new Exception('Invalid subnet ID format');
    }

    // Begin transaction
    $pdo->beginTransaction();

    // Check if IP exists and belongs to the subnet
    $checkIpSql = "SELECT id, is_used, assigned_to, notes FROM ip_addresses
                  WHERE ip_address = :ip_address AND subnet_id = :subnet_id";
    $checkIpSth = $pdo->prepare($checkIpSql);
    $checkIpSth->bindValue(':ip_address', $data['ip_address']);
    $checkIpSth->bindValue(':subnet_id', $subnetId);
    $checkIpSth->execute();

    if ($checkIpSth->rowCount() === 0) {
      // If the IP does not exist in ip_addresses, create it on the fly so we can mark it as used
      error_log("IP {$data['ip_address']} not found in subnet {$subnetId}. Creating new record in ip_addresses.");

      // Determine device_type just once (we already build this few lines later, wrap in helper)
      $deviceTypeParam = null;
      if(isset($data['for_server_ipmi']) && $data['for_server_ipmi'] === true){
          $deviceTypeParam = !empty($data['server_type']) ? $data['server_type'] : 'server_ipmi';
      }elseif(isset($data['for_switch']) && $data['for_switch'] === true){
          $deviceTypeParam = 'switch';
      }

      $insertIpSql = "INSERT INTO ip_addresses (subnet_id, ip_address, is_used, assigned_to, device_type, notes)
                      VALUES (:subnet_id, :ip_address, 1, :assigned_to, :device_type, :notes)";
      $insertIpSth = $pdo->prepare($insertIpSql);
      $insertIpSth->bindValue(':subnet_id', $subnetId);
      $insertIpSth->bindValue(':ip_address', $data['ip_address']);
      $insertIpSth->bindValue(':assigned_to', $data['manual_alocation']);
      if($deviceTypeParam === null){
          $insertIpSth->bindValue(':device_type', null, PDO::PARAM_NULL);
      }else{
          $insertIpSth->bindValue(':device_type', $deviceTypeParam);
      }
      $insertIpSth->bindValue(':notes', $data['notes'] ?? null);
      $insertIpSth->execute();

      // Fetch the row as if it existed to keep later logic unchanged
      $ip = ['is_used' => 1];
    } else {
      $ip = $checkIpSth->fetch(PDO::FETCH_ASSOC);

      // Check if IP is already used
      if ($ip['is_used'] == 1) {
        throw new Exception('IP address is already in use');
      }
    }

    // Check if we need to deallocate an old IP
    if (isset($data['deallocate_old_ip']) && $data['deallocate_old_ip'] === true && isset($data['old_ip_address'])) {
      $oldIpAddress = $data['old_ip_address'];
      error_log("Request to deallocate old IP: $oldIpAddress during allocation");

      // Find the old IP in the database
      $findOldIpSql = "SELECT * FROM ip_addresses WHERE ip_address = :ip_address";
      $findOldIpSth = $pdo->prepare($findOldIpSql);
      $findOldIpSth->bindValue(':ip_address', $oldIpAddress);
      $findOldIpSth->execute();

      if ($findOldIpSth->rowCount() > 0) {
        $oldIpData = $findOldIpSth->fetch(PDO::FETCH_ASSOC);
        $oldSubnetId = $oldIpData['subnet_id'];

        error_log("Found old IP $oldIpAddress in subnet ID: $oldSubnetId, deallocating it");

        // Update the old IP to mark it as deallocated
        $updateOldIpSql = "UPDATE ip_addresses
                          SET is_used = 0,
                              assigned_to = NULL,
                              notes = NULL,
                              device_type = NULL
                          WHERE ip_address = :ip_address";
        $updateOldIpSth = $pdo->prepare($updateOldIpSql);
        $updateOldIpSth->bindValue(':ip_address', $oldIpAddress);
        $updateOldIpSth->execute();

        error_log("Deallocated old IP $oldIpAddress");

        // Check if this IP is used as a switch IP
        $checkSwitchSql = "SELECT id FROM inventory_switches WHERE switch_ip = :ip_address";
        $checkSwitchSth = $pdo->prepare($checkSwitchSql);
        $checkSwitchSth->bindValue(':ip_address', $oldIpAddress);
        $checkSwitchSth->execute();

        if ($checkSwitchSth->rowCount() > 0) {
          $switchId = $checkSwitchSth->fetch(PDO::FETCH_ASSOC)['id'];

          // Update the switch to remove the IP address
          $updateSwitchSql = "UPDATE inventory_switches SET switch_ip = NULL WHERE id = :switch_id";
          $updateSwitchSth = $pdo->prepare($updateSwitchSql);
          $updateSwitchSth->bindValue(':switch_id', $switchId);
          $updateSwitchSth->execute();

          error_log("Removed IP address $oldIpAddress from switch ID $switchId");
        }
      } else {
        error_log("Old IP $oldIpAddress not found in database, nothing to deallocate");
      }
    }

    // Update the IP address to mark it as allocated
    $updateIpSql = "UPDATE ip_addresses
                    SET is_used = 1,
                        assigned_to = :manual_alocation,
                        notes = :notes,
                        device_type = :device_type
                    WHERE ip_address = :ip_address AND subnet_id = :subnet_id";
    $updateIpSth = $pdo->prepare($updateIpSql);
    $updateIpSth->bindValue(':manual_alocation', $data['manual_alocation']);
    $updateIpSth->bindValue(':notes', $data['notes'] ?? null);
    // Determine device_type based on context (server IPMI or switch)
    $deviceTypeParam = null;
    if(isset($data['for_server_ipmi']) && $data['for_server_ipmi'] === true){
        // Use provided server_type if available (blade/dedicated) else fall back to 'server_ipmi'
        $deviceTypeParam = !empty($data['server_type']) ? $data['server_type'] : 'server_ipmi';
    }elseif(isset($data['for_switch']) && $data['for_switch'] === true){
        $deviceTypeParam = 'switch';
    }
    if($deviceTypeParam === null){
        $updateIpSth->bindValue(':device_type', null, PDO::PARAM_NULL);
    }else{
        $updateIpSth->bindValue(':device_type', $deviceTypeParam);
    }
    $updateIpSth->bindValue(':ip_address', $data['ip_address']);
    $updateIpSth->bindValue(':subnet_id', $subnetId);
    $updateIpSth->execute();

    // Get all parent subnets to ensure they're marked as unavailable in the UI
    $getParentsSql = "WITH RECURSIVE parent_tree AS (
                        -- Start with direct parent
                        SELECT p.id, p.subnet
                        FROM subnet_relationships r
                        JOIN subnets p ON r.parent_subnet_id = p.id
                        WHERE r.child_subnet_id = :subnet_id

                        UNION ALL

                        -- Add all ancestors
                        SELECT p.id, p.subnet
                        FROM subnet_relationships r
                        JOIN subnets p ON r.parent_subnet_id = p.id
                        JOIN parent_tree pt ON r.child_subnet_id = pt.id
                      )
                      SELECT id, subnet FROM parent_tree";
    $getParentsSth = $pdo->prepare($getParentsSql);
    $getParentsSth->bindValue(':subnet_id', $subnetId);
    $getParentsSth->execute();

    $parentSubnets = $getParentsSth->fetchAll(PDO::FETCH_ASSOC);
    error_log("Found " . count($parentSubnets) . " parent subnets that will be marked as unavailable in the UI");

    // Log all parent subnets for debugging
    foreach ($parentSubnets as $parent) {
        error_log("Parent subnet: {$parent['subnet']} (ID: {$parent['id']}) will be marked as unavailable");
    }

    // If this is for a server's IPMI, update the server record
    if (isset($data['for_server_ipmi']) && $data['for_server_ipmi'] === true) {
      // Validate server data
      if (!isset($data['server_id']) || empty($data['server_id'])) {
        throw new Exception('Server ID is required for IPMI allocation');
      }

      if (!isset($data['server_type']) || empty($data['server_type'])) {
        throw new Exception('Server type is required for IPMI allocation');
      }

      // Determine which table to update based on server type
      $serverTable = $data['server_type'] === 'dedicated' ? 'inventory_dedicated_servers' : 'blade_server_inventory';

      // Check if the server already has an IPMI IP assigned
      $checkIpmiSql = "SELECT ipmi FROM $serverTable WHERE id = :server_id";
      $checkIpmiSth = $pdo->prepare($checkIpmiSql);
      $checkIpmiSth->bindValue(':server_id', $data['server_id']);
      $checkIpmiSth->execute();

      if ($checkIpmiSth->rowCount() > 0) {
        $currentIpmi = $checkIpmiSth->fetch(PDO::FETCH_ASSOC)['ipmi'];

        // If the server already has an IPMI IP and it's different from the new one, deallocate it
        if ($currentIpmi && $currentIpmi !== $data['ip_address']) {
          error_log("Server already has IPMI IP: $currentIpmi - will deallocate before assigning new IP: {$data['ip_address']}");

          // Find which subnet the old IPMI IP belongs to
          $findSubnetSql = "SELECT subnet_id FROM ip_addresses WHERE ip_address = :ip_address AND is_used = 1";
          $findSubnetSth = $pdo->prepare($findSubnetSql);
          $findSubnetSth->bindValue(':ip_address', $currentIpmi);
          $findSubnetSth->execute();

          if ($findSubnetSth->rowCount() > 0) {
            $oldIpSubnetId = $findSubnetSth->fetch(PDO::FETCH_ASSOC)['subnet_id'];

            // Deallocate the old IPMI IP
            $deallocateIpSql = "UPDATE ip_addresses
                              SET is_used = 0,
                                  assigned_to = NULL,
                                  notes = NULL,
                                  device_type = NULL
                              WHERE ip_address = :ip_address AND subnet_id = :subnet_id";
            $deallocateIpSth = $pdo->prepare($deallocateIpSql);
            $deallocateIpSth->bindValue(':ip_address', $currentIpmi);
            $deallocateIpSth->bindValue(':subnet_id', $oldIpSubnetId);
            $deallocateIpSth->execute();

            error_log("Deallocated old IPMI IP: $currentIpmi from subnet ID: $oldIpSubnetId");
          } else {
            error_log("Warning: Old IPMI IP $currentIpmi not found in IP addresses table or already deallocated");
          }
        }
      }

      // Update the server's IPMI field
      $updateServerSql = "UPDATE $serverTable SET ipmi = :ipmi_address WHERE id = :server_id";
      $updateServerSth = $pdo->prepare($updateServerSql);
      $updateServerSth->bindValue(':ipmi_address', $data['ip_address']);
      $updateServerSth->bindValue(':server_id', $data['server_id']);
      $updateServerSth->execute();

      // Check if server was updated
      if ($updateServerSth->rowCount() === 0) {
        throw new Exception('Server not found or could not be updated');
      }

      // Log the IPMI update
      error_log("Updated IPMI address for server ID {$data['server_id']} in {$serverTable} to {$data['ip_address']}");
    }

    // If this is for a switch, update the switch record
    if (isset($data['for_switch']) && $data['for_switch'] === true) {
      // Validate switch data
      if (!isset($data['switch_id']) || empty($data['switch_id'])) {
        throw new Exception('Switch ID is required for switch IP allocation');
      }

      // Check if the switch already has an IP assigned
      $checkSwitchIpSql = "SELECT switch_ip FROM inventory_switches WHERE id = :switch_id";
      $checkSwitchIpSth = $pdo->prepare($checkSwitchIpSql);
      $checkSwitchIpSth->bindValue(':switch_id', $data['switch_id']);
      $checkSwitchIpSth->execute();

      if ($checkSwitchIpSth->rowCount() > 0) {
        $currentSwitchIp = $checkSwitchIpSth->fetch(PDO::FETCH_ASSOC)['switch_ip'];

        // If the switch already has an IP and it's different from the new one, deallocate it
        if ($currentSwitchIp && $currentSwitchIp !== $data['ip_address']) {
          error_log("Switch already has IP: $currentSwitchIp - will deallocate before assigning new IP: {$data['ip_address']}");

          // Find which subnet the old switch IP belongs to
          $findSubnetSql = "SELECT subnet_id FROM ip_addresses WHERE ip_address = :ip_address AND is_used = 1";
          $findSubnetSth = $pdo->prepare($findSubnetSql);
          $findSubnetSth->bindValue(':ip_address', $currentSwitchIp);
          $findSubnetSth->execute();

          if ($findSubnetSth->rowCount() > 0) {
            $oldIpSubnetId = $findSubnetSth->fetch(PDO::FETCH_ASSOC)['subnet_id'];

            // Deallocate the old switch IP
            $deallocateIpSql = "UPDATE ip_addresses
                              SET is_used = 0,
                                  assigned_to = NULL,
                                  notes = NULL,
                                  device_type = NULL
                              WHERE ip_address = :ip_address AND subnet_id = :subnet_id";
            $deallocateIpSth = $pdo->prepare($deallocateIpSql);
            $deallocateIpSth->bindValue(':ip_address', $currentSwitchIp);
            $deallocateIpSth->bindValue(':subnet_id', $oldIpSubnetId);
            $deallocateIpSth->execute();

            error_log("Deallocated old switch IP: $currentSwitchIp from subnet ID: $oldIpSubnetId");
          } else {
            error_log("Warning: Old switch IP $currentSwitchIp not found in IP addresses table or already deallocated");
          }
        }
      }

      // Update the switch's IP field
      $updateSwitchSql = "UPDATE inventory_switches SET switch_ip = :switch_ip WHERE id = :switch_id";
      $updateSwitchSth = $pdo->prepare($updateSwitchSql);
      $updateSwitchSth->bindValue(':switch_ip', $data['ip_address']);
      $updateSwitchSth->bindValue(':switch_id', $data['switch_id']);
      $updateSwitchSth->execute();

      // Check if switch was updated
      if ($updateSwitchSth->rowCount() === 0) {
        throw new Exception('Switch not found or could not be updated');
      }

      // Log the switch IP update
      error_log("Updated switch IP for switch ID {$data['switch_id']} to {$data['ip_address']}");
    }

    // Commit transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');

    // Prepare response message
    $message = 'IP address allocated successfully';
    if (isset($data['for_server_ipmi']) && $data['for_server_ipmi'] === true) {
      $message = 'IP address allocated and server IPMI updated successfully';

      // Add information about old IP deallocation if applicable
      if (isset($currentIpmi) && $currentIpmi && $currentIpmi !== $data['ip_address']) {
        $message = "IP address allocated and server IPMI updated successfully. Previous IPMI IP ($currentIpmi) was deallocated.";
      }
    }
    
    if (isset($data['for_switch']) && $data['for_switch'] === true) {
      $message = 'IP address allocated and switch IP updated successfully';

      // Add information about old IP deallocation if applicable
      if (isset($currentSwitchIp) && $currentSwitchIp && $currentSwitchIp !== $data['ip_address']) {
        $message = "IP address allocated and switch IP updated successfully. Previous switch IP ($currentSwitchIp) was deallocated.";
      }
    }

    // Format parent subnets for the response
    $formattedParents = array_map(function($parent) {
        return [
            'id' => $parent['id'],
            'subnet' => $parent['subnet'],
            'formatted_id' => 'SUB-' . str_pad($parent['id'], 4, '0', STR_PAD_LEFT)
        ];
    }, $parentSubnets);

    echo json_encode([
      'success' => true,
      'message' => $message,
      'ip' => [
        'ip_address' => $data['ip_address'],
        'is_used' => 1,
        'assigned_to' => $data['manual_alocation'],
        'notes' => $data['notes'] ?? null
      ],
      'server_updated' => isset($data['for_server_ipmi']) && $data['for_server_ipmi'] === true,
      'switch_updated' => isset($data['for_switch']) && $data['for_switch'] === true,
      'old_ipmi_deallocated' => isset($currentIpmi) && $currentIpmi && $currentIpmi !== $data['ip_address'] ? $currentIpmi : null,
      'old_switch_ip_deallocated' => isset($currentSwitchIp) && $currentSwitchIp && $currentSwitchIp !== $data['ip_address'] ? $currentSwitchIp : null,
      'old_ip_deallocated' => isset($data['deallocate_old_ip']) && $data['deallocate_old_ip'] === true && isset($data['old_ip_address']) ? $data['old_ip_address'] : null,
      'parent_subnets' => $formattedParents
    ]);

  } catch (Exception $e) {
    // Rollback transaction if active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in allocate_ip: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to allocate IP address: ' . $e->getMessage()
    ]);
  }
}

// API endpoint to delete all IP addresses for a subnet
elseif($_GET['f'] == 'delete_subnet_ips'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['subnet_id']) || empty($data['subnet_id'])) {
      throw new Exception('Subnet ID is required');
    }

    // Parse subnet ID (support both numeric and SUB-xxxx formats)
    if (is_numeric($data['subnet_id'])) {
      $subnetId = (int)$data['subnet_id'];
    } else if (preg_match('/SUB-(\d+)/', $data['subnet_id'], $matches)) {
      $subnetId = (int)$matches[1];
    } else {
      throw new Exception('Invalid subnet ID format');
    }

    // Begin transaction
    $pdo->beginTransaction();

    // Check if the subnet exists
    $checkSubnetSql = "SELECT id, subnet FROM subnets WHERE id = :subnet_id";
    $checkSubnetSth = $pdo->prepare($checkSubnetSql);
    $checkSubnetSth->bindValue(':subnet_id', $subnetId);
    $checkSubnetSth->execute();

    if ($checkSubnetSth->rowCount() === 0) {
      $pdo->rollBack();
      throw new Exception('Subnet not found');
    }

    $subnet = $checkSubnetSth->fetch(PDO::FETCH_ASSOC);

    // Check if any IPs are allocated (used)
    $checkAllocatedSql = "SELECT COUNT(*) FROM ip_addresses WHERE subnet_id = :subnet_id AND is_used = 1";
    $checkAllocatedSth = $pdo->prepare($checkAllocatedSql);
    $checkAllocatedSth->bindValue(':subnet_id', $subnetId);
    $checkAllocatedSth->execute();

    $allocatedCount = $checkAllocatedSth->fetchColumn();
    if ($allocatedCount > 0) {
      $pdo->rollBack();
      throw new Exception("Cannot delete IPs: $allocatedCount IP(s) are allocated in this subnet. Deallocate them first.");
    }

    // Delete all IP addresses for this subnet
    $deleteIpsSql = "DELETE FROM ip_addresses WHERE subnet_id = :subnet_id";
    $deleteIpsSth = $pdo->prepare($deleteIpsSql);
    $deleteIpsSth->bindValue(':subnet_id', $subnetId);
    $deleteIpsSth->execute();

    $deletedCount = $deleteIpsSth->rowCount();
    error_log("Deleted $deletedCount IP addresses from subnet ID $subnetId");

    // Commit transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => "Successfully deleted $deletedCount IP addresses from subnet {$subnet['subnet']}",
      'subnet_id' => $subnetId,
      'deleted_count' => $deletedCount
    ]);

  } catch (Exception $e) {
    // Rollback transaction if active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in delete_subnet_ips: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to delete subnet IPs: ' . $e->getMessage()
    ]);
  }
}

// API endpoint to deallocate an IP address
elseif($_GET['f'] == 'deallocate_ip'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['ip_address']) || empty($data['ip_address'])) {
      throw new Exception('IP address is required');
    }

    if (!isset($data['subnet_id']) || empty($data['subnet_id'])) {
      throw new Exception('Subnet ID is required');
    }

    // Parse subnet ID (support both numeric and SUB-xxxx formats)
    if (is_numeric($data['subnet_id'])) {
      $subnetId = (int)$data['subnet_id'];
    } else if (preg_match('/SUB-(\d+)/', $data['subnet_id'], $matches)) {
      $subnetId = (int)$matches[1];
    } else {
      throw new Exception('Invalid subnet ID format');
    }

    // Begin transaction
    $pdo->beginTransaction();

    // Check if IP exists and belongs to the subnet
    $checkIpSql = "SELECT id, is_used, assigned_to FROM ip_addresses
                  WHERE ip_address = :ip_address AND subnet_id = :subnet_id";
    $checkIpSth = $pdo->prepare($checkIpSql);
    $checkIpSth->bindValue(':ip_address', $data['ip_address']);
    $checkIpSth->bindValue(':subnet_id', $subnetId);
    $checkIpSth->execute();

    if ($checkIpSth->rowCount() === 0) {
      throw new Exception('IP address not found in this subnet');
    }

    $ip = $checkIpSth->fetch(PDO::FETCH_ASSOC);

    // Check if IP is actually allocated
    if ($ip['is_used'] != 1) {
      throw new Exception('IP address is not currently allocated');
    }

    // Check if this IP is used as an IPMI address for any server
    $serverUpdated = false;

    // Check dedicated servers
    $checkDedicatedSql = "SELECT id FROM inventory_dedicated_servers WHERE ipmi = :ip_address";
    $checkDedicatedSth = $pdo->prepare($checkDedicatedSql);
    $checkDedicatedSth->bindValue(':ip_address', $data['ip_address']);
    $checkDedicatedSth->execute();

    if ($checkDedicatedSth->rowCount() > 0) {
      $serverId = $checkDedicatedSth->fetch(PDO::FETCH_ASSOC)['id'];

      // Update the server to remove the IPMI address
      $updateServerSql = "UPDATE inventory_dedicated_servers SET ipmi = NULL WHERE id = :server_id";
      $updateServerSth = $pdo->prepare($updateServerSql);
      $updateServerSth->bindValue(':server_id', $serverId);
      $updateServerSth->execute();

      $serverUpdated = true;
      error_log("Removed IPMI address {$data['ip_address']} from dedicated server ID {$serverId}");
    }

    // Check blade servers
    $checkBladeSql = "SELECT id FROM blade_server_inventory WHERE ipmi = :ip_address";
    $checkBladeSth = $pdo->prepare($checkBladeSql);
    $checkBladeSth->bindValue(':ip_address', $data['ip_address']);
    $checkBladeSth->execute();

    if ($checkBladeSth->rowCount() > 0) {
      $serverId = $checkBladeSth->fetch(PDO::FETCH_ASSOC)['id'];

      // Update the server to remove the IPMI address
      $updateServerSql = "UPDATE blade_server_inventory SET ipmi = NULL WHERE id = :server_id";
      $updateServerSth = $pdo->prepare($updateServerSql);
      $updateServerSth->bindValue(':server_id', $serverId);
      $updateServerSth->execute();

      $serverUpdated = true;
      error_log("Removed IPMI address {$data['ip_address']} from blade server ID {$serverId}");
    }

    // Update the IP address to mark it as deallocated
    $updateIpSql = "UPDATE ip_addresses
                    SET is_used = 0,
                        assigned_to = NULL,
                        notes = NULL
                    WHERE ip_address = :ip_address AND subnet_id = :subnet_id";
    $updateIpSth = $pdo->prepare($updateIpSql);
    $updateIpSth->bindValue(':ip_address', $data['ip_address']);
    $updateIpSth->bindValue(':subnet_id', $subnetId);
    $updateIpSth->execute();

    // Commit transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => $serverUpdated
        ? 'IP address deallocated and server IPMI updated successfully'
        : 'IP address deallocated successfully',
      'ip' => [
        'ip_address' => $data['ip_address'],
        'is_used' => 0,
        'assigned_to' => null,
        'notes' => null
      ],
      'server_updated' => $serverUpdated
    ]);

  } catch (Exception $e) {
    // Rollback transaction if active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in deallocate_ip: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to deallocate IP address: ' . $e->getMessage()
    ]);
  }
}

// Force deallocate an IP address by its address
// Update subnet note
elseif($_GET['f'] == 'update_note'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['subnet_id']) || empty($data['subnet_id'])) {
      throw new Exception('Subnet ID is required');
    }

    // Parse subnet ID (support both numeric and SUB-xxxx formats)
    if (is_numeric($data['subnet_id'])) {
      $subnetId = (int)$data['subnet_id'];
    } else if (preg_match('/SUB-(\d+)/', $data['subnet_id'], $matches)) {
      $subnetId = (int)$matches[1];
    } else {
      throw new Exception('Invalid subnet ID format');
    }

    $note = isset($data['note']) ? trim($data['note']) : '';

    // Begin transaction
    $pdo->beginTransaction();

    // Verify the subnet exists
    $checkSubnetSql = "SELECT id FROM subnets WHERE id = :subnet_id";
    $checkSubnetSth = $pdo->prepare($checkSubnetSql);
    $checkSubnetSth->bindValue(':subnet_id', $subnetId);
    $checkSubnetSth->execute();

    if ($checkSubnetSth->rowCount() === 0) {
      throw new Exception('Subnet not found');
    }

    // Update the subnet note
    $updateNoteSql = "UPDATE subnets SET note = :note WHERE id = :subnet_id";
    $updateNoteSth = $pdo->prepare($updateNoteSql);
    $updateNoteSth->bindValue(':note', $note);
    $updateNoteSth->bindValue(':subnet_id', $subnetId);
    $updateNoteSth->execute();

    // Commit transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Subnet note updated successfully',
      'subnet_id' => $subnetId,
      'note' => $note
    ]);

  } catch (Exception $e) {
    // Rollback transaction if active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in update_note: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to update subnet note: ' . $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'force_deallocate_ip'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['ip_address']) || empty($data['ip_address'])) {
      throw new Exception('IP address is required');
    }

    // Begin transaction
    $pdo->beginTransaction();

    // Find the IP in the database
    $findIpSql = "SELECT * FROM ip_addresses WHERE ip_address = :ip_address";
    $findIpSth = $pdo->prepare($findIpSql);
    $findIpSth->bindValue(':ip_address', $data['ip_address']);
    $findIpSth->execute();

    if ($findIpSth->rowCount() === 0) {
      // IP not found, nothing to deallocate
      $pdo->commit();
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'IP address not found, nothing to deallocate',
        'ip_address' => $data['ip_address']
      ]);
      exit;
    }

    $ipData = $findIpSth->fetch(PDO::FETCH_ASSOC);
    $subnetId = $ipData['subnet_id'];

    // Check if this IP is used by a server's IPMI
    $serverUpdated = false;

    // Check dedicated servers
    $checkDedicatedSql = "SELECT id FROM inventory_dedicated_servers WHERE ipmi = :ip_address";
    $checkDedicatedSth = $pdo->prepare($checkDedicatedSql);
    $checkDedicatedSth->bindValue(':ip_address', $data['ip_address']);
    $checkDedicatedSth->execute();

    if ($checkDedicatedSth->rowCount() > 0) {
      $serverId = $checkDedicatedSth->fetch(PDO::FETCH_ASSOC)['id'];

      // Update the server to remove the IPMI address
      $updateServerSql = "UPDATE inventory_dedicated_servers SET ipmi = NULL WHERE id = :server_id";
      $updateServerSth = $pdo->prepare($updateServerSql);
      $updateServerSth->bindValue(':server_id', $serverId);
      $updateServerSth->execute();

      $serverUpdated = true;
      error_log("Removed IPMI address {$data['ip_address']} from dedicated server ID {$serverId}");
    }

    // Check blade servers
    $checkBladeSql = "SELECT id FROM blade_server_inventory WHERE ipmi = :ip_address";
    $checkBladeSth = $pdo->prepare($checkBladeSql);
    $checkBladeSth->bindValue(':ip_address', $data['ip_address']);
    $checkBladeSth->execute();

    if ($checkBladeSth->rowCount() > 0) {
      $serverId = $checkBladeSth->fetch(PDO::FETCH_ASSOC)['id'];

      // Update the server to remove the IPMI address
      $updateServerSql = "UPDATE blade_server_inventory SET ipmi = NULL WHERE id = :server_id";
      $updateServerSth = $pdo->prepare($updateServerSql);
      $updateServerSth->bindValue(':server_id', $serverId);
      $updateServerSth->execute();

      $serverUpdated = true;
      error_log("Removed IPMI address {$data['ip_address']} from blade server ID {$serverId}");
    }

    // Check switches
    $checkSwitchSql = "SELECT id FROM inventory_switches WHERE switch_ip = :ip_address";
    $checkSwitchSth = $pdo->prepare($checkSwitchSql);
    $checkSwitchSth->bindValue(':ip_address', $data['ip_address']);
    $checkSwitchSth->execute();

    if ($checkSwitchSth->rowCount() > 0) {
      $switchId = $checkSwitchSth->fetch(PDO::FETCH_ASSOC)['id'];

      // Update the switch to remove the IP address
      $updateSwitchSql = "UPDATE inventory_switches SET switch_ip = NULL WHERE id = :switch_id";
      $updateSwitchSth = $pdo->prepare($updateSwitchSql);
      $updateSwitchSth->bindValue(':switch_id', $switchId);
      $updateSwitchSth->execute();

      $serverUpdated = true;
      error_log("Removed IP address {$data['ip_address']} from switch ID {$switchId}");
    }

    // Update the IP address to mark it as deallocated
    $updateIpSql = "UPDATE ip_addresses
                    SET is_used = 0,
                        assigned_to = NULL,
                        notes = NULL,
                        device_type = NULL
                    WHERE ip_address = :ip_address";
    $updateIpSth = $pdo->prepare($updateIpSql);
    $updateIpSth->bindValue(':ip_address', $data['ip_address']);
    $updateIpSth->execute();

    error_log("Force deallocated IP address {$data['ip_address']} from subnet ID {$subnetId}");

    // Commit transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => $serverUpdated
        ? 'IP address force deallocated and device updated successfully'
        : 'IP address force deallocated successfully',
      'ip' => [
        'ip_address' => $data['ip_address'],
        'is_used' => 0,
        'assigned_to' => null,
        'notes' => null
      ],
      'device_updated' => $serverUpdated
    ]);

  } catch (Exception $e) {
    // Rollback transaction if active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in force_deallocate_ip: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to force deallocate IP address: ' . $e->getMessage()
    ]);
  }
}







// Add new API endpoint for manual switch configuration
elseif($_GET['f'] == 'configure_switch_interface'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['server_id']) || empty($data['server_id'])) {
      throw new Exception('Server ID is required');
    }

    if (!isset($data['server_type']) || empty($data['server_type'])) {
      throw new Exception('Server type is required');
    }

    if (!isset($data['ip_address']) || empty($data['ip_address'])) {
      throw new Exception('IP address is required');
    }

    $serverId = (int)$data['server_id'];
    $serverType = $data['server_type'];
    $ipAddress = $data['ip_address'];

    // Configure the switch interface
    $result = configureServerSwitchInterface($pdo, $serverId, $serverType, $ipAddress);

    // Return response
    header('Content-Type: application/json');
    if ($result['success']) {
      echo json_encode([
        'success' => true,
        'message' => $result['message'],
        'configuration_details' => $result
      ]);
    } else {
      http_response_code(400);
      echo json_encode([
        'success' => false,
        'error' => $result['error']
      ]);
    }

  } catch (Exception $e) {
    error_log("Error in configure_switch_interface: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Add new API endpoint for manual switch unconfiguration
elseif($_GET['f'] == 'unconfigure_switch_interface'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['server_id']) || empty($data['server_id'])) {
      throw new Exception('Server ID is required');
    }

    if (!isset($data['server_type']) || empty($data['server_type'])) {
      throw new Exception('Server type is required');
    }

    if (!isset($data['ip_address']) || empty($data['ip_address'])) {
      throw new Exception('IP address is required');
    }

    $serverId = (int)$data['server_id'];
    $serverType = $data['server_type'];
    $ipAddress = $data['ip_address'];

    // Unconfigure the switch interface
    $result = unconfigureSwitchInterface($pdo, $serverId, $serverType, $ipAddress);

    // Return response
    header('Content-Type: application/json');
    if ($result['success']) {
      echo json_encode([
        'success' => true,
        'message' => $result['message'],
        'unconfiguration_details' => $result
      ]);
    } else {
      http_response_code(400);
      echo json_encode([
        'success' => false,
        'error' => $result['error']
      ]);
    }

  } catch (Exception $e) {
    error_log("Error in unconfigure_switch_interface: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Add API endpoint for restarting the switch queue
elseif($_GET['f'] == 'restart_switch_queue'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    error_log("Admin $admin_id requested manual switch queue restart");
    
    // Call the restart function
    restartSwitchQueue($pdo);

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Switch queue restart completed successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error restarting switch queue: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Add API endpoint for debugging the switch queue
elseif($_GET['f'] == 'debug_switch_queue'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    error_log("Admin $admin_id requested switch queue debug");
    
    // Check queue status
    $queueSql = "SELECT COUNT(*) as total, 
                        SUM(CASE WHEN status = 'queued' THEN 1 ELSE 0 END) as queued,
                        SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
                 FROM automation_in_progres";
    $queueResult = $pdo->query($queueSql)->fetch(PDO::FETCH_ASSOC);
    
    // Check locks
    $locksSql = "SELECT COUNT(*) as total_locks FROM switch_locks";
    $locksResult = $pdo->query($locksSql)->fetch(PDO::FETCH_ASSOC);
    
    // Get recent operations
    $recentSql = "SELECT * FROM automation_in_progres ORDER BY created_at DESC LIMIT 5";
    $recentOps = $pdo->query($recentSql)->fetchAll(PDO::FETCH_ASSOC);
    
    // Test SSH extension
    $sshAvailable = extension_loaded('ssh2');
    
    // Try to manually process one operation to see what happens
    $debugLog = [];
    $debugLog[] = "🔍 Starting debug queue processing...";
    
    // Get the oldest queued operation
    $testOpSql = "SELECT * FROM automation_in_progres WHERE status = 'queued' ORDER BY created_at ASC LIMIT 1";
    $testOp = $pdo->query($testOpSql)->fetch(PDO::FETCH_ASSOC);
    
    if ($testOp) {
      $debugLog[] = "📋 Found test operation: " . $testOp['id'];
      
      // Try to acquire lock
      $lockResult = acquireSwitchLock($testOp['switch_ip'], $testOp['port'], 5, $pdo);
      $debugLog[] = "🔒 Lock attempt result: " . ($lockResult ? 'SUCCESS' : 'FAILED');
      
      if ($lockResult) {
        releaseSwitchLock($testOp['switch_ip'], $testOp['port'], $pdo);
        $debugLog[] = "🔓 Lock released successfully";
      }
    } else {
      $debugLog[] = "📭 No queued operations found";
    }

    // Return debug response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'debug_info' => [
        'queue_stats' => $queueResult,
        'active_locks' => $locksResult,
        'ssh_extension_loaded' => $sshAvailable,
        'recent_operations' => $recentOps,
        'debug_log' => $debugLog
      ]
    ]);

  } catch (Exception $e) {
    error_log("Error debugging switch queue: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Add API endpoint for manually triggering queue processing
elseif($_GET['f'] == 'manual_process_queue'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    error_log("Admin $admin_id manually triggering queue processing");
    
    // Call processSwitchQueue directly with explicit PDO connection
    error_log("🚀 Starting manual queue processing with explicit PDO connection");
    processSwitchQueue($pdo);
    error_log("🏁 Manual queue processing completed");

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Manual queue processing completed'
    ]);

  } catch (Exception $e) {
    error_log("Error in manual queue processing: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Add API endpoint for getting switch queue status (for UI monitoring)
elseif($_GET['f'] == 'get_switch_queue_status'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Get queue statistics
    $queueSql = "SELECT COUNT(*) as total, 
                        SUM(CASE WHEN status = 'queued' THEN 1 ELSE 0 END) as queued,
                        SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
                 FROM automation_in_progres 
                 WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)";
    $queueStats = $pdo->query($queueSql)->fetch(PDO::FETCH_ASSOC);
    
    // Get recent operations (last 10)
    $recentSql = "SELECT id, operation_type, switch_ip, port, status, created_at, started_at, completed_at, error_message,
                         JSON_UNQUOTE(JSON_EXTRACT(params, '$.server_label')) as server_label
                  FROM automation_in_progres 
                  ORDER BY created_at DESC 
                  LIMIT 10";
    $recentOps = $pdo->query($recentSql)->fetchAll(PDO::FETCH_ASSOC);
    
    // Get active locks count
    $locksSql = "SELECT COUNT(*) as active_locks FROM switch_locks";
    $locksCount = $pdo->query($locksSql)->fetch(PDO::FETCH_ASSOC);
    
    // Calculate queue health
    $queueHealth = 'healthy';
    if ($queueStats['processing'] > 0) {
      $queueHealth = 'processing';
    } elseif ($queueStats['queued'] > 0) {
      $queueHealth = 'queued';
    } elseif ($queueStats['failed'] > 0) {
      $queueHealth = 'warning';
    }

    // Return response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'queue_stats' => $queueStats,
      'recent_operations' => $recentOps,
      'active_locks' => $locksCount['active_locks'],
      'queue_health' => $queueHealth,
      'last_updated' => date('Y-m-d H:i:s')
    ]);

  } catch (Exception $e) {
    error_log("Error getting switch queue status: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Add API endpoint for getting switches (for UI switch name lookup)
elseif($_GET['f'] == 'get_switches'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Get all switches with their labels
    $switchesSql = "SELECT id, label, switch_ip, status FROM inventory_switches ORDER BY label";
    $switches = $pdo->query($switchesSql)->fetchAll(PDO::FETCH_ASSOC);
    
    // Return response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'data' => $switches
    ]);

  } catch (Exception $e) {
    error_log("Error getting switches: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Add API endpoint for retrying a failed switch queue task
elseif($_GET['f'] == 'retry_switch_queue_task'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Get request data
    $input = file_get_contents('php://input');
    error_log("Retry request input: " . $input);
    
    $data = json_decode($input, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new Exception("Invalid JSON input: " . json_last_error_msg());
    }
    
    $taskId = isset($data['task_id']) ? $data['task_id'] : null;
    
    if (!$taskId) {
      throw new Exception("Task ID is required");
    }
    
    error_log("Admin $admin_id requested retry for task ID: $taskId");
    
    // Check if task exists and is in failed status
    $checkTaskSql = "SELECT id, status, operation_type, switch_ip, port FROM automation_in_progres WHERE id = :task_id";
    $checkTaskSth = $pdo->prepare($checkTaskSql);
    $checkTaskSth->bindValue(':task_id', $taskId);
    $checkTaskSth->execute();
    
    if ($checkTaskSth->rowCount() === 0) {
      throw new Exception("Task not found");
    }
    
    $task = $checkTaskSth->fetch(PDO::FETCH_ASSOC);
    
    if ($task['status'] !== 'failed') {
      throw new Exception("Only failed tasks can be retried. Current status: " . $task['status']);
    }
    
    // Reset task to queued status and clear error details
    $retrySql = "UPDATE automation_in_progres 
                 SET status = 'queued', 
                     started_at = NULL, 
                     completed_at = NULL, 
                     error_message = NULL
                 WHERE id = :task_id";
    $retrySth = $pdo->prepare($retrySql);
    $retrySth->bindValue(':task_id', $taskId);
    $retrySth->execute();
    
    if ($retrySth->rowCount() === 0) {
      throw new Exception("Failed to update task status");
    }
    
    error_log("Task $taskId reset to queued status for retry");
    
    // Process the queue to pick up the retried task
    processSwitchQueue($pdo);
    
    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => "Task $taskId has been reset and queued for retry",
      'task' => [
        'id' => $taskId,
        'operation_type' => $task['operation_type'],
        'switch_ip' => $task['switch_ip'],
        'port' => $task['port']
      ]
    ]);

  } catch (Exception $e) {
    error_log("Error retrying task: " . $e->getMessage());

    // Return error as JSON
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Function to configure switch interface via SNMP
function configureServerSwitchInterface($pdo, $serverId, $serverType, $ipAddress) {
  try {
    error_log("Starting switch interface configuration for server $serverId, type $serverType, IP $ipAddress");
    
    // Determine the server table based on the passed server type parameter
    $serverTable = ($serverType === 'dedicated') ? 'inventory_dedicated_servers' : 'blade_server_inventory';
    
    // Get server details including switch and port information
    $getServerSql = "SELECT s.*, sw.switch_ip, sw.snmp_community, sw.snmp_version, sw.root_password, s.label
                     FROM $serverTable s
                     LEFT JOIN inventory_switches sw ON s.switch_id = sw.id
                     WHERE s.id = :server_id";
    $getServerSth = $pdo->prepare($getServerSql);
    $getServerSth->bindValue(':server_id', $serverId);
    $getServerSth->execute();
    
    if ($getServerSth->rowCount() === 0) {
      throw new Exception("Server not found");
    }
    
    $server = $getServerSth->fetch(PDO::FETCH_ASSOC);
    
    // Check if server has switch information
    if (empty($server['switch_id']) || empty($server['switch_ip'])) {
      error_log("Server $serverId does not have switch configuration - skipping interface configuration");
      return [
        'success' => true,
        'message' => 'Server has no switch configured - interface configuration skipped',
        'configured' => false
      ];
    }
    
    // Check if server has port1 configured (primary port)
    if (empty($server['port1'])) {
      error_log("Server $serverId does not have port1 configured - skipping interface configuration");
      return [
        'success' => true,
        'message' => 'Server has no primary port configured - interface configuration skipped',
        'configured' => false
      ];
    }
    
    // Resolve port1 to actual port name/number
    // port1 might be a port ID that needs to be resolved to port_name/port_number
    $serverPort = $server['port1'];
    
    // Check if port1 is numeric (likely a port ID that needs resolution)
    if (is_numeric($serverPort)) {
      $getPortSql = "SELECT port_name, port_number FROM inventory_switch_ports WHERE id = :port_id AND switch_id = :switch_id";
      $getPortSth = $pdo->prepare($getPortSql);
      $getPortSth->bindValue(':port_id', $serverPort);
      $getPortSth->bindValue(':switch_id', $server['switch_id']);
      $getPortSth->execute();
      
      if ($getPortSth->rowCount() > 0) {
        $portInfo = $getPortSth->fetch(PDO::FETCH_ASSOC);
        // Use port_number (like "Ethernet35") for SNMP configuration, fallback to port_name
        $serverPort = !empty($portInfo['port_number']) ? $portInfo['port_number'] : $portInfo['port_name'];
        error_log("Resolved port ID {$server['port1']} to port interface: $serverPort");
        
        // For SNMP interface discovery, extract just the numeric part if it's a full interface name
        $portNumberForSNMP = $serverPort;
        if (preg_match('/(\d+)$/', $serverPort, $matches)) {
          $portNumberForSNMP = $matches[1];
          error_log("Extracted port number for SNMP: $portNumberForSNMP from $serverPort");
        }
      } else {
        error_log("Could not resolve port ID {$server['port1']} for server $serverId");
        return [
          'success' => false,
          'error' => 'Could not resolve server port configuration',
          'configured' => false
        ];
      }
    }
    
    // Extract IP and calculate gateway IP from CIDR
    $configIp = $ipAddress;
    if (strpos($ipAddress, '/') !== false) {
      list($networkIp, $prefixLength) = explode('/', $ipAddress);
      // Calculate the gateway IP (first usable IP in the subnet)
      $networkLong = ip2long($networkIp);
      $gatewayLong = $networkLong + 1; // First usable IP is network + 1
      $configIp = long2ip($gatewayLong);
      error_log("Calculated gateway IP: $configIp from subnet $ipAddress");
    }
    
    // Get switch credentials
    $switchIp = $server['switch_ip'];
    $switchPassword = $server['root_password'];
    
    // Pass the full subnet info to the SSH function
    $subnetInfo = strpos($ipAddress, '/') !== false ? $ipAddress : $configIp;

    error_log("Configuring switch $switchIp port $serverPort with IP $configIp via SSH");
    
    // Queue the switch configuration operation to prevent race conditions
    $operationId = queueSwitchOperation('configure', $switchIp, $serverPort, [
      'password' => $switchPassword,
      'ip_address' => $configIp,
      'subnet_info' => $subnetInfo,
      'server_label' => $server['label'] ?? null
    ]);
    
    if ($operationId) {
      error_log("Switch configuration queued for server $serverId (Operation ID: $operationId)");
      
      // Process the queue immediately for this manual configuration request
      processSwitchQueue($pdo);
      
      // Return success for queueing
      $result = ['success' => true, 'queued' => true, 'operation_id' => $operationId];
    } else {
      error_log("Failed to queue switch configuration for server $serverId");
      $result = ['success' => false, 'error' => 'Failed to queue operation'];
    }
    
    if ($result['success']) {
      error_log("Successfully configured switch interface for server $serverId via SSH");
      return [
        'success' => true,
        'message' => "Switch interface configured successfully on port $serverPort with IP $configIp",
        'configured' => true,
        'switch_ip' => $switchIp,
        'port' => $serverPort,
        'configured_ip' => $configIp
      ];
    } else {
      // Return the error details for better debugging
      error_log("SSH switch interface configuration failed for server $serverId: " . $result['error']);
      return [
        'success' => false,
        'error' => "Failed to configure switch interface via SSH: " . $result['error'],
        'configured' => false,
        'switch_ip' => $switchIp,
        'port' => $serverPort,
        'attempted_ip' => $configIp,
        'manual_config_needed' => true
      ];
    }
    
  } catch (Exception $e) {
    error_log("Error configuring switch interface: " . $e->getMessage());
    return [
      'success' => false,
      'error' => $e->getMessage(),
      'configured' => false
    ];
  }
}



function configureSwitchViaSsh($switchIp, $password, $portNumber, $ipAddress, $subnetInfo = null, $serverLabel = null) {
  global $pdo;
  
  try {
    error_log("=== Starting Switch Configuration ===");
    error_log("Switch IP: $switchIp");
    error_log("Port: $portNumber");
    error_log("Primary IP: $ipAddress");
    error_log("Subnet Info Type: " . gettype($subnetInfo));
    error_log("Subnet Info Content: " . print_r($subnetInfo, true));
    error_log("Subnet Info is empty: " . (empty($subnetInfo) ? 'YES' : 'NO'));
    error_log("Subnet Info is array: " . (is_array($subnetInfo) ? 'YES' : 'NO'));
    
    // Check if SSH2 extension is available
    if (!extension_loaded('ssh2')) {
      error_log("SSH2 extension not available");
      return ['success' => false, 'error' => 'SSH2 extension not available'];
    }
    
    // Connect via SSH
    $connection = @ssh2_connect($switchIp, 22);
    if (!$connection) {
      error_log("Could not connect to switch");
      return ['success' => false, 'error' => 'Could not connect to switch'];
    }
    
    // Authenticate
    if (!@ssh2_auth_password($connection, 'admin', $password)) {
      error_log("SSH authentication failed");
      return ['success' => false, 'error' => 'SSH authentication failed'];
    }
    
    // Open shell session
    $shell = @ssh2_shell($connection, 'vt102');
    if (!$shell) {
      error_log("Could not open SSH shell");
      return ['success' => false, 'error' => 'Could not open SSH shell'];
    }
    
    // Get IP helper address
    $ipHelperAddress = "*************"; // Default
    try {
      $helperStmt = $pdo->prepare("SELECT setting_value FROM general_settings WHERE setting_key = 'ip_helper_address' LIMIT 1");
      $helperStmt->execute();
      $helperResult = $helperStmt->fetch(PDO::FETCH_ASSOC);
      if ($helperResult && !empty($helperResult['setting_value'])) {
        $ipHelperAddress = $helperResult['setting_value'];
      }
    } catch (Exception $e) {
      // Use default
    }
    error_log("IP Helper Address: $ipHelperAddress");
    
    // Start building commands
    $commands = [
      "enable",
      "configure terminal",
      "interface $portNumber",
      "no shutdown",
      "description " . ($serverLabel ?? 'API') . " " . date('Y-m-d H:i:s'),
      "no switchport",
      "no ip address"  // Clear existing config
    ];
    
    // Process subnet information
    $allSubnets = [];

    // If subnetInfo is provided as array or string, use it
    if (!empty($subnetInfo)) {
      if (is_array($subnetInfo)) {
        $allSubnets = $subnetInfo;
      } else {
        $allSubnets[] = $subnetInfo;
      }
    }

    error_log("Subnets to configure: " . print_r($allSubnets, true));
    error_log("Number of subnets to configure: " . count($allSubnets));

    // Configure IP addresses with CIDR notation
    $ipIndex = 0;
    $aclSubnets = []; // Track subnets for ACL configuration

    if (empty($allSubnets)) {
      error_log("WARNING: No subnets provided for configuration - this will result in no IP address being set");
    }

    foreach ($allSubnets as $subnet) {
      error_log("Processing subnet: '$subnet'");

      if (empty($subnet) || strpos($subnet, '/') === false) {
        error_log("Skipping invalid subnet: '$subnet' (empty or no CIDR notation)");
        continue;
      }

      // Store original subnet for ACL
      $aclSubnets[] = $subnet;

      // Parse subnet to get network and prefix
      list($network, $prefix) = explode('/', $subnet);
      error_log("Parsed subnet - Network: '$network', Prefix: '$prefix'");

      $networkLong = ip2long($network);

      if ($networkLong === false) {
        error_log("Invalid network portion '$network' in subnet $subnet – skipping");
        continue;
      }

      $gatewayIp = long2ip($networkLong + 1); // First usable IP
      error_log("Calculated gateway IP: '$gatewayIp' from network '$network'");

      // Build the IP address command with CIDR notation
      if ($ipIndex === 0) {
        // Primary IP address
        $ipCommand = "ip address {$gatewayIp}/{$prefix}";
      } else {
        // Additional IP addresses use 'secondary' keyword
        $ipCommand = "ip address {$gatewayIp}/{$prefix} secondary";
      }

      $commands[] = $ipCommand;
      error_log("Added IP command: '$ipCommand'");
      $ipIndex++;
    }

    if ($ipIndex === 0) {
      error_log("ERROR: No valid IP addresses were configured - interface will have no IP!");
    }
    
    // Complete interface configuration
    $commands[] = "ip helper-address $ipHelperAddress";
    $commands[] = "no shutdown";
    $commands[] = "exit";
    $commands[] = "exit";
    $commands[] = "write memory";
    
    // Configure ACLs if we have valid subnets
    if (!empty($aclSubnets)) {
      $interfaceName = str_replace('interface ', '', $portNumber);
      $aclName = $interfaceName;
      
      // Remove old ACL
      $commands[] = "configure terminal";
      $commands[] = "interface $interfaceName";
      $commands[] = "no ip access-group $aclName in";
      $commands[] = "exit";
      $commands[] = "no ip access-list $aclName";
      
      // Create new ACL
      $commands[] = "ip access-list $aclName";
      $seq = 1;
      foreach ($aclSubnets as $subnet) {
        if (!empty($subnet) && strpos($subnet, '/') !== false) {
          $commands[] = "$seq permit ip $subnet any";
          $seq++;
        }
      }
      $commands[] = "999 deny ip any any";
      $commands[] = "exit";
      $commands[] = "interface $interfaceName";
      $commands[] = "ip access-group $aclName in";
      $commands[] = "exit";
      $commands[] = "exit";
      $commands[] = "write memory";
      $commands[] = "exit";  // Final exit
    }
    
    error_log("Total commands to execute: " . count($commands));
    error_log("Commands: " . implode(" ; ", $commands));
    error_log("IP configuration commands found: " . count(array_filter($commands, function($cmd) { return strpos($cmd, 'ip address') !== false; })));
    
    // Execute commands
    $output = '';
    $errors = [];
    
    foreach ($commands as $cmd) {
      fwrite($shell, $cmd . "\n");
      usleep(300000); // 300ms delay
      
      // Read output
      $buffer = '';
      $start = time();
      while ((time() - $start) < 1) {
        $data = fread($shell, 4096);
        if ($data !== false) {
          $buffer .= $data;
        }
        if (strpos($buffer, '#') !== false || strpos($buffer, '>') !== false) {
          break;
        }
        usleep(50000);
      }
      
      $output .= "CMD: $cmd\n$buffer\n";
      
      // Check for errors
      if (preg_match('/Invalid|Error|Incomplete|Ambiguous/i', $buffer) && 
          !preg_match('/no ip access-group/i', $cmd)) { // Ignore "no" command errors
        $errors[] = "Command '$cmd' may have failed";
        error_log("Possible error with command: $cmd - Output: $buffer");
      }
    }
    
    fclose($shell);
    ssh2_disconnect($connection);
    
    if (!empty($errors)) {
      error_log("Configuration completed with warnings: " . implode("; ", $errors));
      return [
        'success' => true,
        'warnings' => $errors,
        'message' => 'Configuration applied with warnings',
        'output' => $output
      ];
    }
    
    error_log("Switch configuration completed successfully");
    return [
      'success' => true,
      'message' => 'Switch configured successfully',
      'output' => $output
    ];
    
  } catch (Exception $e) {
    error_log("Exception in switch configuration: " . $e->getMessage());
    return ['success' => false, 'error' => $e->getMessage()];
  }
}


function ensureSwitchConfiguration($pdo, $serverId, $serverType) {
  try {
      error_log("🔧 Starting ensureSwitchConfiguration for server $serverId (type: $serverType)");
      
      // Get server table
      $serverTable = $serverType === 'dedicated' ? 'inventory_dedicated_servers' : 'blade_server_inventory';
      
      // Get server details with switch info
      $sql = "SELECT s.*, sw.switch_ip, sw.root_password, sw.label as switch_label
              FROM $serverTable s
              LEFT JOIN inventory_switches sw ON s.switch_id = sw.id
              WHERE s.id = :server_id";
      
      $sth = $pdo->prepare($sql);
      $sth->bindValue(':server_id', $serverId);
      $sth->execute();
      
      if ($sth->rowCount() === 0) {
          error_log("❌ Server not found: $serverId");
          return ['success' => false, 'error' => 'Server not found'];
      }
      
      $server = $sth->fetch(PDO::FETCH_ASSOC);
      
      // Check if server has switch configuration
      if (!$server['switch_ip'] || !$server['root_password'] || !$server['port1']) {
          error_log("⚠️ Server lacks switch configuration - IP: {$server['switch_ip']}, Port: {$server['port1']}");
          return ['success' => false, 'error' => 'Server lacks switch configuration'];
      }
      
      // Resolve port name
      $port = $server['port1'];
      if (is_numeric($port)) {
          $portSql = "SELECT port_name, port_number FROM inventory_switch_ports 
                     WHERE id = :port_id AND switch_id = :switch_id";
          $portSth = $pdo->prepare($portSql);
          $portSth->bindValue(':port_id', $port);
          $portSth->bindValue(':switch_id', $server['switch_id']);
          $portSth->execute();
          
          if ($portSth->rowCount() > 0) {
              $portInfo = $portSth->fetch(PDO::FETCH_ASSOC);
              $port = !empty($portInfo['port_number']) ? $portInfo['port_number'] : $portInfo['port_name'];
          }
      }
      
      // Build list of all subnets
      $subnets = [];
      if (!empty($server['main_ip'])) {
          $subnets[] = trim($server['main_ip']);
      }
      if (!empty($server['additional_ips'])) {
          $additionalSubnets = array_filter(array_map('trim', explode(',', $server['additional_ips'])));
          $subnets = array_merge($subnets, $additionalSubnets);
      }
      
      if (empty($subnets)) {
          error_log("⚠️ No subnets to configure for server $serverId");
          return ['success' => false, 'error' => 'No subnets to configure'];
      }
      
      // Calculate gateway IP from first subnet
      $primarySubnet = $subnets[0];
      $gatewayIp = $primarySubnet;
      if (strpos($primarySubnet, '/') !== false) {
          list($netIp, $prefix) = explode('/', $primarySubnet);
          $gatewayIp = long2ip(ip2long($netIp) + 1);
      }
      
      error_log("📋 Configuring switch {$server['switch_ip']} port $port with subnets: " . implode(', ', $subnets));
      
      // Try direct SSH configuration first (bypass queue for immediate configuration)
      $result = configureSwitchViaSshDirect(
          $server['switch_ip'],
          $server['root_password'],
          $port,
          $gatewayIp,
          $subnets,
          $server['label'] ?? null,
          $pdo
      );
      
      if ($result['success']) {
          error_log("✅ Direct switch configuration successful");
          return $result;
      }
      
      // If direct fails, queue it
      error_log("⚠️ Direct configuration failed, queueing operation");
      
      $operationId = queueSwitchOperation('configure', $server['switch_ip'], $port, [
          'password' => $server['root_password'],
          'ip_address' => $gatewayIp,
          'subnet_info' => $subnets,
          'server_label' => $server['label'] ?? null
      ], $pdo);
      
      if ($operationId) {
          // Try to process immediately
          processSwitchQueue($pdo);
          return ['success' => true, 'queued' => true, 'operation_id' => $operationId];
      }
      
      return ['success' => false, 'error' => 'Failed to configure or queue switch operation'];
      
  } catch (Exception $e) {
      error_log("💥 Exception in ensureSwitchConfiguration: " . $e->getMessage());
      return ['success' => false, 'error' => $e->getMessage()];
  }
}

function configureSwitchViaSshDirect($switchIp, $password, $portNumber, $ipAddress, $subnetInfo, $serverLabel, $pdo) {
  try {
      error_log("🔌 Attempting direct SSH configuration for switch $switchIp port $portNumber");
      
      if (!extension_loaded('ssh2')) {
          error_log("❌ SSH2 extension not available");
          return ['success' => false, 'error' => 'SSH2 extension not available'];
      }
      
      // Connect with timeout
      $connection = @ssh2_connect($switchIp, 22, ['timeout' => 10]);
      if (!$connection) {
          error_log("❌ Could not connect to switch $switchIp");
          return ['success' => false, 'error' => 'Could not connect to switch'];
      }
      
      // Authenticate
      if (!@ssh2_auth_password($connection, 'admin', $password)) {
          error_log("❌ SSH authentication failed for switch $switchIp");
          return ['success' => false, 'error' => 'SSH authentication failed'];
      }
      
      // Open shell
      $shell = @ssh2_shell($connection, 'vt102', null, 80, 24, SSH2_TERM_UNIT_CHARS);
      if (!$shell) {
          error_log("❌ Could not open SSH shell");
          return ['success' => false, 'error' => 'Could not open SSH shell'];
      }
      
      // Set stream to non-blocking
      stream_set_blocking($shell, false);
      
      // Normalize subnet list
      $subnetList = is_array($subnetInfo) ? $subnetInfo : [$subnetInfo];
      
      // Get IP helper address
      $ipHelperAddress = "*************"; // Default
      try {
          $helperStmt = $pdo->prepare("SELECT setting_value FROM general_settings WHERE setting_key = 'ip_helper_address' LIMIT 1");
          $helperStmt->execute();
          $helperResult = $helperStmt->fetch(PDO::FETCH_ASSOC);
          if ($helperResult && !empty($helperResult['setting_value'])) {
              $ipHelperAddress = $helperResult['setting_value'];
          }
      } catch (Exception $e) {
          error_log("Using default IP helper: $ipHelperAddress");
      }
      
      // Build commands
      $commands = [
          "",  // Empty line to ensure clean start
          "enable",
          "configure terminal",
          "interface $portNumber",
          "no shutdown",
          "description " . ($serverLabel ?? 'API') . " " . date('Y-m-d H:i:s'),
          "no switchport",
          "no ip address"  // Clear existing config
      ];
      
      // Add IP addresses
      $index = 0;
      foreach ($subnetList as $subnet) {
          if (strpos($subnet, '/') === false) continue;
          
          list($netIp, $prefix) = explode('/', $subnet);
          $gatewayIp = long2ip(ip2long($netIp) + 1);
          
          $ipCmd = "ip address $gatewayIp/$prefix";
          if ($index > 0) {
              $ipCmd .= " secondary";
          }
          $commands[] = $ipCmd;
          $index++;
      }
      
      // Add IP helper and complete interface config
      $commands[] = "ip helper-address $ipHelperAddress";
      $commands[] = "no shutdown";
      $commands[] = "exit";
      
      // Clear and configure ACLs
      $interfaceName = str_replace('interface ', '', $portNumber);
      $aclName = $interfaceName;
      
      // Remove old ACL
      $commands[] = "interface $interfaceName";
      $commands[] = "no ip access-group $aclName in";
      $commands[] = "exit";
      $commands[] = "no ip access-list $aclName";
      
      // Create new ACL
      if (!empty($subnetList)) {
          $commands[] = "ip access-list $aclName";
          $seq = 1;
          foreach ($subnetList as $subnet) {
              if (strpos($subnet, '/') !== false) {
                  $commands[] = "$seq permit ip $subnet any";
                  $seq++;
              }
          }
          $commands[] = "999 deny ip any any";
          $commands[] = "exit";
          $commands[] = "interface $interfaceName";
          $commands[] = "ip access-group $aclName in";
          $commands[] = "exit";
      }
      
      $commands[] = "exit";
      $commands[] = "write memory";
      $commands[] = "exit";
      
      error_log("📝 Executing " . count($commands) . " commands on switch");
      
      // Execute commands with better error handling
      $output = '';
      $errors = [];
      
      foreach ($commands as $cmd) {
          if (!empty($cmd)) {
              error_log("  → $cmd");
          }
          
          fwrite($shell, $cmd . "\n");
          
          // Wait for command to process
          usleep(200000); // 200ms
          
          // Read output
          $cmdOutput = '';
          $start = time();
          while (time() - $start < 2) {
              $data = fread($shell, 4096);
              if ($data !== false && strlen($data) > 0) {
                  $cmdOutput .= $data;
                  
                  // Check for errors
                  if (stripos($data, 'Invalid input') !== false || 
                      stripos($data, 'Error') !== false ||
                      stripos($data, 'Failed') !== false) {
                      $errors[] = "Command '$cmd' may have failed: " . trim($data);
                  }
              }
              usleep(50000); // 50ms
          }
          
          $output .= $cmdOutput;
      }
      
      fclose($shell);
      ssh2_disconnect($connection);
      
      if (!empty($errors)) {
          error_log("⚠️ Configuration completed with warnings: " . implode('; ', $errors));
          return [
              'success' => true,
              'warnings' => $errors,
              'message' => 'Configuration applied with warnings',
              'output' => $output
          ];
      }
      
      error_log("✅ Switch configuration completed successfully");
      return [
          'success' => true,
          'message' => 'Switch configured successfully',
          'output' => $output
      ];
      
  } catch (Exception $e) {
      error_log("💥 Exception in direct SSH configuration: " . $e->getMessage());
      return ['success' => false, 'error' => $e->getMessage()];
  }
}

// Function to create ACL commands for SSH configuration
function createAclCommands($portNumber, $subnetInfo = null) {
  $commands = [];
  
  // Normalise subnet list (allow string or array)
  $subnets = [];
  if (is_array($subnetInfo)) {
    $subnets = $subnetInfo;
  } elseif (!empty($subnetInfo)) {
    $subnets[] = $subnetInfo;
  }

  // Filter only valid CIDRs
  $subnets = array_values(array_filter($subnets, function ($cidr) {
    return strpos($cidr, '/') !== false;
  }));

  if (empty($subnets)) {
    error_log("No valid subnet info provided for ACL creation");
    return $commands;
  }

  // Extract interface name (remove "interface " prefix if present)
  $interfaceName = str_replace('interface ', '', $portNumber);
  $aclName = $interfaceName;

  // ACL base commands
  $commands[] = "configure terminal";
  $commands[] = "ip access-list $aclName";

  // Add a permit line per subnet with incremental sequence numbers starting at 1
  $seq = 1;
  foreach ($subnets as $cidr) {
    $commands[] = "$seq permit ip $cidr any";
    $seq++;
  }

  // Final deny rule
  $commands[] = "999 deny ip any any";
  $commands[] = "exit";
  $commands[] = "interface $interfaceName";
  $commands[] = "ip access-group $aclName in";
  $commands[] = "exit";
  $commands[] = "exit";

  error_log("Created ACL commands for interface $interfaceName with " . count($subnets) . " subnet(s)");

  return $commands;
}

// Function to create ACL expect script commands
function createAclExpectScript($portNumber, $subnetInfo = null) {
  if (!$subnetInfo || strpos($subnetInfo, '/') === false) {
    error_log("No subnet info provided for ACL expect script creation");
    return "";
  }

  // Extract interface name (remove "interface " prefix if present)
  $interfaceName = str_replace('interface ', '', $portNumber);
  $aclName = $interfaceName;
  
  $aclScript = "
        send \"configure terminal\\r\"
        expect \"#\"
        send \"ip access-list $aclName\\r\"
        expect \"#\"
        send \"1 permit ip $subnetInfo any\\r\"
        expect \"#\"
        send \"999 deny ip any any\\r\"
        expect \"#\"
        send \"exit\\r\"
        expect \"#\"
        send \"interface $interfaceName\\r\"
        expect \"#\"
        send \"ip access-group $aclName in\\r\"
        expect \"#\"
        send \"exit\\r\"
        expect \"#\"
";
  
  error_log("Created ACL expect script for interface $interfaceName with subnet $subnetInfo");
  
  return $aclScript;
}

// Function to create ACL removal commands
function createAclRemovalCommands($portNumber, $subnetInfo = null) {
  $commands = [];
  
  // Extract interface name (remove "interface " prefix if present)
  $interfaceName = str_replace('interface ', '', $portNumber);
  $aclName = $interfaceName;
  
  // Add ACL removal commands
  $commands[] = "configure terminal";
  $commands[] = "interface $interfaceName";
  $commands[] = "no ip access-group $aclName in";
  $commands[] = "no ip address-helper";  // Remove ACL from interface
  $commands[] = "exit";
  $commands[] = "no ip access-list $aclName";  // Delete the ACL
  $commands[] = "exit";
  $commands[] = "write memory";
  
  error_log("Created ACL removal commands for interface $interfaceName");
  
  return $commands;
}

// Function to create ACL removal expect script commands
function createAclRemovalExpectScript($portNumber, $subnetInfo = null) {
  // Extract interface name (remove "interface " prefix if present)
  $interfaceName = str_replace('interface ', '', $portNumber);
  $aclName = $interfaceName;
  
  $aclRemovalScript = "
        send \"configure terminal\\r\"
        expect \"#\"
        send \"interface $interfaceName\\r\"
        expect \"#\"
        send \"no ip access-group $aclName in\\r\"
        expect \"#\"
        send \"exit\\r\"
        expect \"#\"
        send \"no ip access-list $aclName\\r\"
        expect \"#\"
        send \"exit\\r\"
        expect \"#\"
";
  
  error_log("Created ACL removal expect script for interface $interfaceName");
  
  return $aclRemovalScript;
}

// Function to unconfigure switch interface by removing IP address
function unconfigureSwitchInterface($pdo, $serverId, $serverType, $ipAddress, $subnetInfo = null) {
  try {
    error_log("Starting switch interface unconfiguration for server $serverId, type $serverType, IP $ipAddress");
    
    // Determine the server table based on the passed server type parameter
    $serverTable = ($serverType === 'dedicated') ? 'inventory_dedicated_servers' : 'blade_server_inventory';
    
    // Get server and switch info
    $getServerSql = "SELECT s.*, sw.switch_ip, sw.snmp_community, sw.snmp_version, sw.root_password, s.label
                     FROM $serverTable s
                     LEFT JOIN inventory_switches sw ON s.switch_id = sw.id
                     WHERE s.id = :server_id";
    $getServerSth = $pdo->prepare($getServerSql);
    $getServerSth->bindValue(':server_id', $serverId);
    $getServerSth->execute();
    
    if ($getServerSth->rowCount() === 0) {
      error_log("Server $serverId not found");
      return [
        'success' => false,
        'error' => 'Server not found',
        'unconfigured' => false
      ];
    }
    
    $server = $getServerSth->fetch(PDO::FETCH_ASSOC);
    
    // Check if server has switch information
    if (empty($server['switch_id']) || empty($server['switch_ip'])) {
      error_log("Server $serverId has no switch configured");
      return [
        'success' => false,
        'error' => 'Server has no switch configured',
        'unconfigured' => false
      ];
    }
    
    // Check if server has port information
    if (empty($server['port1'])) {
      error_log("Server $serverId has no port configured");
      return [
        'success' => false,
        'error' => 'Server has no port configured',
        'unconfigured' => false
      ];
    }
    
    // Resolve port name if it's a numeric port ID
    $serverPort = $server['port1'];
    if (is_numeric($serverPort)) {
      $getPortSql = "SELECT port_name, port_number FROM inventory_switch_ports WHERE id = :port_id AND switch_id = :switch_id";
      $getPortSth = $pdo->prepare($getPortSql);
      $getPortSth->bindValue(':port_id', $serverPort);
      $getPortSth->bindValue(':switch_id', $server['switch_id']);
      $getPortSth->execute();
      
      if ($getPortSth->rowCount() > 0) {
        $portInfo = $getPortSth->fetch(PDO::FETCH_ASSOC);
        // Use port_number (like "Ethernet35") for configuration, fallback to port_name
        $serverPort = !empty($portInfo['port_number']) ? $portInfo['port_number'] : $portInfo['port_name'];
        error_log("Resolved port ID {$server['port1']} to port interface: $serverPort");
      } else {
        error_log("Could not resolve port ID {$server['port1']} for server $serverId");
        return [
          'success' => false,
          'error' => 'Could not resolve server port configuration',
          'unconfigured' => false
        ];
      }
    }
    
    // Get switch credentials
    $switchIp = $server['switch_ip'];
    $switchPassword = $server['root_password'];
    
    error_log("Unconfiguring switch $switchIp port $serverPort by removing IP $ipAddress via SSH");
    
    // Queue the switch unconfiguration operation to prevent race conditions
    $operationId = queueSwitchOperation('unconfigure', $switchIp, $serverPort, [
      'password' => $switchPassword,
      'ip_address' => $ipAddress,
      'subnet_info' => $subnetInfo,
      'server_label' => $server['label'] ?? null
    ]);
    
    if ($operationId) {
      error_log("Switch unconfiguration queued for server $serverId (Operation ID: $operationId)");
      
      // Process the queue immediately for this manual unconfiguration request
      processSwitchQueue($pdo);
      
      // Return success for queueing
      $result = ['success' => true, 'queued' => true, 'operation_id' => $operationId];
    } else {
      error_log("Failed to queue switch unconfiguration for server $serverId");
      $result = ['success' => false, 'error' => 'Failed to queue operation'];
    }
    
    if ($result['success']) {
      error_log("Successfully unconfigured switch interface for server $serverId via SSH");
      return [
        'success' => true,
        'message' => "Switch interface unconfigured successfully on port $serverPort - IP $ipAddress removed",
        'unconfigured' => true,
        'switch_ip' => $switchIp,
        'port' => $serverPort,
        'removed_ip' => $ipAddress
      ];
    } else {
      // Return the error details for better debugging
      error_log("SSH switch interface unconfiguration failed for server $serverId: " . $result['error']);
      return [
        'success' => false,
        'error' => "Failed to unconfigure switch interface via SSH: " . $result['error'],
        'unconfigured' => false,
        'switch_ip' => $switchIp,
        'port' => $serverPort,
        'attempted_ip' => $ipAddress,
        'manual_unconfig_needed' => true
      ];
    }
    
  } catch (Exception $e) {
    error_log("Error unconfiguring switch interface: " . $e->getMessage());
    return [
      'success' => false,
      'error' => $e->getMessage(),
      'unconfigured' => false
    ];
  }
}

function unconfigureSwitchViaSsh($switchIp, $password, $portNumber, $ipAddress, $subnetInfo = null, $serverLabel = null) {
  try {
    error_log("=== Starting Switch Unconfiguration ===");
    error_log("Switch IP: $switchIp");
    error_log("Port: $portNumber");
    error_log("Subnet info for unconfiguration: " . print_r($subnetInfo, true));
    
    // Check if SSH2 extension is available
    if (!extension_loaded('ssh2')) {
      error_log("SSH2 extension not available");
      return ['success' => false, 'error' => 'SSH2 extension not available'];
    }
    
    // Connect via SSH
    $connection = @ssh2_connect($switchIp, 22);
    if (!$connection) {
      error_log("Could not connect to switch");
      return ['success' => false, 'error' => 'Could not connect to switch'];
    }
    
    // Authenticate
    if (!@ssh2_auth_password($connection, 'admin', $password)) {
      error_log("SSH authentication failed");
      return ['success' => false, 'error' => 'SSH authentication failed'];
    }
    
    // Open shell session
    $shell = @ssh2_shell($connection, 'vt102');
    if (!$shell) {
      error_log("Could not open SSH shell");
      return ['success' => false, 'error' => 'Could not open SSH shell'];
    }
    
    // Check if we should reconfigure with remaining subnets
    $reconfigureWithRemaining = false;
    $remainingSubnets = [];
    
    if (is_array($subnetInfo) && !empty($subnetInfo)) {
      $reconfigureWithRemaining = true;
      $remainingSubnets = $subnetInfo;
      error_log("Will reconfigure with " . count($remainingSubnets) . " remaining subnet(s)");
    }
    
    // Start building commands
    $commands = [
      "enable",
      "configure terminal", 
      "interface $portNumber",
      "description " . ($serverLabel ?? 'API') . " " . date('Y-m-d H:i:s'),
      "no ip address"  // Clear all existing IPs
    ];
    
    if ($reconfigureWithRemaining) {
      // Reconfigure with remaining subnets
      $ipIndex = 0;
      foreach ($remainingSubnets as $subnet) {
        if (empty($subnet) || strpos($subnet, '/') === false) continue;
        
        list($network, $prefix) = explode('/', $subnet);
        $gatewayIp = long2ip(ip2long($network) + 1);
        
        if ($ipIndex === 0) {
          $commands[] = "ip address $gatewayIp/$prefix";
        } else {
          $commands[] = "ip address $gatewayIp/$prefix secondary";
        }
        $ipIndex++;
      }
      
      $commands[] = "no shutdown"; // Keep interface up
    } else {
      // Complete removal
      $commands[] = "shutdown"; // Shutdown the interface
    }
    
    $commands[] = "exit";
    $commands[] = "exit";
    $commands[] = "write memory";
    
    // Handle ACL removal/reconfiguration
    $interfaceName = str_replace('interface ', '', $portNumber);
    $aclName = $interfaceName;
    
    // Remove existing ACL
    $commands[] = "configure terminal";
    $commands[] = "interface $interfaceName";
    $commands[] = "no ip access-group $aclName in";
    $commands[] = "exit";
    $commands[] = "no ip access-list $aclName";
    
    // If reconfiguring, create new ACL for remaining subnets
    if ($reconfigureWithRemaining && !empty($remainingSubnets)) {
      $commands[] = "ip access-list $aclName";
      $seq = 1;
      foreach ($remainingSubnets as $subnet) {
        if (!empty($subnet) && strpos($subnet, '/') !== false) {
          $commands[] = "$seq permit ip $subnet any";
          $seq++;
        }
      }
      $commands[] = "999 deny ip any any";
      $commands[] = "exit";
      $commands[] = "interface $interfaceName";
      $commands[] = "ip access-group $aclName in";
      $commands[] = "exit";
    }
    
    $commands[] = "exit";
    $commands[] = "write memory";
    $commands[] = "exit";
    
    error_log("Unconfiguration commands: " . implode(" ; ", $commands));
    
    // Execute commands
    $output = '';
    foreach ($commands as $cmd) {
      fwrite($shell, $cmd . "\n");
      usleep(300000); // 300ms delay
      
      // Read output
      $buffer = '';
      $start = time();
      while ((time() - $start) < 1) {
        $data = fread($shell, 4096);
        if ($data !== false) {
          $buffer .= $data;
        }
        if (strpos($buffer, '#') !== false || strpos($buffer, '>') !== false) {
          break;
        }
        usleep(50000);
      }
      
      $output .= "CMD: $cmd\n$buffer\n";
    }
    
    fclose($shell);
    ssh2_disconnect($connection);
    
    error_log("Switch unconfiguration completed successfully");
    return [
      'success' => true,
      'message' => $reconfigureWithRemaining 
        ? "Interface reconfigured with " . count($remainingSubnets) . " remaining subnet(s)"
        : "Interface unconfigured - all IPs removed",
      'output' => $output
    ];
    
  } catch (Exception $e) {
    error_log("Exception in switch unconfiguration: " . $e->getMessage());
    return ['success' => false, 'error' => $e->getMessage()];
  }
}

// Switch operation queue functions to prevent race conditions
function acquireSwitchLock($switchIp, $port, $timeout = 30, $pdoConnection = null) {
  global $pdo;
  
  // Use provided PDO connection or global one
  $db = $pdoConnection ?? $pdo;
  
  try {
    // Ensure the switch_locks table exists
    $createLockTableSql = "CREATE TABLE IF NOT EXISTS switch_locks (
      switch_ip VARCHAR(45) NOT NULL,
      port VARCHAR(50) NOT NULL,
      process_id INT NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (switch_ip, port),
      INDEX idx_created (created_at)
    )";
    $db->exec($createLockTableSql);
    
    $startTime = time();
    $processId = getmypid();
    
    while (time() - $startTime < $timeout) {
      try {
        // Try to insert lock (will fail if lock already exists)
        $insertLockSql = "INSERT INTO switch_locks (switch_ip, port, process_id) 
                         VALUES (:switch_ip, :port, :process_id)";
        $insertLockSth = $db->prepare($insertLockSql);
        $insertLockSth->bindValue(':switch_ip', $switchIp);
        $insertLockSth->bindValue(':port', $port);
        $insertLockSth->bindValue(':process_id', $processId);
        
        if ($insertLockSth->execute()) {
          error_log("Acquired switch lock for $switchIp:$port (PID: $processId)");
          return true;
        }
        
      } catch (Exception $e) {
        // Lock already exists, check if it's stale
        $checkStaleLockSql = "SELECT process_id, created_at FROM switch_locks 
                             WHERE switch_ip = :switch_ip AND port = :port 
                             AND created_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)";
        $checkStaleSth = $db->prepare($checkStaleLockSql);
        $checkStaleSth->bindValue(':switch_ip', $switchIp);
        $checkStaleSth->bindValue(':port', $port);
        $checkStaleSth->execute();
        
        if ($checkStaleSth->rowCount() > 0) {
          // Remove stale lock
          error_log("Removing stale lock for $switchIp:$port");
          $removeStaleLockSql = "DELETE FROM switch_locks 
                               WHERE switch_ip = :switch_ip AND port = :port 
                               AND created_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)";
          $removeStaleSth = $db->prepare($removeStaleLockSql);
          $removeStaleSth->bindValue(':switch_ip', $switchIp);
          $removeStaleSth->bindValue(':port', $port);
          $removeStaleSth->execute();
          continue; // Try to acquire lock again
        }
      }
      
      // Wait 500ms before retrying
      usleep(500000);
    }
    
    error_log("Failed to acquire switch lock for $switchIp:$port after {$timeout}s timeout");
    return false;
    
  } catch (Exception $e) {
    error_log("Error acquiring switch lock for $switchIp:$port: " . $e->getMessage());
    return false;
  }
}

function releaseSwitchLock($switchIp, $port, $pdoConnection = null) {
  global $pdo;
  
  // Use provided PDO connection or global one
  $db = $pdoConnection ?? $pdo;
  
  try {
    $processId = getmypid();
    
    $deleteLockSql = "DELETE FROM switch_locks 
                     WHERE switch_ip = :switch_ip AND port = :port AND process_id = :process_id";
    $deleteLockSth = $db->prepare($deleteLockSql);
    $deleteLockSth->bindValue(':switch_ip', $switchIp);
    $deleteLockSth->bindValue(':port', $port);
    $deleteLockSth->bindValue(':process_id', $processId);
    
    if ($deleteLockSth->execute() && $deleteLockSth->rowCount() > 0) {
      error_log("Released switch lock for $switchIp:$port (PID: $processId)");
    } else {
      error_log("Warning: Could not release switch lock for $switchIp:$port (PID: $processId) - may not have been locked by this process");
    }
    
  } catch (Exception $e) {
    error_log("Error releasing switch lock for $switchIp:$port: " . $e->getMessage());
  }
}

function queueSwitchOperation($operation, $switchIp, $port, $params = [], $pdoConnection = null) {
  global $pdo;
  
  // Use provided PDO connection or global one
  $db = $pdoConnection ?? $pdo;
  
  if (!$db) {
    error_log("No database connection available for queueing switch operation");
    return false;
  }
  
  try {
    // Ensure the automation_in_progres table exists
    $createTableSql = "CREATE TABLE IF NOT EXISTS automation_in_progres (
      id VARCHAR(50) PRIMARY KEY,
      operation_type VARCHAR(20) NOT NULL,
      switch_ip VARCHAR(45) NOT NULL,
      port VARCHAR(50) NOT NULL,
      params TEXT,
      status VARCHAR(20) DEFAULT 'queued',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      started_at TIMESTAMP NULL,
      completed_at TIMESTAMP NULL,
      error_message TEXT NULL,
      INDEX idx_status_created (status, created_at),
      INDEX idx_switch_port (switch_ip, port)
    )";
    $db->exec($createTableSql);
    
    $operationId = uniqid() . '_' . time();
    
    $insertSql = "INSERT INTO automation_in_progres 
                  (id, operation_type, switch_ip, port, params, status) 
                  VALUES (:id, :operation_type, :switch_ip, :port, :params, 'queued')";
    
    $insertSth = $db->prepare($insertSql);
    $insertSth->bindValue(':id', $operationId);
    $insertSth->bindValue(':operation_type', $operation);
    $insertSth->bindValue(':switch_ip', $switchIp);
    $insertSth->bindValue(':port', $port);
    $insertSth->bindValue(':params', json_encode($params));
    
    if ($insertSth->execute()) {
      error_log("✅ QUEUED: Switch operation $operation for $switchIp:$port (ID: $operationId)");
      
      // Check queue count
      $countSql = "SELECT COUNT(*) FROM automation_in_progres WHERE status = 'queued'";
      $count = $db->query($countSql)->fetchColumn();
      error_log("📋 Queue status: $count operations queued");
      
      return $operationId;
    }
    
    error_log("❌ Failed to insert switch operation into database");
    return false;
    
  } catch (Exception $e) {
    error_log("❌ Error queueing switch operation: " . $e->getMessage());
    return false;
  }
}

function processSwitchQueue($pdoConnection = null) {
  global $pdo;
  
  // Use provided PDO connection or global one
  $db = $pdoConnection ?? $pdo;
  
  if (!$db) {
    error_log("❌ No database connection available for processing switch queue");
    return;
  }
  
  try {
    error_log("🔄 Starting switch queue processing...");
    
    // Get queued operations, oldest first
    $selectSql = "SELECT * FROM automation_in_progres 
                  WHERE status = 'queued' 
                  ORDER BY created_at ASC";
    $selectSth = $db->prepare($selectSql);
    $selectSth->execute();
    
    $operations = $selectSth->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($operations)) {
      error_log("📭 Queue is empty, nothing to process");
      return;
    }
    
    error_log("📋 Found " . count($operations) . " queued operations");
    
    $processed = 0;
    $skipped = 0;
    
    foreach ($operations as $operationData) {
      $switchIp = $operationData['switch_ip'];
      $port = $operationData['port'];
      $operation = $operationData['operation_type'];
      $params = json_decode($operationData['params'], true);
      $operationId = $operationData['id'];
      
      error_log("🔍 Attempting to process: $operationId ($operation for $switchIp:$port)");
      error_log("📋 Params: " . json_encode($params));
      
      // Try to acquire lock
      if (!acquireSwitchLock($switchIp, $port, 5, $db)) {
        error_log("🔒 Lock unavailable for $switchIp:$port, skipping operation $operationId");
        $skipped++;
        continue;
      }
      
      try {
        error_log("⚡ PROCESSING: $operationId - $operation for $switchIp:$port");
        
        // Mark as processing
        $updateStatusSql = "UPDATE automation_in_progres 
                           SET status = 'processing', started_at = NOW() 
                           WHERE id = :id";
        $updateStatusSth = $db->prepare($updateStatusSql);
        $updateStatusSth->bindValue(':id', $operationId);
        $updateStatusSth->execute();
        
        $result = null;
        
        switch ($operation) {
          case 'configure':
            error_log("🔧 Executing configure operation for $switchIp:$port");
            
            // Extract parameters
            $password = $params['password'] ?? '';
            $ipAddress = $params['ip_address'] ?? '';
            $subnetInfo = $params['subnet_info'] ?? null;
            $serverLabel = $params['server_label'] ?? null;
            
            // Log what we're passing
            error_log("Configure params - IP: $ipAddress, Subnets: " . print_r($subnetInfo, true));
            
            $result = configureSwitchViaSsh(
              $switchIp,
              $password,
              $port,
              $ipAddress,
              $subnetInfo,
              $serverLabel
            );
            break;
            
          case 'unconfigure':
            error_log("🗑️ Executing unconfigure operation for $switchIp:$port");
            
            // Extract parameters
            $password = $params['password'] ?? '';
            $ipAddress = $params['ip_address'] ?? null;
            $subnetInfo = $params['subnet_info'] ?? null;
            $serverLabel = $params['server_label'] ?? null;
            
            $result = unconfigureSwitchViaSsh(
              $switchIp,
              $password,
              $port,
              $ipAddress,
              $subnetInfo,
              $serverLabel
            );
            break;
            
          default:
            error_log("❌ Unknown switch operation: $operation");
            $result = ['success' => false, 'error' => 'Unknown operation'];
        }
        
        // Update operation status
        if ($result && $result['success']) {
          error_log("✅ SUCCESS: Switch operation $operationId completed successfully");
          $completeSql = "UPDATE automation_in_progres 
                         SET status = 'completed', completed_at = NOW() 
                         WHERE id = :id";
          $completeSth = $db->prepare($completeSql);
          $completeSth->bindValue(':id', $operationId);
          $completeSth->execute();
          $processed++;
        } else {
          $errorMsg = $result['error'] ?? 'Unknown error';
          error_log("❌ FAILED: Switch operation $operationId failed: $errorMsg");
          $errorSql = "UPDATE automation_in_progres 
                      SET status = 'failed', completed_at = NOW(), error_message = :error 
                      WHERE id = :id";
          $errorSth = $db->prepare($errorSql);
          $errorSth->bindValue(':id', $operationId);
          $errorSth->bindValue(':error', $errorMsg);
          $errorSth->execute();
        }
        
      } catch (Exception $e) {
        error_log("💥 EXCEPTION: Switch operation $operationId failed with exception: " . $e->getMessage());
        
        // Mark as failed
        $failSql = "UPDATE automation_in_progres 
                   SET status = 'failed', completed_at = NOW(), error_message = :error 
                   WHERE id = :id";
        $failSth = $db->prepare($failSql);
        $failSth->bindValue(':id', $operationId);
        $failSth->bindValue(':error', $e->getMessage());
        $failSth->execute();
        
      } finally {
        releaseSwitchLock($switchIp, $port, $db);
        error_log("🔓 Released lock for $switchIp:$port");
      }
    }
    
    error_log("📊 Queue processing complete: $processed processed, $skipped skipped");
    
    // If we processed any operations, check if there are more queued operations to process
    if ($processed > 0) {
      $checkMoreSql = "SELECT COUNT(*) FROM automation_in_progres WHERE status = 'queued'";
      $moreQueued = $db->query($checkMoreSql)->fetchColumn();
      
      if ($moreQueued > 0) {
        error_log("🔄 Found $moreQueued more queued operations, continuing processing...");
        // Recursively process more operations (but with a limit to prevent infinite loops)
        static $recursionDepth = 0;
        if ($recursionDepth < 10) {
          $recursionDepth++;
          processSwitchQueue($db);
          $recursionDepth--;
        } else {
          error_log("⚠️ Maximum recursion depth reached, stopping queue processing");
        }
      }
    }
    
    // Clean up old completed/failed operations (older than 24 hours)
    $cleanupSql = "DELETE FROM automation_in_progres 
                   WHERE status IN ('completed', 'failed') 
                   AND completed_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)";
    $cleanupResult = $db->exec($cleanupSql);
    if ($cleanupResult > 0) {
      error_log("🗑️ Cleaned up $cleanupResult old operations");
    }
    
    // Clean up old stale locks (older than 10 minutes)
    $cleanupLocksSql = "DELETE FROM switch_locks 
                       WHERE created_at < DATE_SUB(NOW(), INTERVAL 10 MINUTE)";
    $lockCleanupResult = $db->exec($cleanupLocksSql);
    if ($lockCleanupResult > 0) {
      error_log("🗑️ Cleaned up $lockCleanupResult stale locks");
    }
    
  } catch (Exception $e) {
    error_log("💥 ERROR processing switch queue: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
  }
}

// Function to manually restart stuck queue processing
function restartSwitchQueue($pdoConnection = null) {
  global $pdo;
  
  try {
    $db = $pdoConnection ?? $pdo;
    
    error_log("🔧 Manual queue restart requested");
    
    // Reset any operations stuck in 'processing' status for more than 5 minutes back to 'queued'
    $resetSql = "UPDATE automation_in_progres 
                 SET status = 'queued', 
                     started_at = NULL,
                     updated_at = NOW()
                 WHERE status = 'processing' 
                 AND started_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)";
    $resetResult = $db->exec($resetSql);
    if ($resetResult > 0) {
      error_log("🔄 Reset $resetResult stuck operations back to queued");
    }
    
    // Clean up any stale locks
    $cleanupLocksSql = "DELETE FROM switch_locks 
                       WHERE created_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)";
    $lockCleanupResult = $db->exec($cleanupLocksSql);
    if ($lockCleanupResult > 0) {
      error_log("🗑️ Cleaned up $lockCleanupResult stale locks");
    }
    
    // Start processing the queue
    processSwitchQueue($db);
    
    error_log("🚀 Queue restart completed");
    
  } catch (Exception $e) {
    error_log("💥 ERROR restarting queue: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
  }
}

// Add new API endpoint to delete an unused IP address
if($_GET['f'] == 'delete_ip'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['ip_address']) || empty($data['ip_address'])) {
      throw new Exception('IP address is required');
    }

    $ipAddress = trim($data['ip_address']);

    // Begin transaction
    $pdo->beginTransaction();

    // Fetch the IP row
    $fetchSql = "SELECT id, is_used FROM ip_addresses WHERE ip_address = :ip";
    $fetchSt = $pdo->prepare($fetchSql);
    $fetchSt->bindValue(':ip', $ipAddress);
    $fetchSt->execute();

    if ($fetchSt->rowCount() === 0) {
      // Nothing to delete
      $pdo->commit();
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'IP address not found, nothing to delete'
      ]);
      exit;
    }

    $ipData = $fetchSt->fetch(PDO::FETCH_ASSOC);

    if ($ipData['is_used'] == 1) {
      $pdo->rollBack();
      throw new Exception('Cannot delete: IP address is currently allocated');
    }

    // Delete the row
    $deleteSql = "DELETE FROM ip_addresses WHERE id = :id";
    $delSt = $pdo->prepare($deleteSql);
    $delSt->bindValue(':id', $ipData['id']);
    $delSt->execute();

    $pdo->commit();

    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'IP address deleted successfully',
      'ip_address' => $ipAddress
    ]);

  } catch (Exception $e) {
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log('Error in delete_ip: ' . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to delete IP address: ' . $e->getMessage()
    ]);
  }
}

// API endpoint to force allocate (or create and allocate) an IP address when the normal allocate_ip fails
elseif($_GET['f'] == 'force_allocate_ip'){
  try {
    $admin_id = auth_admin();
    $data = json_decode(file_get_contents('php://input'), true);

    if (empty($data['ip_address'])) {
      throw new Exception('ip_address is required');
    }

    $ipAddress = trim($data['ip_address']);

    // Helper: check if an IPv4 address belongs to subnet CIDR
    $ipInSubnet = function(string $ip, string $cidr): bool {
      if (strpos($cidr, '/') === false) return false;
      [$subnetIp, $prefix] = explode('/', $cidr);
      $ipLong = ip2long($ip);
      $subnetLong = ip2long($subnetIp);
      $mask = -1 << (32 - (int)$prefix);
      return ($ipLong & $mask) === ($subnetLong & $mask);
    };

    // Attempt to locate subnet containing this IP
    $subnetSql = 'SELECT id, subnet FROM subnets';
    $subnets = $pdo->query($subnetSql)->fetchAll(PDO::FETCH_ASSOC);
    $foundSubnetId = null;
    foreach ($subnets as $sn) {
      if ($ipInSubnet($ipAddress, $sn['subnet'])) {
        $foundSubnetId = (int)$sn['id'];
        break;
      }
    }

    if ($foundSubnetId === null) {
      throw new Exception('No subnet found that contains the given IP');
    }

    // Build device_type for logging
    $deviceTypeParam = null;
    if(!empty($data['for_server_ipmi'])){
      $deviceTypeParam = !empty($data['server_type']) ? $data['server_type'] : 'server_ipmi';
    } elseif(!empty($data['for_switch'])){
      $deviceTypeParam = 'switch';
    }

    $pdo->beginTransaction();

    // Check if row exists
    $checkSql = 'SELECT id FROM ip_addresses WHERE ip_address = :ip';
    $checkSt = $pdo->prepare($checkSql);
    $checkSt->bindValue(':ip', $ipAddress);
    $checkSt->execute();

    if ($checkSt->rowCount() > 0) {
      // Update existing row
      $upd = 'UPDATE ip_addresses SET subnet_id = :subnet, is_used = 1, assigned_to = :assigned, device_type = :dtype, notes = :notes WHERE ip_address = :ip';
      $u = $pdo->prepare($upd);
      $u->bindValue(':subnet', $foundSubnetId, PDO::PARAM_INT);
      $u->bindValue(':assigned', $data['manual_alocation'] ?? null);
      if ($deviceTypeParam === null) {
        $u->bindValue(':dtype', null, PDO::PARAM_NULL);
      } else {
        $u->bindValue(':dtype', $deviceTypeParam);
      }
      $u->bindValue(':notes', $data['notes'] ?? null);
      $u->bindValue(':ip', $ipAddress);
      $u->execute();
    } else {
      // Insert
      $ins = 'INSERT INTO ip_addresses (subnet_id, ip_address, is_used, assigned_to, device_type, notes) VALUES (:subnet, :ip, 1, :assigned, :dtype, :notes)';
      $i = $pdo->prepare($ins);
      $i->bindValue(':subnet', $foundSubnetId, PDO::PARAM_INT);
      $i->bindValue(':ip', $ipAddress);
      $i->bindValue(':assigned', $data['manual_alocation'] ?? null);
      if ($deviceTypeParam === null) {
        $i->bindValue(':dtype', null, PDO::PARAM_NULL);
      } else {
        $i->bindValue(':dtype', $deviceTypeParam);
      }
      $i->bindValue(':notes', $data['notes'] ?? null);
      $i->execute();
    }

    $pdo->commit();

    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'subnet_id' => $foundSubnetId]);
  } catch (Exception $e) {
    if(isset($pdo) && $pdo->inTransaction()) $pdo->rollBack();
    error_log('Error in force_allocate_ip: '.$e->getMessage());
    header('Content-Type: application/json');
    http_response_code(400);
    echo json_encode(['success'=>false,'error'=>$e->getMessage()]);
  }
}

?>