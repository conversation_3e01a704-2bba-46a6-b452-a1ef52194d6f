<?php
/**
 * Debug Script for PXE Reinstall Switch Configuration
 * 
 * This script helps debug why IP helper-address isn't being configured
 * during PXE reinstall operations.
 */

require_once 'mysql.php';
require_once 'api_admin_subnets.php';

function debugPXESwitchConfig() {
    global $pdo;
    
    echo "=== Debugging PXE Reinstall Switch Configuration ===\n";
    
    // Step 1: Check IP helper-address setting
    echo "\n1. Checking IP helper-address configuration...\n";
    
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM general_settings WHERE setting_key = 'ip_helper_address' LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result && !empty($result['setting_value'])) {
            echo "✅ IP helper-address: " . $result['setting_value'] . "\n";
            $ipHelperAddress = $result['setting_value'];
        } else {
            echo "❌ IP helper-address not found in general_settings\n";
            return false;
        }
    } catch (Exception $e) {
        echo "❌ Error checking IP helper-address: " . $e->getMessage() . "\n";
        return false;
    }
    
    // Step 2: Find a server that can be used for testing
    echo "\n2. Finding servers suitable for PXE reinstall...\n";
    
    try {
        // Check dedicated servers
        $stmt = $pdo->prepare("
            SELECT s.id, s.label, s.main_ip, s.additional_ips, s.switch_id, s.port1,
                   sw.switch_ip, sw.root_password, sw.label as switch_label
            FROM inventory_dedicated_servers s
            LEFT JOIN inventory_switches sw ON s.switch_id = sw.id
            WHERE s.main_ip IS NOT NULL 
            AND s.switch_id IS NOT NULL 
            AND s.port1 IS NOT NULL 
            AND sw.switch_ip IS NOT NULL
            AND sw.root_password IS NOT NULL
            LIMIT 5
        ");
        $stmt->execute();
        $dedicatedServers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Check blade servers
        $stmt = $pdo->prepare("
            SELECT s.id, s.label, s.main_ip, s.additional_ips, s.switch_id, s.port1,
                   sw.switch_ip, sw.root_password, sw.label as switch_label
            FROM blade_server_inventory s
            LEFT JOIN inventory_switches sw ON s.switch_id = sw.id
            WHERE s.main_ip IS NOT NULL 
            AND s.switch_id IS NOT NULL 
            AND s.port1 IS NOT NULL 
            AND sw.switch_ip IS NOT NULL
            AND sw.root_password IS NOT NULL
            LIMIT 5
        ");
        $stmt->execute();
        $bladeServers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $allServers = array_merge($dedicatedServers, $bladeServers);
        
        if (empty($allServers)) {
            echo "❌ No servers found with complete switch configuration\n";
            echo "   Required: main_ip, switch_id, port1, switch with switch_ip and root_password\n";
            return false;
        }
        
        echo "✅ Found " . count($allServers) . " servers suitable for PXE reinstall:\n";
        foreach ($allServers as $server) {
            $serverType = isset($server['chassis_id']) ? 'blade' : 'dedicated';
            echo "   - Server {$server['id']} ({$server['label']}) - {$serverType}\n";
            echo "     Main IP: {$server['main_ip']}\n";
            echo "     Switch: {$server['switch_label']} ({$server['switch_ip']}) Port: {$server['port1']}\n";
            if (!empty($server['additional_ips'])) {
                echo "     Additional IPs: {$server['additional_ips']}\n";
            }
        }
        
        // Use the first server for testing
        $testServer = $allServers[0];
        $serverType = isset($testServer['chassis_id']) ? 'blade' : 'dedicated';
        
    } catch (Exception $e) {
        echo "❌ Error finding servers: " . $e->getMessage() . "\n";
        return false;
    }
    
    // Step 3: Simulate what happens during PXE reinstall
    echo "\n3. Simulating PXE reinstall switch configuration...\n";
    
    try {
        echo "Using test server: {$testServer['id']} ({$testServer['label']})\n";
        
        // Get all subnets for this server (like the PXE reinstall code does)
        $all_subnets = [];
        
        // Add main IP if exists
        if (!empty($testServer['main_ip'])) {
            $all_subnets[] = $testServer['main_ip'];
        }
        
        // Add additional IPs if exist
        if (!empty($testServer['additional_ips'])) {
            $additional_ips = array_filter(array_map('trim', explode(',', $testServer['additional_ips'])));
            $all_subnets = array_merge($all_subnets, $additional_ips);
        }
        
        if (empty($all_subnets)) {
            echo "❌ No subnets found for server {$testServer['id']}\n";
            return false;
        }
        
        echo "Server subnets: " . implode(', ', $all_subnets) . "\n";
        
        // Calculate gateway IP from first subnet (like PXE reinstall code does)
        $primary_subnet = $all_subnets[0];
        $gateway_ip = $primary_subnet;
        if (strpos($primary_subnet, '/') !== false) {
            list($net_ip, $prefix) = explode('/', $primary_subnet);
            $gateway_ip = long2ip(ip2long($net_ip) + 1);
        }
        
        echo "Calculated gateway IP: $gateway_ip\n";

        // Resolve port name like the actual code does
        $server_port = $testServer['port1'];
        $resolved_port_number = $server_port;
        if (is_numeric($server_port)) {
            $get_port_stmt = $pdo->prepare("SELECT port_number, port_number FROM inventory_switch_ports WHERE id = :port_id AND switch_id = :switch_id");
            $get_port_stmt->bindValue(':port_id', $server_port);
            $get_port_stmt->bindValue(':switch_id', $testServer['switch_id']);
            $get_port_stmt->execute();

            if ($get_port_stmt->rowCount() > 0) {
                $port_info = $get_port_stmt->fetch(PDO::FETCH_ASSOC);
                $resolved_port_number = !empty($port_info['port_number']) ? $port_info['port_number'] : $port_info['port_number'];
                echo "Port resolution: ID {$testServer['port1']} -> {$resolved_port_number}\n";
            } else {
                echo "Port resolution: Could not resolve port ID {$testServer['port1']}\n";
            }
        }

        // Show what switch commands would be generated
        echo "\nSwitch commands that would be generated:\n";
        echo "Switch IP: {$testServer['switch_ip']}\n";
        echo "Port ID: {$testServer['port1']} -> Port Name: $resolved_port_number\n";
        echo "Commands:\n";
        echo "  enable\n";
        echo "  configure terminal\n";
        echo "  interface $resolved_port_number\n";
        echo "  no shutdown\n";
        echo "  description {$testServer['label']} " . date('Y-m-d H:i:s') . "\n";
        echo "  no switchport\n";
        echo "  no ip address\n";
        
        // Show IP addresses for each subnet
        foreach ($all_subnets as $index => $subnet) {
            if (strpos($subnet, '/') !== false) {
                list($net_ip, $prefix) = explode('/', $subnet);
                $subnet_gateway = long2ip(ip2long($net_ip) + 1);
                $cmd = "  ip address $subnet_gateway/$prefix";
                if ($index > 0) {
                    $cmd .= " secondary";
                }
                echo "$cmd\n";
            }
        }
        
        echo "  ip helper-address $ipHelperAddress\n";
        echo "  no shutdown\n";
        echo "  exit\n";
        echo "  exit\n";
        echo "  write memory\n";
        
    } catch (Exception $e) {
        echo "❌ Error simulating switch configuration: " . $e->getMessage() . "\n";
        return false;
    }
    
    // Step 4: Check if switch configuration functions are working
    echo "\n4. Testing switch configuration functions...\n";
    
    try {
        // Test queueSwitchOperation function
        if (!function_exists('queueSwitchOperation')) {
            echo "❌ queueSwitchOperation function not found\n";
            return false;
        }
        
        if (!function_exists('processSwitchQueue')) {
            echo "❌ processSwitchQueue function not found\n";
            return false;
        }
        
        echo "✅ Switch configuration functions are available\n";
        
        // Check automation_in_progres table
        $stmt = $pdo->prepare("SHOW TABLES LIKE 'automation_in_progres'");
        $stmt->execute();
        if ($stmt->rowCount() == 0) {
            echo "❌ automation_in_progres table not found\n";
            return false;
        }
        
        echo "✅ automation_in_progres table exists\n";
        
        // Check recent switch operations
        $stmt = $pdo->prepare("
            SELECT * FROM automation_in_progres 
            WHERE operation_type = 'configure' 
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $stmt->execute();
        $recentOps = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($recentOps)) {
            echo "Recent switch configuration operations:\n";
            foreach ($recentOps as $op) {
                echo "  - ID: {$op['id']}, Switch: {$op['switch_ip']}, Port: {$op['port']}, Status: {$op['status']}\n";
                echo "    Created: {$op['created_at']}\n";
                if (!empty($op['error_message'])) {
                    echo "    Error: {$op['error_message']}\n";
                }
            }
        } else {
            echo "No recent switch configuration operations found\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error testing switch functions: " . $e->getMessage() . "\n";
        return false;
    }
    
    echo "\n=== Debug Summary ===\n";
    echo "✅ IP helper-address is configured: $ipHelperAddress\n";
    echo "✅ Suitable servers found for testing\n";
    echo "✅ Switch configuration functions are available\n";
    echo "✅ PXE reinstall should configure IP helper-address correctly\n";
    
    echo "\nTo test actual PXE reinstall:\n";
    echo "1. Initiate PXE reinstall on server {$testServer['id']}\n";
    echo "2. Check auto.logs for switch configuration messages\n";
    echo "3. Check automation_in_progres table for queued operations\n";
    echo "4. Verify switch interface has IP helper-address configured\n";
    
    return true;
}

// Run the debug
if (php_sapi_name() === 'cli') {
    // Running from command line
    $success = debugPXESwitchConfig();
    exit($success ? 0 : 1);
} else {
    // Running from web browser
    header('Content-Type: text/plain');
    debugPXESwitchConfig();
}
?>
