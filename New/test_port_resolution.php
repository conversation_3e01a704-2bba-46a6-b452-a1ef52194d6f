<?php
/**
 * Test Port Resolution
 * 
 * This script tests the port resolution logic to see if port IDs
 * are being correctly resolved to port names.
 */

require_once 'mysql.php';

function testPortResolution() {
    global $pdo;
    
    echo "=== Testing Port Resolution ===\n";
    
    // Find a server with port information
    try {
        $stmt = $pdo->prepare("
            SELECT s.id, s.label, s.switch_id, s.port1,
                   sw.switch_ip, sw.label as switch_label
            FROM inventory_dedicated_servers s
            LEFT JOIN inventory_switches sw ON s.switch_id = sw.id
            WHERE s.switch_id IS NOT NULL 
            AND s.port1 IS NOT NULL 
            AND sw.switch_ip IS NOT NULL
            LIMIT 1
        ");
        $stmt->execute();
        $server = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$server) {
            echo "❌ No suitable server found for testing\n";
            return false;
        }
        
        echo "Test server: {$server['id']} ({$server['label']})\n";
        echo "Switch: {$server['switch_label']} ({$server['switch_ip']})\n";
        echo "Port ID: {$server['port1']}\n";
        
        // Test port resolution
        $server_port = $server['port1'];
        echo "\nOriginal port value: $server_port\n";
        
        if (is_numeric($server_port)) {
            echo "Port is numeric, attempting resolution...\n";
            
            $get_port_stmt = $pdo->prepare("SELECT port_name, port_number FROM inventory_switch_ports WHERE id = :port_id AND switch_id = :switch_id");
            $get_port_stmt->bindValue(':port_id', $server_port);
            $get_port_stmt->bindValue(':switch_id', $server['switch_id']);
            $get_port_stmt->execute();
            
            if ($get_port_stmt->rowCount() > 0) {
                $port_info = $get_port_stmt->fetch(PDO::FETCH_ASSOC);
                echo "Port info found:\n";
                echo "  port_name: '{$port_info['port_name']}'\n";
                echo "  port_number: '{$port_info['port_number']}'\n";
                
                // Use the correct resolution logic
                $resolved_port = !empty($port_info['port_name']) ? $port_info['port_name'] : $port_info['port_number'];
                echo "Resolved port: '$resolved_port'\n";
                
                // Show what the switch command would look like
                echo "\nSwitch command would be:\n";
                echo "interface $resolved_port\n";
                
                if ($resolved_port === $port_info['port_name']) {
                    echo "✅ Using port_name (correct)\n";
                } else {
                    echo "⚠️ Using port_number (might be wrong)\n";
                }
                
            } else {
                echo "❌ No port info found for port ID $server_port in switch {$server['switch_id']}\n";
                
                // Show what's in the ports table for this switch
                $all_ports_stmt = $pdo->prepare("SELECT id, port_name, port_number FROM inventory_switch_ports WHERE switch_id = :switch_id LIMIT 5");
                $all_ports_stmt->bindValue(':switch_id', $server['switch_id']);
                $all_ports_stmt->execute();
                $all_ports = $all_ports_stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "Available ports for switch {$server['switch_id']}:\n";
                foreach ($all_ports as $port) {
                    echo "  ID: {$port['id']}, Name: '{$port['port_name']}', Number: '{$port['port_number']}'\n";
                }
                
                return false;
            }
        } else {
            echo "Port is not numeric: '$server_port'\n";
            echo "Switch command would be:\n";
            echo "interface $server_port\n";
        }
        
        return true;
        
    } catch (Exception $e) {
        echo "❌ Error testing port resolution: " . $e->getMessage() . "\n";
        return false;
    }
}

// Run the test
if (php_sapi_name() === 'cli') {
    // Running from command line
    $success = testPortResolution();
    exit($success ? 0 : 1);
} else {
    // Running from web browser
    header('Content-Type: text/plain');
    testPortResolution();
}
?>
