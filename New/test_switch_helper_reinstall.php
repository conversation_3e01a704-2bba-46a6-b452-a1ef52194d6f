<?php
/**
 * Test Script for Switch IP Helper-Address Configuration During Reinstall
 * 
 * This script tests that the IP helper-address is properly configured
 * on switch interfaces during server PXE reinstall operations.
 */

require_once 'mysql.php';
require_once 'api_admin_subnets.php';

function testSwitchHelperConfiguration() {
    global $pdo;
    
    echo "=== Testing Switch IP Helper-Address Configuration ===\n";
    
    // Test 1: Check if IP helper-address setting exists in database
    echo "\n1. Checking IP helper-address setting in database...\n";
    
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM general_settings WHERE setting_key = 'ip_helper_address' LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result && !empty($result['setting_value'])) {
            echo "✅ IP helper-address found: " . $result['setting_value'] . "\n";
            $ipHelperAddress = $result['setting_value'];
        } else {
            echo "❌ IP helper-address not found in general_settings table\n";
            echo "   Please add it with: INSERT INTO general_settings (setting_key, setting_value, description) VALUES ('ip_helper_address', '*************', 'The IP Address of the helper');\n";
            return false;
        }
    } catch (Exception $e) {
        echo "❌ Error checking IP helper-address: " . $e->getMessage() . "\n";
        return false;
    }
    
    // Test 2: Check if configureServerSwitchInterface function exists
    echo "\n2. Checking if configureServerSwitchInterface function exists...\n";
    
    if (function_exists('configureServerSwitchInterface')) {
        echo "✅ configureServerSwitchInterface function exists\n";
    } else {
        echo "❌ configureServerSwitchInterface function not found\n";
        return false;
    }
    
    // Test 3: Find a test server to use
    echo "\n3. Finding a test server...\n";
    
    try {
        $stmt = $pdo->prepare("
            SELECT id, label, main_ip, switch_id, port1 
            FROM inventory_dedicated_servers 
            WHERE main_ip IS NOT NULL 
            AND switch_id IS NOT NULL 
            AND port1 IS NOT NULL 
            LIMIT 1
        ");
        $stmt->execute();
        $testServer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($testServer) {
            echo "✅ Found test server: ID {$testServer['id']}, Label: {$testServer['label']}, IP: {$testServer['main_ip']}\n";
            echo "   Switch ID: {$testServer['switch_id']}, Port: {$testServer['port1']}\n";
        } else {
            echo "⚠️  No suitable test server found (need server with main_ip, switch_id, and port1)\n";
            echo "   Testing will be limited to function availability checks\n";
            return true; // Still consider this a success for basic checks
        }
    } catch (Exception $e) {
        echo "❌ Error finding test server: " . $e->getMessage() . "\n";
        return false;
    }
    
    // Test 4: Check switch configuration (dry run)
    echo "\n4. Testing switch configuration function (dry run)...\n";
    
    if ($testServer) {
        try {
            // Note: This would actually configure the switch in a real environment
            // For testing purposes, we'll just check that the function can be called
            echo "   Would call: configureServerSwitchInterface(pdo, {$testServer['id']}, 'dedicated', '{$testServer['main_ip']}')\n";
            echo "   This would configure switch interface with IP helper-address: $ipHelperAddress\n";
            echo "✅ Switch configuration function is ready to use\n";
        } catch (Exception $e) {
            echo "❌ Error testing switch configuration: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    // Test 5: Check PXE reinstall integration
    echo "\n5. Checking PXE reinstall integration...\n";
    
    // Check if the PXE reinstall endpoint exists
    if (file_exists('api_admin_inventory.php')) {
        $content = file_get_contents('api_admin_inventory.php');
        if (strpos($content, 'configureServerSwitchInterface') !== false) {
            echo "✅ PXE reinstall endpoint includes switch configuration\n";
        } else {
            echo "❌ PXE reinstall endpoint missing switch configuration\n";
            return false;
        }
    } else {
        echo "❌ api_admin_inventory.php not found\n";
        return false;
    }
    
    // Check if the PXE API integration includes switch configuration
    if (file_exists('pxe_api_integration.php')) {
        $content = file_get_contents('pxe_api_integration.php');
        if (strpos($content, 'configureServerSwitchInterface') !== false) {
            echo "✅ PXE API integration includes switch configuration\n";
        } else {
            echo "❌ PXE API integration missing switch configuration\n";
            return false;
        }
    } else {
        echo "❌ pxe_api_integration.php not found\n";
        return false;
    }
    
    echo "\n=== Test Results ===\n";
    echo "✅ All tests passed! Switch IP helper-address configuration is properly integrated.\n";
    echo "\nWhat happens during PXE reinstall:\n";
    echo "1. Server PXE reinstall is initiated\n";
    echo "2. Switch interface is configured with IP helper-address: $ipHelperAddress\n";
    echo "3. PXE boot files are created\n";
    echo "4. Server is power-cycled to start network boot\n";
    echo "5. Server boots from network with proper DHCP helper configuration\n";
    
    return true;
}

// Run the test
if (php_sapi_name() === 'cli') {
    // Running from command line
    $success = testSwitchHelperConfiguration();
    exit($success ? 0 : 1);
} else {
    // Running from web browser
    header('Content-Type: text/plain');
    testSwitchHelperConfiguration();
}
?>
