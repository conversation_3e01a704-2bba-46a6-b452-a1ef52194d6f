<?php
/**
 * Simple log viewer for ACL operations
 */

$logFile = 'auto.logs';

echo "=== ACL Operation Logs ===\n\n";

if (!file_exists($logFile)) {
    echo "Log file '$logFile' not found.\n";
    echo "Logs may be written to a different location or no operations have been logged yet.\n";
    exit(1);
}

$logContent = file_get_contents($logFile);

if (empty($logContent)) {
    echo "Log file is empty.\n";
    exit(1);
}

// Get the last 100 lines of the log file
$lines = explode("\n", $logContent);
$totalLines = count($lines);
$startLine = max(0, $totalLines - 100);

echo "Showing last " . min(100, $totalLines) . " lines of $logFile:\n";
echo "Total lines in log: $totalLines\n";
echo str_repeat("=", 80) . "\n";

for ($i = $startLine; $i < $totalLines; $i++) {
    if (!empty(trim($lines[$i]))) {
        echo ($i + 1) . ": " . $lines[$i] . "\n";
    }
}

echo str_repeat("=", 80) . "\n";
echo "End of log\n";

// Also check for recent ACL-related entries
echo "\n=== Recent ACL-related entries ===\n";
$aclLines = [];
foreach ($lines as $lineNum => $line) {
    if (stripos($line, 'acl') !== false || 
        stripos($line, 'switch') !== false || 
        stripos($line, 'ssh') !== false ||
        stripos($line, 'pxe reinstall') !== false) {
        $aclLines[] = ($lineNum + 1) . ": " . $line;
    }
}

if (empty($aclLines)) {
    echo "No ACL-related entries found in logs.\n";
} else {
    // Show last 20 ACL-related entries
    $recentAclLines = array_slice($aclLines, -20);
    foreach ($recentAclLines as $line) {
        echo $line . "\n";
    }
}

echo "\n";
?>
